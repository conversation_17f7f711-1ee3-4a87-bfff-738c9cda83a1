<?php
namespace Swissup\AddressFieldManager\Plugin\Model;

use Magento\Sales\Model\Order;
use Magento\Sales\Model\Order\Email\Sender\OrderSender;

class OrderEmailSender
{
    protected \Swissup\FieldManager\Helper\Data $helper;

    public function __construct(
        \Swissup\FieldManager\Helper\Data $helper
    ) {
        $this->helper = $helper;
    }

    public function beforeSend(OrderSender $subject, Order $order, $forceSyncMode = false)
    {
        $billingAddress = $order->getBillingAddress();
        if ($customAttributesData = $this->helper->fixAttributesValues($billingAddress)) {
            foreach ($customAttributesData as $code => $value) {
                $billingAddress->setData($code, $value);
            }
            $order->setBillingAddress($billingAddress);
        }

        return [$order, $forceSyncMode];
    }
}
