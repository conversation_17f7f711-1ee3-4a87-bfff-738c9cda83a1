<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="before.body.end">
            <block class="Swissup\AddressFieldManager\Block\Init" name="afm.address-renderer">
                <arguments>
                    <argument name="selectors" xsi:type="array">
                        <item name="0" xsi:type="string">.box-shipping-address address</item>
                        <item name="1" xsi:type="string">.box-billing-address address</item>
                    </argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>

