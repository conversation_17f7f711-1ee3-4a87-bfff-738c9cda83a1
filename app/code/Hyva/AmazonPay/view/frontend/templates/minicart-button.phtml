<?php
/**
 * Copyright © Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *  http://aws.amazon.com/apache2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */

/**
 * @var Button $block
 * @var Magento\Framework\Escaper $escaper
 * @var ViewModelRegistry $viewModels
 */

use Amazon\Pay\Block\Minicart\Button;
use Hyva\AmazonPay\ViewModel\AmazonConfig;
use Hyva\AmazonPay\ViewModel\AmazonPayAdapter;
use Hyva\Theme\Model\ViewModelRegistry;

/** @var AmazonConfig $amazonConfig */
$amazonConfig = $viewModels->require(AmazonConfig::class);
$amazonPayAdapter = $viewModels->require(AmazonPayAdapter::class);
?>

<div id="minicart-amazon-pay-button" class="amazon-minicart-container pt-7">
    <div class="amazon-divider">
        <div class="amazon-divider__separator flex items-center whitespace-nowrap whitespace-nowrap font-medium text-lg text-gfpgray"><?= __('Faster to checkout') ?></div>
        <span class="amazon-divider__note flex text-center text-sm text-gfpgray"><?= __('Very simple: Transfer shipping and payment data directly') ?></span>
    </div>

    <div class="amazon-button-container pt-6 pb-6 flex flex-row justify-center gap-x-0.5">
        <div class="amazon-button-container__cell">
            <div id="AmazonPayButton" x-data="amazonInit()" x-spread="eventListeners"></div>
            <script defer src="https://static-eu.payments-amazon.com/checkout.js"></script>
            <script>
                function amazonInit () {
                    <?php
                    $payload = '{"storeId":"'.$amazonConfig->getClientId().'","webCheckoutDetails":{"checkoutReviewReturnUrl":"'.$amazonConfig->getCheckoutReviewReturnUrl().'"}}';
                    $signature = $amazonPayAdapter->signButton($payload);
                    ?>
                    return {
                        amazonPayButton: null,
                        eventListeners: {
                            ['@private-content-loaded.window'](event) {
                                let customerData = event.detail.data;
                                if (customerData.cart && customerData.cart.subtotalAmount){
                                    const grandTotal = customerData.cart.subtotalAmount + parseFloat(customerData.cart.shipping_amount ? customerData.cart.shipping_amount : 0);

                                    this.amazonPayButton = amazon.Pay.renderButton('#AmazonPayButton', {
                                        // set checkout environment
                                        merchantId: '<?= $amazonConfig->getMerchantId() ?>',
                                        publicKeyId: '<?= $amazonConfig->getPublicKeyId() ?>',
                                        ledgerCurrency: '<?= $amazonConfig->getCurrencyCode() ?>',
                                        // customize the buyer experience
                                        checkoutLanguage: '<?= $amazonConfig->getLanguage() ?>',
                                        productType: 'PayAndShip',
                                        placement: 'Cart',
                                        sandbox: <?= $amazonConfig->isSandboxEnabled() ? 'true' : 'false' ?>,
                                        buttonColor: 'Gold',
                                        estimatedOrderAmount: { "amount": grandTotal, "currencyCode": "<?= $amazonConfig->getCurrencyCode() ?>"},
                                        // configure Create Checkout Session request
                                        createCheckoutSessionConfig: {
                                            payloadJSON: '<?= $payload ?>',
                                            signature: '<?= $signature ?>'
                                        }
                                    });
                                }
                            },
                        }
                    };
                }
            </script>
        </div>

        <div class="amazon-button-container__cell align-items pt-1">
            <div class="field-tooltip toggle">
                <div class="text-xs" x-data="initPopper($el, document.querySelector('#amazon-pay-tooltip'))" x-init="init()">
                    <div class="rounded-full border w-5 h-5 flex justify-center items-center ml-1">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="w-2 h-2 text-primary-lighter" viewBox="0 0 192 512"><path d="M160 448h-32V224c0-17.69-14.33-32-32-32L32 192c-17.67 0-32 14.31-32 32s14.33 31.1 32 31.1h32v192H32c-17.67 0-32 14.31-32 32s14.33 32 32 32h128c17.67 0 32-14.31 32-32S177.7 448 160 448zM96 128c26.51 0 48-21.49 48-48S122.5 32.01 96 32.01s-48 21.49-48 48S69.49 128 96 128z"/></svg>
                    </div>
                </div>
                <div class="hidden shadow-lg bg-white text-primary rounded-md px-4 py-2 z-10 max-w-xl" id="amazon-pay-tooltip" data-tooltip>
                    <?= $escaper->escapeHtml(__('Are you an Amazon customer? ' .
                        'Pay now with address and payment details stored in your Amazon account.')); ?>
                    <div data-popper-arrow></div>
                </div>
            </div>
        </div>
    </div>
</div>
