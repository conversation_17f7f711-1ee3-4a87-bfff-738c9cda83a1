/* global module, require */

module.exports = require("BlueFormBuilder_Core/js/jquery/fileUploader/vendor/blueimp-load-image/js/load-image");

require("BlueFormBuilder_Core/js/jquery/fileUploader/vendor/blueimp-load-image/js/load-image-scale");
require("BlueFormBuilder_Core/js/jquery/fileUploader/vendor/blueimp-load-image/js/load-image-meta");
require("BlueFormBuilder_Core/js/jquery/fileUploader/vendor/blueimp-load-image/js/load-image-fetch");
require("BlueFormBuilder_Core/js/jquery/fileUploader/vendor/blueimp-load-image/js/load-image-exif");
require("BlueFormBuilder_Core/js/jquery/fileUploader/vendor/blueimp-load-image/js/load-image-exif-map");
require("BlueFormBuilder_Core/js/jquery/fileUploader/vendor/blueimp-load-image/js/load-image-iptc");
require("BlueFormBuilder_Core/js/jquery/fileUploader/vendor/blueimp-load-image/js/load-image-iptc-map");
require("BlueFormBuilder_Core/js/jquery/fileUploader/vendor/blueimp-load-image/js/load-image-orientation");
