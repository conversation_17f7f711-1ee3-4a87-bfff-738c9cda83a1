<div class="addressfield">
	<div class="subfield addr1field" ng-if="element.show_address1">
		<input class="admin__control-text" placeholder="Street Address" />
	</div>
	<div class="subfield addr2field" ng-if="element.show_address2">
		<input class="admin__control-text" placeholder="Address Line 2" />
	</div>
	<div class="subfield cityfield" ng-if="element.show_city">
		<input class="admin__control-text" placeholder="City" />
	</div>
	<div class="subfield statefield" ng-if="element.show_state">
		<input class="admin__control-text" placeholder="State/Prov/Region" />
	</div>
	<div class="subfield zipfield" ng-if="element.show_zip">
		<input class="admin__control-text" placeholder="Postal/Zip" />
	</div>
	<div class="subfield countryfield" ng-if="element.show_country">
		<select class="admin__control-select">
			<option>USA</option>
		</select>
	</div>
</div>