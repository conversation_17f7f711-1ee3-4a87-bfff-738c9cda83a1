<?xml version="1.0"?>
<!--
/**
 * Magezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license.
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  BlueFormBuilder
 * @package   BlueFormBuilder_Core
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<update handle="styles"/>
	<head>
		<css src="Magezon_Core::css/font-awesome.min.css"/>
	</head>
	<body>
		<referenceContainer name="content">
			<block class="BlueFormBuilder\Core\Block\Adminhtml\Submission\Edit\Tabs" name="blueformbuilder.submission.edit.tabs"/>
			<block class="BlueFormBuilder\Core\Block\Adminhtml\Submission\Edit" name="blueformbuilder.submission.edit"/>
		</referenceContainer>
        <referenceContainer name="main.top">
            <block class="BlueFormBuilder\Core\Block\Adminhtml\TopMenu" name="page.title" template="Magezon_Core::menu.phtml"/>
        </referenceContainer>
	</body>
</page>