<?xml version="1.0"?>
<!--
/**
 * Magezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license.
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  BlueFormBuilder
 * @package   BlueFormBuilder_Core
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <virtualType name="BlueFormBuilder\Core\Ui\Form\Modifier\Pool" type="Magento\Ui\DataProvider\Modifier\Pool">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="common" xsi:type="array">
                    <item name="class" xsi:type="string">BlueFormBuilder\Core\Ui\DataProvider\Form\Modifier\Common</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
                <item name="builder" xsi:type="array">
                    <item name="class" xsi:type="string">BlueFormBuilder\Core\Ui\DataProvider\Form\Modifier\Builder</item>
                    <item name="sortOrder" xsi:type="number">20</item>
                </item>
                <item name="submission" xsi:type="array">
                    <item name="class" xsi:type="string">BlueFormBuilder\Core\Ui\DataProvider\Form\Modifier\Submission</item>
                    <item name="sortOrder" xsi:type="number">30</item>
                </item>
                <item name="styling" xsi:type="array">
                    <item name="class" xsi:type="string">BlueFormBuilder\Core\Ui\DataProvider\Form\Modifier\Styling</item>
                    <item name="sortOrder" xsi:type="number">40</item>
                </item>
                <item name="settings" xsi:type="array">
                    <item name="class" xsi:type="string">BlueFormBuilder\Core\Ui\DataProvider\Form\Modifier\Settings</item>
                    <item name="sortOrder" xsi:type="number">50</item>
                </item>
                <item name="embed" xsi:type="array">
                    <item name="class" xsi:type="string">BlueFormBuilder\Core\Ui\DataProvider\Form\Modifier\Embed</item>
                    <item name="sortOrder" xsi:type="number">60</item>
                </item>
                <item name="customjavascript" xsi:type="array">
                    <item name="class" xsi:type="string">BlueFormBuilder\Core\Ui\DataProvider\Form\Modifier\CustomJavaScript</item>
                    <item name="sortOrder" xsi:type="number">70</item>
                </item>
            </argument>
        </arguments>
    </virtualType>
    <type name="BlueFormBuilder\Core\Ui\DataProvider\Form\FormDataProvider">
        <arguments>
            <argument name="pool" xsi:type="object">BlueFormBuilder\Core\Ui\Form\Modifier\Pool</argument>
        </arguments>
    </type>
</config>