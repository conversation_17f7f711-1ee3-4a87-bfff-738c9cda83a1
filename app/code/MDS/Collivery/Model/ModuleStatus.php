<?php

namespace MDS\Collivery\Model;

trait ModuleStatus
{
    public static $path = 'carriers/collivery/active';
    private $scopeConfig;

    public function __construct(\Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig)
    {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @return bool
     */
    public function isActive()
    {
        return $this->scopeConfig->getValue(self::$path,\Magento\Store\Model\ScopeInterface::SCOPE_STORE ) == 1 ? true : false;
    }
}
