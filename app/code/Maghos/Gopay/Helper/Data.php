<?php
/**
 *
 * Maghos_Gopay Magento 2 extension
 *
 * NOTICE OF LICENSE
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 * @category Maghos
 * @package Maghos_Gopay
 * @copyright Copyright (c) 2017 Maghos s.r.o.
 * @license http://www.maghos.com/business-license
 * <AUTHOR> dev team <<EMAIL>>
 */
namespace Maghos\Gopay\Helper;

use Magento\Sales\Api\Data\OrderInterface;
use Magento\Store\Model\ScopeInterface;
use Maghos\Gopay\Model\Gateway\Payment;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{

    const XML_PATH_GOPAY_CAN_REPEAT_PAYMENT = 'payment/maghos_gopay/repeated_payment';
    const XML_PATH_GOPAY_CAN_CHANGE_PAYMENT = 'payment/maghos_gopay/repeated_payment_change';
    const XML_PATH_GOPAY_DEFAULT_INSTRUMENT = 'payment/maghos_gopay/types/default_instrument';
    const XML_PATH_GOPAY_ALLOWED_INSTRUMENTS = 'payment/maghos_gopay/types/allowedinstruments';
    const XML_PATH_GOPAY_CLIENT_ID = 'payment/maghos_gopay/client_id';
    const XML_PATH_GOPAY_CLIENT_SECRET = 'payment/maghos_gopay/client_secret';
    const XML_PATH_GOPAY_GO_ID = 'payment/maghos_gopay/go_id';
    const XML_PATH_GOPAY_TESTING_MODE = 'payment/maghos_gopay/testing_mode';
    const XML_PATH_GOPAY_ENABLED = 'payment/maghos_gopay/active';
    const XML_PATH_GOPAY_SEND_INVOICE_EMAIL = 'payment/maghos_gopay/send_invoice_email';
    const XML_PATH_GOPAY_SHOW_ICONS = 'payment/maghos_gopay/types/icons';
    const XML_PATH_GOPAY_LINK_ACCOUNT = 'payment/maghos_gopay/links/account';
    const XML_PATH_GOPAY_LINK_EMAIL = 'payment/maghos_gopay/links/order_email';
    const XML_PATH_GOPAY_EMAIL_FAILED = 'payment/maghos_gopay/links/failed_email';
    const XML_PATH_GOPAY_EMAIL_NOTICE = 'payment/maghos_gopay/links/notice_email';
    const XML_PATH_GOPAY_EMAIL_NOTICE_DAYS = 'payment/maghos_gopay/links/notice_email_days';
    const XML_PATH_GOPAY_EMAIL_TEMPLATE_NOTICE = 'payment/maghos_gopay/links/notice_template';
    const XML_PATH_GOPAY_EMAIL_TEMPLATE_FAILED = 'payment/maghos_gopay/links/failed_template';

    const XML_PATH_GOPAY_EET_ENABLED = 'payment/maghos_gopay/eet/enabled';
    const XML_PATH_GOPAY_EET_SHIPPING = 'payment/maghos_gopay/eet/shipping';
    const XML_PATH_GOPAY_EET_TAX_BASE = 'payment/maghos_gopay/eet/base';
    const XML_PATH_GOPAY_EET_TAX_LOWER1 = 'payment/maghos_gopay/eet/lower1';
    const XML_PATH_GOPAY_EET_TAX_LOWER2 = 'payment/maghos_gopay/eet/lower2';

    /** @var \Maghos\Gopay\Model\Source\Instruments */
    private $instruments;

    /** @var \Magento\Store\Model\StoreManagerInterface */
    private $storeManager;

    /** @var \Magento\Framework\Encryption\EncryptorInterface */
    private $encryptor;

    /** @var \Magento\Framework\Serialize\SerializerInterface */
    private $serialize;

    /**
     * Data constructor.
     * @param \Maghos\Gopay\Model\Source\Instruments $instruments
     * @param \Magento\Framework\App\Helper\Context $context
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\Framework\Encryption\EncryptorInterface $encryptor
     * @param \Magento\Framework\Serialize\SerializerInterface $serialize
     */
    public function __construct(
        \Maghos\Gopay\Model\Source\Instruments $instruments,
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\Encryption\EncryptorInterface $encryptor,
        \Magento\Framework\Serialize\SerializerInterface $serialize
    ) {
        $this->instruments  = $instruments;
        $this->storeManager = $storeManager;
        $this->encryptor    = $encryptor;
        $this->serialize    = $serialize;
        parent::__construct($context);
    }

    /**
     * Get language code from order
     *
     * @param \Magento\Sales\Model\Order $order
     * @return string
     */
    public function getOrderLanguage(\Magento\Sales\Model\Order $order)
    {
        $storeCode = $order->getStore()->getCode();
        $locale    = $this->getLocale($storeCode);

        $parts = explode("_", (string)$locale);
        $part  = strtoupper(array_shift($parts));

        $supported = $this->getSupportedLanguages();
        if (in_array($part, $supported)) {
            return $part;
        }

        return 'EN';
    }

    /**
     * Can be payment repeated for order
     *
     * @param string|null $storeCode
     * @return bool
     */
    public function canRepeatPayment($storeCode = null)
    {
        return (bool)$this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_CAN_REPEAT_PAYMENT,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * Get default payment method (instrument) for Gopay Api
     *
     * @param string|null $storeCode
     * @return string | null
     */
    public function getDefaultInstrument($storeCode = null)
    {
        $config = $this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_DEFAULT_INSTRUMENT,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );

        return $config;
    }

    /**
     * Get encrypted URL key
     * @param int $paymentId
     * @param int $orderId
     * @return string
     */
    public function getPaymentKey($paymentId, $orderId)
    {
        return base64_encode($this->encryptor->encrypt($paymentId . ',' . $orderId));
    }

    /**
     * Get decrypted data from key
     * @param string $key
     * @return array
     */
    public function getPaymentFromKey($key)
    {
        $string = $this->encryptor->decrypt(base64_decode($key));
        $data = explode(",", (string)$string);
        return ['payment' => array_shift($data), 'order' => array_pop($data)];
    }

    /**
     * Get failed payment e-mail template
     *
     * @param string|null $storeCode
     * @return string
     */
    public function getFailedTemplateId($storeCode = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_EMAIL_TEMPLATE_FAILED,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * Get pending payment notice e-mail template
     *
     * @param string|null $storeCode
     * @return string
     */
    public function getNoticeTemplateId($storeCode = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_EMAIL_TEMPLATE_NOTICE,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * Get allowed payment methods (instruments) for Gopay Api
     *
     * @param null $storeCode
     * @return array
     */
    public function getInstruments($storeCode = null)
    {
        $config = $this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_ALLOWED_INSTRUMENTS,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );

        $instruments = [];
        if ($config) {
            $instruments = explode(",", (string)$config);
        }
        if (empty($instruments)) {
            foreach ($this->instruments->toOptionArray() as $option) {
                $instruments[] = $option['value'];
            }
        }

        return $instruments;
    }

    /**
     * Get allowed swift codes for order
     *
     * @param \Magento\Sales\Model\Order $order
     * @return array
     */
    public function getOrderSwifts(\Magento\Sales\Model\Order $order)
    {
        $method    = $order->getPayment()->getMethodInstance();
        $storeCode = $order->getStore()->getCode();
        $currency  = $order->getOrderCurrencyCode();
        $swifts    = [];

        if ($method instanceof \Maghos\Gopay\Model\Payment) {
            $additional = $method->getAdditionalInstruments($currency, $storeCode);
            foreach ($additional as $swift) {
                $swifts[] = $swift['code'];
            }
        }

        if ($selected = $this->getOrderSelectedSwift($order)) {
            if (in_array($selected, $swifts)) {
                return [$selected];
            }
        }

        return $swifts;
    }

    /**
     * Get default swift code for order
     *
     * @param \Magento\Sales\Model\Order $order
     * @return null|string
     */
    public function getOrderSelectedSwift(\Magento\Sales\Model\Order $order)
    {
        $payment     = $order->getPayment();
        $information = $payment->getAdditionalInformation();
        if (isset($information['gopay_instrument'])) {
            return $information['gopay_instrument'];
        }

        return null;
    }

    /**
     * Get allowed payment instruments for order
     *
     * @param \Magento\Sales\Model\Order $order
     * @return array
     */
    public function getOrderInstruments(\Magento\Sales\Model\Order $order)
    {
        $method    = $order->getPayment()->getMethodInstance();
        $storeCode = $order->getStore()->getCode();
        $currency  = $order->getOrderCurrencyCode();

        if (!$this->allowToChangeMethod()) {
            if ($instrument = $method->getInstrument()) {
                return [$instrument];
            }
        }

        $instruments = $method->getInstruments($currency, $storeCode, $order->getBaseGrandTotal());
        return array_keys($instruments);
    }

    /**
     * Check if customer is allowed to change method in repeated payment
     *
     * @param string|null $storeCode
     * @return bool
     */
    public function allowToChangeMethod($storeCode = null)
    {
        $state = $this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_CAN_CHANGE_PAYMENT,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );

        return (bool)$state;
    }

    /**
     * Get icons show state
     *
     * @param string|null $storeCode
     * @return bool
     */
    public function getShowIcons($storeCode = null)
    {
        $state = $this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_SHOW_ICONS,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );

        return (bool)$state;
    }

    /**
     * Get HTTP auth key
     *
     * @param string|null $storeCode
     * @return string
     */
    public function getAuthKey($storeCode = null)
    {
        $client = $this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_CLIENT_ID,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
        $secret = $this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_CLIENT_SECRET,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );

        return $client . ':' . $secret;
    }

    /**
     * Get payment success/error return url
     *
     * @param string|null $storeCode
     * @return string
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getReturnUrl($storeCode = null)
    {
        return $this->storeManager->getStore($storeCode)->getBaseUrl() . 'gopay/gateway/success/';
    }

    /**
     * Get payment callback/notify url
     *
     * @param string|null $storeCode
     * @return string
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getCallbackUrl($storeCode = null)
    {
        return $this->storeManager->getStore($storeCode)->getBaseUrl() . 'gopay/gateway/notify/';
    }

    /**
     * Get GO Id
     *
     * @param string|null $storeCode
     * @return string
     */
    public function getGoId($storeCode = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_GO_ID,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * Get Api URL
     *
     * @param string|null $storeCode
     * @return string
     */
    public function getApiUrl($storeCode = null)
    {
        $testing = $this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_TESTING_MODE,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );

        if ($testing == '0') {
            return 'https://gate.gopay.cz/api';
        } else {
            return 'https://gw.sandbox.gopay.com/api';
        }
    }

    /**
     * Get Inline gateway javascript URL
     *
     * @param string|null $storeCode
     * @return string
     */
    public function getJsUrl($storeCode = null)
    {
        $testing = $this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_TESTING_MODE,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );

        if ($testing == '0') {
            return 'https://gate.gopay.cz/gp-gw/js/embed.js';
        } else {
            return 'https://gw.sandbox.gopay.com/gp-gw/js/embed.js';
        }
    }

    /**
     * Get status from Api code
     *
     * @param string $statusCode
     * @return int
     */
    public function getStatus($statusCode)
    {
        $list = $this->getStatusList();
        if (isset($list[$statusCode])) {
            return $list[$statusCode];
        }
        return Payment::STATUS_ERROR;
    }

    /**
     * Get payment enabled status
     *
     * @param string $storeCode
     * @return bool
     */
    public function isEnabled($storeCode = null)
    {
        return (bool)$this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * Get if invoice email is sent
     *
     * @param string $storeCode
     * @return bool
     */
    public function getSendInvoiceEmail($storeCode = null)
    {
        return (bool)$this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_SEND_INVOICE_EMAIL,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * Get EET enabled status
     *
     * @param string $storeCode
     * @return bool
     */
    public function isEetEnabled($storeCode = null)
    {
        return (bool)$this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_EET_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * Check if payment link is shown in the customer account
     *
     * @param string $storeCode
     * @return bool
     */
    public function showAccountLink($storeCode = null)
    {
        return (bool)$this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_LINK_ACCOUNT,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * Get sales email link type
     *
     * @param string $storeCode
     * @return int
     */
    public function getEmailLinkType($storeCode = null)
    {
        return (int)$this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_LINK_EMAIL,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * Check if payment failed email is send
     *
     * @param string $storeCode
     * @return bool
     */
    public function sendFailedEmail($storeCode = null)
    {
        return (bool)$this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_EMAIL_FAILED,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * Get notice email timeout configuration
     *
     * @param string $storeCode
     * @return array
     */
    public function getNoticeEmailDays($storeCode = null)
    {
        $send = (bool)$this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_EMAIL_NOTICE,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
        if ($send) {
            $days = $this->scopeConfig->getValue(
                self::XML_PATH_GOPAY_EMAIL_NOTICE_DAYS,
                ScopeInterface::SCOPE_STORE,
                $storeCode
            );
            if ($days) {
                return array_values($this->serialize->unserialize($days));
            }
        }
        return [];
    }

    /**
     * Get EET shipping tax enabled status
     *
     * @param string $storeCode
     * @return bool
     */
    public function isEetShipping($storeCode = null)
    {
        return (bool)$this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_EET_SHIPPING,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * Get EET tax classes
     *
     * @param string|null $storeCode
     * @return array
     */
    public function getTaxClasses($storeCode = null)
    {
        $taxes = [0 => 0];
        if ($base = $this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_EET_TAX_BASE,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        )) {
            $taxes[1] = $base;
        }

        if ($lower = $this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_EET_TAX_LOWER1,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        )) {
            $taxes[2] = $lower;
        }

        if ($lowerSecond = $this->scopeConfig->getValue(
            self::XML_PATH_GOPAY_EET_TAX_LOWER2,
            ScopeInterface::SCOPE_STORE,
            $storeCode
        )) {
            $taxes[3] = $lowerSecond;
        }

        return $taxes;
    }

    /**
     * Get gateway languages supported by GoPay
     *
     * @return array
     */
    private function getSupportedLanguages()
    {
        return ['CS', 'EN', 'SK', 'DE', 'RU', 'PL', 'HU', 'RO', 'BG', 'HR', 'IT', 'FR', 'ES'];
    }

    /**
     * Get payment status map
     *
     * @return array
     */
    private function getStatusList()
    {
        return [
            'CREATED' => Payment::STATUS_NEW,
            'PAYMENT_METHOD_CHOSEN' => Payment::STATUS_METHOD_CHOSEN,
            'TIMEOUTED' => Payment::STATUS_TIMEOUT,
            'PAID' => Payment::STATUS_PAID,
            'AUTHORIZED' => Payment::STATUS_AUTHORIZED,
            'CANCELED' => Payment::STATUS_CANCELED,
            'REFUNDED' => Payment::STATUS_REFUNDED,
            'PARTIALLY_REFUNDED' => Payment::STATUS_REFUNDED_PARTIALLY
        ];
    }

    /**
     * Get store locale
     *
     * @param string $storeCode
     * @return string
     */
    private function getLocale($storeCode)
    {
        return $this->scopeConfig->getValue(
            'general/locale/code',
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }
}
