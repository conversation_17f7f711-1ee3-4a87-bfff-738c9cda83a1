<?php
/**
 *
 * Maghos_Gopay Magento 2 extension
 *
 * NOTICE OF LICENSE
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 * @category Maghos
 * @package Maghos_Gopay
 * @copyright Copyright (c) 2017 Maghos s.r.o.
 * @license http://www.maghos.com/business-license
 * <AUTHOR> dev team <<EMAIL>>
 */
namespace Maghos\Gopay\Model\Checkout;

use Magento\Checkout\Api\Data\PaymentDetailsInterface;
use Psr\Log\LoggerInterface as Logger;
use Magento\Quote\Api\Data\CartExtensionFactory;
use Magento\Quote\Model\ShippingAssignmentFactory;
use Magento\Quote\Model\ShippingFactory;

class ShippingInformationManagement extends \Magento\Checkout\Model\ShippingInformationManagement
{
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private $privateQuoteRepository;

    /**
     * @var \Maghos\Gopay\Api\Data\PaymentDetailsInterfaceFactory
     */
    private $detailsFactory;

    /**
     * Constructor
     *
     * @param \Magento\Quote\Api\PaymentMethodManagementInterface $paymentMethodManagement
     * @param \Magento\Checkout\Model\PaymentDetailsFactory $paymentDetailsFactory
     * @param \Magento\Quote\Api\CartTotalRepositoryInterface $cartTotalsRepository
     * @param \Magento\Quote\Api\CartRepositoryInterface $quoteRepository
     * @param \Magento\Quote\Model\QuoteAddressValidator $addressValidator
     * @param Logger $logger
     * @param \Magento\Customer\Api\AddressRepositoryInterface $addressRepository
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Quote\Model\Quote\TotalsCollector $totalsCollector
     * @param \Maghos\Gopay\Api\Data\PaymentDetailsInterfaceFactory $detailsFactory
     * @param CartExtensionFactory|null $cartExtensionFactory,
     * @param ShippingAssignmentFactory|null $shippingAssignmentFactory,
     * @param ShippingFactory|null $shippingFactory
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     */
    public function __construct(
        \Magento\Quote\Api\PaymentMethodManagementInterface $paymentMethodManagement,
        \Magento\Checkout\Model\PaymentDetailsFactory $paymentDetailsFactory,
        \Magento\Quote\Api\CartTotalRepositoryInterface $cartTotalsRepository,
        \Magento\Quote\Api\CartRepositoryInterface $quoteRepository,
        \Magento\Quote\Model\QuoteAddressValidator $addressValidator,
        Logger $logger,
        \Magento\Customer\Api\AddressRepositoryInterface $addressRepository,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Quote\Model\Quote\TotalsCollector $totalsCollector,
        \Maghos\Gopay\Api\Data\PaymentDetailsInterfaceFactory $detailsFactory,
        CartExtensionFactory $cartExtensionFactory = null,
        ShippingAssignmentFactory $shippingAssignmentFactory = null,
        ShippingFactory $shippingFactory = null
    ) {
        $this->privateQuoteRepository = $quoteRepository;
        $this->detailsFactory         = $detailsFactory;
        parent::__construct(
            $paymentMethodManagement,
            $paymentDetailsFactory,
            $cartTotalsRepository,
            $quoteRepository,
            $addressValidator,
            $logger,
            $addressRepository,
            $scopeConfig,
            $totalsCollector,
            $cartExtensionFactory,
            $shippingAssignmentFactory,
            $shippingFactory
        );
    }

    /**
     * @param int $cartId
     * @param \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
     * @return \Maghos\Gopay\Api\Data\PaymentDetailsInterface
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\StateException
     */
    public function saveAddressInformation(
        $cartId,
        \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
    ) : PaymentDetailsInterface {
        /** @var \Magento\Checkout\Api\Data\PaymentDetailsInterface $detail */
        $detail = parent::saveAddressInformation($cartId, $addressInformation);

        /** @var \Magento\Quote\Model\Quote $quote */
        $quote = $this->privateQuoteRepository->getActive($cartId);

        $newDetail = $this->detailsFactory->create();
        $methods   = [];
        foreach ($detail->getPaymentMethods() as $method) {
            $isDataObject = $method instanceof \Magento\Framework\DataObject;
            $data = [
                'code'                   => $method->getCode(),
                'method'                 => $isDataObject ? $method->getMethod() : null,
                'title'                  => $method->getTitle(),
                'additional_instruments' => []
            ];
            if ($method instanceof \Maghos\Gopay\Model\Payment) {
                $data['additional_instruments'] = $method->getAdditionalInstruments(
                    $quote->getCurrency()->getQuoteCurrencyCode(),
                    $quote->getStoreId()
                );
            }
            $methods[] = $data;
        }
        $newDetail->setTotals($detail->getTotals());
        if ($attributes = $detail->getExtensionAttributes()) {
            $newDetail->setExtensionAttributes($attributes);
        }
        $newDetail->setPaymentMethods($methods);

        return $newDetail;
    }
}
