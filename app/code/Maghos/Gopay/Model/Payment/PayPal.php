<?php
/**
 *
 * Maghos_Gopay Magento 2 extension
 *
 * NOTICE OF LICENSE
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 * @category Maghos
 * @package Maghos_Gopay
 * @copyright Copyright (c) 2017 Maghos s.r.o.
 * @license http://www.maghos.com/business-license
 * <AUTHOR> dev team <<EMAIL>>
 */
namespace Maghos\Gopay\Model\Payment;

class PayPal extends \Maghos\Gopay\Model\Payment
{

    const CODE = 'maghos_gopay_paypal';
    const SPECIFIC_CODE = 'paypal';

    /** @var string */
    public $_code = self::CODE;

    /** @var string */
    public $specificCode = self::SPECIFIC_CODE;

    /**
     * Retrieve payment method title
     *
     * @return string
     */
    public function getTitle()
    {
        $title = parent::getTitle();
        if (!trim((string)$title ?? "")) {
            $title = __('PayPal Account') . '';
        }
        return $title;
    }

    /**
     * Get payment specific instrument
     *
     * @return null|int
     */
    public function getInstrument()
    {
        return \Maghos\Gopay\Model\Source\Instruments::INSTRUMENT_PAYPAL;
    }
}
