<?php
/**
 *
 * Maghos_Gopay Magento 2 extension
 *
 * NOTICE OF LICENSE
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 * @category Maghos
 * @package Maghos_Gopay
 * @copyright Copyright (c) 2017 Maghos s.r.o.
 * @license http://www.maghos.com/business-license
 * <AUTHOR> dev team <<EMAIL>>
 */
namespace Maghos\Gopay\Model\ResourceModel\Token;

class Collection extends \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
{

    /**
     * Define model & resource model
     */
    public function _construct()
    {
        $this->_init(
            'Maghos\Gopay\Model\Gateway\Token',
            'Maghos\Gopay\Model\ResourceModel\Token'
        );
    }
}
