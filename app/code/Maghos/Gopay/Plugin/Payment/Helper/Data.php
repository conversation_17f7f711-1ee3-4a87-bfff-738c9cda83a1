<?php
/**
 *
 * Maghos_Gopay Magento 2 extension
 *
 * NOTICE OF LICENSE
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 * @category Maghos
 * @package Maghos_Gopay
 * @copyright Copyright (c) 2017 Maghos s.r.o.
 * @license http://www.maghos.com/business-license
 * <AUTHOR> dev team <<EMAIL>>
 */
namespace Maghos\Gopay\Plugin\Payment\Helper;

use Maghos\Gopay\Model\Payment;

class Data
{
    /** @var \Magento\Payment\Model\Method\Factory */
    private $methodFactory;

    /**
     * Data constructor.
     * @param \Magento\Payment\Model\Method\Factory $methodFactory
     */
    public function __construct(
        \Magento\Payment\Model\Method\Factory $methodFactory
    ) {
        $this->methodFactory = $methodFactory;
    }

    /**
     * @param \Magento\Payment\Helper\Data $helper
     * @param \Closure $proceed
     * @param bool $sorted
     * @param bool $asLabelValue
     * @param bool $withGroups
     * @param int|null $store
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function aroundGetPaymentMethodList(
        \Magento\Payment\Helper\Data $helper,
        \Closure $proceed,
        $sorted = true,
        $asLabelValue = false,
        $withGroups = false,
        $store = null
    ) {
        $titles = [];
        $labels = [];
        foreach ($helper->getPaymentMethods() as $code => $method) {
            if (strpos((string)$code, Payment::CODE) === 0) {
                $methodModel = $this->methodFactory->create($method['model']);
                $methodModel->setStore($store);
                $title = $methodModel->getTitle();
                $titles[$code] = $title;
                $labels[$code] = ['value' => $code, 'label' => $title];
            }
        }
        $methods = $proceed($sorted, $asLabelValue, $withGroups, $store);
        if (count($labels) <= 1) {
            return $methods;
        }
        $compare = function($a, $b) {
            if ($a['label'] == $b['label']) {
                return 0;
            }
            return ($a['label'] < $b['label']) ? -1 : 1;
        };

        if (isset($methods[Payment::CODE])) {
            unset($methods[Payment::CODE]);
        }
        if (!$asLabelValue) {
            $methods = array_merge($methods, $titles);
            if ($sorted) {
                asort($methods);
            }
        } else if ($withGroups) {
            $methods[Payment::CODE] = [
                'label' => isset($titles[Payment::CODE]) ? $titles[Payment::CODE] : 'Gopay',
                'value' => $labels
            ];
        } else {
            $methods = array_merge($methods, $labels);
            if ($sorted) {
                uasort($methods, $compare);
            }
        }

        return $methods;
    }
}
