<?php
/**
 *
 * Maghos_Gopay Magento 2 extension
 *
 * NOTICE OF LICENSE
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 * @category Maghos
 * @package Maghos_Gopay
 * @copyright Copyright (c) 2017 Maghos s.r.o.
 * @license http://www.maghos.com/business-license
 * <AUTHOR> dev team <<EMAIL>>
 */
namespace Maghos\Gopay\Plugin\Payment\Checks;

use Magento\Payment\Model\MethodInterface;
use Magento\Quote\Model\Quote;

class CanUseForCurrency
{

    /**
     * Check whether payment method is applicable to quote
     * Purposed to allow use in controllers some logic that was implemented in blocks only before
     *
     * @param \Magento\Payment\Model\Checks\CanUseForCurrency $subject
     * @param \Closure $proceed
     * @param \Magento\Payment\Model\MethodInterface $paymentMethod
     * @param \Magento\Quote\Model\Quote $quote
     * @return bool
     * @SuppressWarnings("PMD.UnusedFormalParameter")
     */
    public function aroundIsApplicable(
        \Magento\Payment\Model\Checks\CanUseForCurrency $subject,
        \Closure $proceed,
        MethodInterface $paymentMethod,
        Quote $quote
    ) {
        if ($paymentMethod instanceof \Maghos\Gopay\Model\Payment) {
            return $paymentMethod->canUseForCurrency($quote->getQuoteCurrencyCode());
        }
        return $proceed($paymentMethod, $quote);
    }
}
