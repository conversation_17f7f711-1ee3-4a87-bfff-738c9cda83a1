<?xml version="1.0"?>
<!--
/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_ExtraFee
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="order_totals">
            <block class="Mageplaza\ExtraFee\Block\Sales\Order\ExtraFee" name="mp_extra_fee" ifconfig="mp_extra_fee/general/enabled"/>
        </referenceBlock>
        <referenceContainer name="additional.product.info">
            <block class="Mageplaza\ExtraFee\Block\Sales\Order\View\ExtraFee" name="extra_fee_detail" template="Mageplaza_ExtraFee::order/view/extra-fee.phtml" ifconfig="mp_extra_fee/general/enabled"/>
        </referenceContainer>
    </body>
</page>