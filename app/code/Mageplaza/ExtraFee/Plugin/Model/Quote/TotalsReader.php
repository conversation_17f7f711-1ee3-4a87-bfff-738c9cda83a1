<?php
/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_ExtraFee
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */

namespace Mageplaza\ExtraFee\Plugin\Model\Quote;

use Magento\Checkout\Model\Cart;
use Mageplaza\ExtraFee\Helper\Data;

/**
 * Class TotalsReader
 * @package Mageplaza\ExtraFee\Plugin\Model\Quote
 */
class TotalsReader
{
    /**
     * @var Data
     */
    public $helperData;
    /**
     * @var Cart
     */
    private $cart;

    /**
     * TotalsReader constructor.
     * @param Data $helperData
     */
    public function __construct(
        Data $helperData,
        Cart $cart
    )
    {
        $this->helperData = $helperData;
        $this->cart = $cart;
    }

    public function afterFetch(\Magento\Quote\Model\Quote\TotalsReader $subject, $result)
    {
        $quote = $this->cart->getQuote();
        $extraFee = $this->helperData->getMpExtraFee($quote, 4);
        if (isset($extraFee[0])) {
            $extraFee = $extraFee[0];
            if ($extraFee['apply_type'] == 2) {
                $result['tax']->setValue($result['tax']->getValue() + $extraFee['value_incl_tax'] - $extraFee['value_excl_tax']);
            }

            $data = $result['tax']->getFullInfo();
            $data[$extraFee['code']] = [
                'amount' => ($extraFee['value_excl_tax'] /100 * $extraFee['percent']),
                'base_amount' => ($extraFee['value_excl_tax'] /100 * $extraFee['percent']),
                'percent' => $extraFee['percent'],
                'id' => $extraFee['code'],
                'rates' => [
                    [
                        'percent' => $extraFee['percent'],
                        'code' => $extraFee['code'],
                        'title' => $extraFee['title'],
                    ]
                ]
            ];

            $result['tax']->setFullInfo($data);
            $result['tax']->setValue($result['tax']->getValue() - ($extraFee['value_excl_tax'] /100 * $extraFee['percent']));
        }
        return $result;
    }

}