<?php
declare(strict_types=1);

namespace Mageplaza\ExtraFeeMollie\Console\Command;

use Magento\Framework\App\State;
use Magento\Sales\Api\OrderRepositoryInterface;
use Mageplaza\ExtraFeeMollie\Service\Order\Lines\Generator\MageplazaExtraFee;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class TestExtraFeeGenerator extends Command
{
    const ORDER_ID_ARGUMENT = 'order_id';

    private $generator;
    private $orderRepository;
    private $state;

    public function __construct(
        MageplazaExtraFee $generator,
        OrderRepositoryInterface $orderRepository,
        State $state,
        string $name = null
    ) {
        parent::__construct($name);
        $this->generator = $generator;
        $this->orderRepository = $orderRepository;
        $this->state = $state;
    }

    protected function configure()
    {
        $this->setName('mageplaza:extrafee:test')
            ->setDescription('Test ExtraFee Generator with specific order')
            ->setDefinition([
                new InputArgument(
                    self::ORDER_ID_ARGUMENT,
                    InputArgument::REQUIRED,
                    'Order ID to test'
                )
            ]);

        parent::configure();
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        try {
            $this->state->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);
            
            $orderId = $input->getArgument(self::ORDER_ID_ARGUMENT);
            $order = $this->orderRepository->get($orderId);
            
            $output->writeln("Testing ExtraFee Generator for Order #" . $orderId);
            
            // Initial empty orderLines array
            $orderLines = [];
            
            // Process the order
            $result = $this->generator->process($order, $orderLines);
            
            // Output results
            if (empty($result)) {
                $output->writeln("No extra fees found for this order.");
            } else {
                $output->writeln("Found extra fees:");
                foreach ($result as $line) {
                    $output->writeln(sprintf(
                        "- %s: %s %s",
                        $line['name'],
                        $line['totalAmount']['value'],
                        $line['totalAmount']['currency']
                    ));
                }
            }
            
            return 0;
            
        } catch (\Exception $e) {
            $output->writeln("<error>" . $e->getMessage() . "</error>");
            return 1;
        }
    }
}