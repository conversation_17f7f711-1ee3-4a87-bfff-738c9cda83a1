<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Se<PERSON>ling\ElasticsuiteGhostCleaner\Console\Command;

use <PERSON><PERSON>ling\ElasticsuiteGhostCleaner\Cron\DeleteGhosts;
use <PERSON>ymfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class Clean extends Command
{

    /**
     * @var DeleteGhosts
     */
    private $deleteGhosts;

    public function __construct(DeleteGhosts $deleteGhosts, string $name = null)
    {
        parent::__construct($name);
        $this->deleteGhosts = $deleteGhosts;
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $ghosts = $this->deleteGhosts->getGhostIndices();
        foreach ($ghosts as $name ) {
            $output->writeln("Deleting ghost: " . $name);
            $this->deleteGhosts->deleteIndex($name);
        }
        return 0;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName("elasticsuite:ghost:clean");
        $this->setDescription("Clean elasticsuite ghosts");
        parent::configure();
    }
}
