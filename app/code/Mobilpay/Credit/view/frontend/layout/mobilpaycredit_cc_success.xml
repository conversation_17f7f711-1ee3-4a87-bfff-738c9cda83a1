<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <title>Success Page</title>
    </head>
    <body>
        <referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Multumim pentru plata!</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="content">
            <block class="Mobilpay\Credit\Block\Success" name="success-Block" template="success.phtml"/>
        </referenceContainer>
    </body>
</page>