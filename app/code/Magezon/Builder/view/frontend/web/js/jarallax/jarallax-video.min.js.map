{"version": 3, "sources": ["jarallax-video.js"], "names": ["modules", "installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "callback", "document", "readyState", "attachEvent", "addEventListener", "global", "win", "window", "self", "this", "g", "_typeof", "iterator", "obj", "constructor", "Function", "eval", "e", "_videoWorker2", "_interopRequireDefault", "_global2", "_liteReady2", "_jarallaxVideo2", "default", "VideoWorker", "jarall<PERSON>", "querySelectorAll", "_createClass", "defineProperties", "target", "props", "length", "descriptor", "configurable", "writable", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "Deferred", "_done", "_fail", "execute", "list", "args", "Array", "slice", "apply", "resolve", "arguments", "reject", "done", "push", "fail", "ID", "YoutubeAPIadded", "VimeoAPIadded", "loadingYoutubePlayer", "loadingVimeoPlayer", "loadingYoutubeDefer", "loadingVimeoDefer", "url", "options", "instance", "TypeError", "_classCallCheck", "options_default", "autoplay", "loop", "mute", "volume", "showContols", "startTime", "endTime", "extend", "videoID", "parseURL", "loadAPI", "init", "out", "_arguments", "keys", "for<PERSON>ach", "match", "videoFormats", "result", "ready", "Youtube", "Vimeo", "Local", "split", "val", "type", "userEventsList", "_this", "_this2", "start", "player", "playVideo", "seekTo", "YT", "PlayerState", "PLAYING", "getPlayerState", "setCurrentTime", "getPaused", "then", "paused", "play", "currentTime", "pauseVideo", "pause", "setVolume", "$video", "muted", "unMute", "undefined", "getVolume", "isMuted", "videoImage", "availableSizes", "step", "tempImg", "Image", "onload", "naturalWidth", "width", "src", "request", "XMLHttpRequest", "open", "onreadystatechange", "status", "response", "JSON", "parse", "responseText", "thumbnail_large", "send", "getVideo", "onAPIready", "hiddenDiv", "createElement", "style", "display", "playerOptions", "videoId", "playerVars", "autohide", "rel", "playsinline", "iv_load_policy", "modestbranding", "controls", "showinfo", "disablekb", "ytStarted", "ytProgressInterval", "events", "onReady", "fire", "getDuration", "setInterval", "onStateChange", "data", "ENDED", "PAUSED", "getCurrentTime", "clearInterval", "firstInit", "div", "setAttribute", "playerID", "append<PERSON><PERSON><PERSON>", "body", "Player", "getElementById", "videoWidth", "parseInt", "getAttribute", "videoHeight", "id", "autopause", "transparent", "badge", "byline", "portrait", "title", "playerOptionsString", "encodeURIComponent", "getVideoWidth", "getVideoHeight", "height", "vmStarted", "on", "seconds", "element", "source", "locStarted", "tag", "head", "getElementsByTagName", "loaded", "onYouTubeIframeAPIReady", "vimeoInterval", "Jarallax", "defInit", "video", "disable<PERSON><PERSON><PERSON>", "$parent", "parentNode", "css", "position", "image", "top", "left", "right", "bottom", "max<PERSON><PERSON><PERSON>", "maxHeight", "margin", "zIndex", "$container", "<PERSON><PERSON><PERSON><PERSON>", "defCoverImage", "coverImage", "imageData", "node", "$item", "nodeName", "h", "w", "ml", "container", "mt", "marginTop", "marginLeft", "defInitImg", "initImg", "defaultResult", "videoSrc", "defaultInitImgResult", "defCanInitParallax", "canInitParallax", "videoLoop", "videoStartTime", "videoEndTime", "videoVolume", "<PERSON><PERSON><PERSON><PERSON>", "videoPlayOnlyVisible", "oldOnScroll", "onScroll", "videoEnded", "isVisible", "$default_item", "clipContainer", "getImageURL", "curStyle", "background-image", "background-position", "background-size", "defDest<PERSON>", "destroy"], "mappings": ";;;;;IAAA,SAAAA,GAEA,IAAAC,EAAA,GAGA,SAAAC,EAAAC,GAGA,GAAAF,EAAAE,GACA,OAAAF,EAAAE,GAAAC,QAGA,IAAAC,EAAAJ,EAAAE,GAAA,CACAG,EAAAH,EACAI,GAAA,EACAH,QAAA,IAUA,OANAJ,EAAAG,GAAAK,KAAAH,EAAAD,QAAAC,EAAAA,EAAAD,QAAAF,GAGAG,EAAAE,GAAA,EAGAF,EAAAD,QAKAF,EAAAO,EAAAT,EAGAE,EAAAQ,EAAAT,EAGAC,EAAAS,EAAA,SAAAP,EAAAQ,EAAAC,GACAX,EAAAY,EAAAV,EAAAQ,IACAG,OAAAC,eAAAZ,EAAAQ,EAAA,CAAAK,YAAA,EAAAC,IAAAL,KAKAX,EAAAiB,EAAA,SAAAf,GACA,oBAAAgB,QAAAA,OAAAC,aACAN,OAAAC,eAAAZ,EAAAgB,OAAAC,YAAA,CAAAC,MAAA,WAEAP,OAAAC,eAAAZ,EAAA,aAAA,CAAAkB,OAAA,KAQApB,EAAAqB,EAAA,SAAAD,EAAAE,GAEA,GADA,EAAAA,IAAAF,EAAApB,EAAAoB,IACA,EAAAE,EAAA,OAAAF,EACA,GAAA,EAAAE,GAAA,iBAAAF,GAAAA,GAAAA,EAAAG,WAAA,OAAAH,EACA,IAAAI,EAAAX,OAAAY,OAAA,MAGA,GAFAzB,EAAAiB,EAAAO,GACAX,OAAAC,eAAAU,EAAA,UAAA,CAAAT,YAAA,EAAAK,MAAAA,IACA,EAAAE,GAAA,iBAAAF,EAAA,IAAA,IAAAM,KAAAN,EAAApB,EAAAS,EAAAe,EAAAE,EAAA,SAAAA,GAAA,OAAAN,EAAAM,IAAAC,KAAA,KAAAD,IACA,OAAAF,GAIAxB,EAAA4B,EAAA,SAAAzB,GACA,IAAAQ,EAAAR,GAAAA,EAAAoB,WACA,WAAA,OAAApB,EAAA,SACA,WAAA,OAAAA,GAEA,OADAH,EAAAS,EAAAE,EAAA,IAAAA,GACAA,GAIAX,EAAAY,EAAA,SAAAiB,EAAAC,GAAA,OAAAjB,OAAAkB,UAAAC,eAAA1B,KAAAuB,EAAAC,IAGA9B,EAAAiC,EAAA,GAIAjC,EAAAA,EAAAkC,EAAA,GAnFA,CAsFA,CACA,CACA,CAEA,SAAA/B,EAAAD,EAAAF,GAEA,aAGAG,EAAAD,QAAA,SAAAiC,GAEA,aAAAC,SAAAC,YAAA,gBAAAD,SAAAC,WAEAF,EAAA7B,OACA8B,SAAAE,YAEAF,SAAAE,YAAA,qBAAA,WACA,gBAAAF,SAAAC,YAAAF,EAAA7B,SAEA8B,SAAAG,kBAEAH,SAAAG,iBAAA,mBAAAJ,KAKA,CAEA,SAAAhC,EAAAD,EAAAF,GAEA,cACA,SAAAwC,GAEA,IAAAC,EAGAA,EADA,oBAAAC,OACAA,YACA,IAAAF,EACAA,EACA,oBAAAG,KACAA,KAEA,GAGAxC,EAAAD,QAAAuC,IACAnC,KAAAsC,KAAA5C,EAAA,KAIA,SAAAG,EAAAD,EAAAF,GAEA,aAGA,IAEA6C,EAFAC,EAAA,mBAAA5B,QAAA,iBAAAA,OAAA6B,SAAA,SAAAC,GAAA,cAAAA,GAAA,SAAAA,GAAA,OAAAA,GAAA,mBAAA9B,QAAA8B,EAAAC,cAAA/B,QAAA8B,IAAA9B,OAAAa,UAAA,gBAAAiB,GAKAH,EAAA,WACA,OAAAD,KADA,GAIA,IAEAC,EAAAA,GAAAK,SAAA,cAAAA,KAAA,EAAAC,MAAA,QACA,MAAAC,GAEA,YAAA,oBAAAV,OAAA,YAAAI,EAAAJ,WAAAG,EAAAH,QAOAvC,EAAAD,QAAA2C,GAIA,SAAA1C,EAAAD,EAAAF,GAEAG,EAAAD,QAAAF,EAAA,IAKA,SAAAG,EAAAD,EAAAF,GAEA,aAGA,IAEAqD,EAAAC,EAFAtD,EAAA,IAMAuD,EAAAD,EAFAtD,EAAA,IAMAwD,EAAAF,EAFAtD,EAAA,IAMAyD,EAAAH,EAFAtD,EAAA,KAIA,SAAAsD,EAAAN,GAAA,OAAAA,GAAAA,EAAAzB,WAAAyB,EAAA,CAAAU,QAAAV,GAGAO,EAAAG,QAAAC,YAAAJ,EAAAG,QAAAC,aAAAN,EAAAK,SAEA,EAAAD,EAAAC,YAGA,EAAAF,EAAAE,SAAA,WACA,oBAAAE,UACAA,SAAAxB,SAAAyB,iBAAA,6BAMA,SAAA1D,EAAAD,EAAAF,GAEA,aAGAG,EAAAD,QAAAF,EAAA,IAIA,SAAAG,EAAAD,EAAAF,GAEA,aAGAa,OAAAC,eAAAZ,EAAA,aAAA,CACAkB,OAAA,IAGA,IAAA0B,EAAA,mBAAA5B,QAAA,iBAAAA,OAAA6B,SAAA,SAAAC,GAAA,cAAAA,GAAA,SAAAA,GAAA,OAAAA,GAAA,mBAAA9B,QAAA8B,EAAAC,cAAA/B,QAAA8B,IAAA9B,OAAAa,UAAA,gBAAAiB,GAEAc,EAAA,WAAA,SAAAC,EAAAC,EAAAC,GAAA,IAAA,IAAA7D,EAAA,EAAAA,EAAA6D,EAAAC,OAAA9D,IAAA,CAAA,IAAA+D,EAAAF,EAAA7D,GAAA+D,EAAApD,WAAAoD,EAAApD,aAAA,EAAAoD,EAAAC,cAAA,EAAA,UAAAD,IAAAA,EAAAE,UAAA,GAAAxD,OAAAC,eAAAkD,EAAAG,EAAAzC,IAAAyC,IAAA,OAAA,SAAAG,EAAAC,EAAAC,GAAA,OAAAD,GAAAR,EAAAO,EAAAvC,UAAAwC,GAAAC,GAAAT,EAAAO,EAAAE,GAAAF,GAAA,GAMA,SAAAG,IACA7B,KAAA8B,MAAA,GACA9B,KAAA+B,MAAA,GAEAF,EAAA1C,UAAA,CACA6C,QAAA,SAAAC,EAAAC,GACA,IAAA1E,EAAAyE,EAAAX,OAEA,IADAY,EAAAC,MAAAhD,UAAAiD,MAAA1E,KAAAwE,GACA1E,KACAyE,EAAAzE,GAAA6E,MAAA,KAAAH,IAGAI,QAAA,WACAtC,KAAAgC,QAAAhC,KAAA8B,MAAAS,YAEAC,OAAA,WACAxC,KAAAgC,QAAAhC,KAAA+B,MAAAQ,YAEAE,KAAA,SAAAlD,GACAS,KAAA8B,MAAAY,KAAAnD,IAEAoD,KAAA,SAAApD,GACAS,KAAA+B,MAAAW,KAAAnD,KAIA,IAAAqD,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,IAAApB,EACAqB,EAAA,IAAArB,EAEAd,EAAA,WACA,SAAAA,EAAAoC,EAAAC,IAvCA,SAAAC,EAAA3B,GAAA,KAAA2B,aAAA3B,GAAA,MAAA,IAAA4B,UAAA,qCAwCAC,CAAAvD,KAAAe,GAEA,IAAAhB,EAAAC,KAEAD,EAAAoD,IAAAA,EAEApD,EAAAyD,gBAAA,CACAC,UAAA,EACAC,MAAA,EACAC,MAAA,EACAC,OAAA,IACAC,aAAA,EAGAC,UAAA,EACAC,QAAA,GAGAhE,EAAAqD,QAAArD,EAAAiE,OAAA,GAAAjE,EAAAyD,gBAAAJ,GAGArD,EAAAkE,QAAAlE,EAAAmE,SAAAf,GAGApD,EAAAkE,UACAlE,EAAA6C,GAAAA,IACA7C,EAAAoE,UACApE,EAAAqE,QA+wBA,OAxwBAlD,EAAAH,EAAA,CAAA,CACAjC,IAAA,SACAN,MAAA,SAAA6F,GACA,IAAAC,EAAA/B,UAWA,OATA8B,EAAAA,GAAA,GACApG,OAAAsG,KAAAhC,WAAAiC,QAAA,SAAAhH,GACA8G,EAAA9G,IAGAS,OAAAsG,KAAAD,EAAA9G,IAAAgH,QAAA,SAAA1F,GACAuF,EAAAvF,GAAAwF,EAAA9G,GAAAsB,OAGAuF,IAEA,CACAvF,IAAA,WACAN,MAAA,SAAA2E,GAmCA,IA9BAsB,EAQAA,EAOAC,EACAC,EACAC,EAaAC,MA9BAJ,EA8BAtB,EA9BAsB,MADA,iEAEA,KAAAA,EAAA,GAAAnD,SAAAmD,EAAA,GA8BAK,MAvBAL,EAuBAtB,EAvBAsB,MADA,kJAEAA,EAAA,KAAAA,EAAA,GAuBAM,GAjBAL,EAiBAvB,EAjBA6B,MAAA,iCACAL,EAAA,GACAC,EAAA,EACAF,EAAAF,QAAA,SAAAS,GAEA,IAAAR,EAAAQ,EAAAR,MAAA,6BACAA,GAAAA,EAAA,IAAAA,EAAA,KAEAE,EAAA,QAAAF,EAAA,GAAA,MAAAA,EAAA,IAAAA,EAAA,GACAG,EAAA,OAGAA,GAAAD,GAOA,OAAAE,GACA7E,KAAAkF,KAAA,UACAL,GACAC,GACA9E,KAAAkF,KAAA,QACAJ,KACAC,IACA/E,KAAAkF,KAAA,QACAH,KAKA,CACAjG,IAAA,UACAN,MAAA,WACA,QAAAwB,KAAAiE,UAKA,CACAnF,IAAA,KACAN,MAAA,SAAAV,EAAAyB,GACAS,KAAAmF,eAAAnF,KAAAmF,gBAAA,IAGAnF,KAAAmF,eAAArH,KAAAkC,KAAAmF,eAAArH,GAAA,KAAA4E,KAAAnD,KAEA,CACAT,IAAA,MACAN,MAAA,SAAAV,EAAAyB,GACA,IAAA6F,EAAApF,KAEAA,KAAAmF,gBAAAnF,KAAAmF,eAAArH,KAIAyB,EAGAS,KAAAmF,eAAArH,GAAA0G,QAAA,SAAAS,EAAAnG,GACAmG,IAAA1F,IACA6F,EAAAD,eAAArH,GAAAgB,IAAA,YAJAkB,KAAAmF,eAAArH,MASA,CACAgB,IAAA,OACAN,MAAA,SAAAV,GACA,IAAAuH,EAAArF,KAEAkC,EAAA,GAAAE,MAAA1E,KAAA6E,UAAA,GACAvC,KAAAmF,qBAAA,IAAAnF,KAAAmF,eAAArH,IACAkC,KAAAmF,eAAArH,GAAA0G,QAAA,SAAAS,GAEAA,GACAA,EAAA5C,MAAAgD,EAAAnD,OAKA,CACApD,IAAA,OACAN,MAAA,SAAA8G,GACA,IAAAvF,EAAAC,KACAD,EAAAwF,SAIA,YAAAxF,EAAAmF,MAAAnF,EAAAwF,OAAAC,iBACA,IAAAF,GACAvF,EAAAwF,OAAAE,OAAAH,GAAA,GAEAI,GAAAC,YAAAC,UAAA7F,EAAAwF,OAAAM,kBACA9F,EAAAwF,OAAAC,aAIA,UAAAzF,EAAAmF,YACA,IAAAI,GACAvF,EAAAwF,OAAAO,eAAAR,GAEAvF,EAAAwF,OAAAQ,YAAAC,KAAA,SAAAC,GACAA,GACAlG,EAAAwF,OAAAW,UAKA,UAAAnG,EAAAmF,YACA,IAAAI,IACAvF,EAAAwF,OAAAY,YAAAb,GAEAvF,EAAAwF,OAAAU,QACAlG,EAAAwF,OAAAW,WAIA,CACApH,IAAA,QACAN,MAAA,WACA,IAAAuB,EAAAC,KACAD,EAAAwF,SAIA,YAAAxF,EAAAmF,MAAAnF,EAAAwF,OAAAa,YACAV,GAAAC,YAAAC,UAAA7F,EAAAwF,OAAAM,kBACA9F,EAAAwF,OAAAa,aAIA,UAAArG,EAAAmF,MACAnF,EAAAwF,OAAAQ,YAAAC,KAAA,SAAAC,GACAA,GACAlG,EAAAwF,OAAAc,UAKA,UAAAtG,EAAAmF,OACAnF,EAAAwF,OAAAU,QACAlG,EAAAwF,OAAAc,YAIA,CACAvH,IAAA,OACAN,MAAA,WACA,IAAAuB,EAAAC,KACAD,EAAAwF,SAIA,YAAAxF,EAAAmF,MAAAnF,EAAAwF,OAAA5B,MACA5D,EAAAwF,OAAA5B,OAGA,UAAA5D,EAAAmF,MAAAnF,EAAAwF,OAAAe,WACAvG,EAAAwF,OAAAe,UAAA,GAGA,UAAAvG,EAAAmF,OACAnF,EAAAwG,OAAAC,OAAA,MAGA,CACA1H,IAAA,SACAN,MAAA,WACA,IAAAuB,EAAAC,KACAD,EAAAwF,SAIA,YAAAxF,EAAAmF,MAAAnF,EAAAwF,OAAA5B,MACA5D,EAAAwF,OAAAkB,SAGA,UAAA1G,EAAAmF,MAAAnF,EAAAwF,OAAAe,WACAvG,EAAAwF,OAAAe,UAAAvG,EAAAqD,QAAAQ,QAGA,UAAA7D,EAAAmF,OACAnF,EAAAwG,OAAAC,OAAA,MAGA,CACA1H,IAAA,YACAN,MAAA,WACA,IAAAoF,EAAA,EAAArB,UAAAjB,aAAAoF,IAAAnE,UAAA,IAAAA,UAAA,GAEAxC,EAAAC,KACAD,EAAAwF,QAAA3B,IAIA,YAAA7D,EAAAmF,MAAAnF,EAAAwF,OAAAe,WACAvG,EAAAwF,OAAAe,UAAA1C,GAGA,UAAA7D,EAAAmF,MAAAnF,EAAAwF,OAAAe,WACAvG,EAAAwF,OAAAe,UAAA1C,GAGA,UAAA7D,EAAAmF,OACAnF,EAAAwG,OAAA3C,OAAAA,EAAA,QAGA,CACA9E,IAAA,YACAN,MAAA,SAAAe,GACA,IAAAQ,EAAAC,KACAD,EAAAwF,QAKA,YAAAxF,EAAAmF,MAAAnF,EAAAwF,OAAAoB,WACApH,EAAAQ,EAAAwF,OAAAoB,aAGA,UAAA5G,EAAAmF,MAAAnF,EAAAwF,OAAAoB,WACA5G,EAAAwF,OAAAoB,YAAAX,KAAA,SAAApC,GACArE,EAAAqE,KAIA,UAAA7D,EAAAmF,MACA3F,EAAA,IAAAQ,EAAAwG,OAAA3C,SAfArE,GAAA,KAkBA,CACAT,IAAA,WACAN,MAAA,SAAAe,GACA,IAAAQ,EAAAC,KACAD,EAAAwF,QAKA,YAAAxF,EAAAmF,MAAAnF,EAAAwF,OAAAqB,SACArH,EAAAQ,EAAAwF,OAAAqB,WAGA,UAAA7G,EAAAmF,MAAAnF,EAAAwF,OAAAoB,WACA5G,EAAAwF,OAAAoB,YAAAX,KAAA,SAAApC,GACArE,IAAAqE,KAIA,UAAA7D,EAAAmF,MACA3F,EAAAQ,EAAAwG,OAAAC,QAfAjH,EAAA,QAkBA,CACAT,IAAA,cACAN,MAAA,SAAAe,GACA,IAAAQ,EAAAC,KAEA,GAAAD,EAAA8G,WACAtH,EAAAQ,EAAA8G,gBADA,CAKA,GAAA,YAAA9G,EAAAmF,KAAA,CACA,IAAA4B,EAAA,CAAA,gBAAA,YAAA,YAAA,KACAC,EAAA,EAEAC,EAAA,IAAAC,MACAD,EAAAE,OAAA,WAEA,OAAAlH,KAAAmH,cAAAnH,KAAAoH,QAAAL,IAAAD,EAAAxF,OAAA,GAEAvB,EAAA8G,WAAA,8BAAA9G,EAAAkE,QAAA,IAAA6C,EAAAC,GAAA,OACAxH,EAAAQ,EAAA8G,cAGAE,IACA/G,KAAAqH,IAAA,8BAAAtH,EAAAkE,QAAA,IAAA6C,EAAAC,GAAA,SAGAC,EAAAK,IAAA,8BAAAtH,EAAAkE,QAAA,IAAA6C,EAAAC,GAAA,OAGA,GAAA,UAAAhH,EAAAmF,KAAA,CACA,IAAAoC,EAAA,IAAAC,eACAD,EAAAE,KAAA,MAAA,kCAAAzH,EAAAkE,QAAA,SAAA,GACAqD,EAAAG,mBAAA,WACA,GAAA,IAAAzH,KAAAP,YACA,KAAAO,KAAA0H,QAAA1H,KAAA0H,OAAA,IAAA,CAEA,IAAAC,EAAAC,KAAAC,MAAA7H,KAAA8H,cACA/H,EAAA8G,WAAAc,EAAA,GAAAI,gBACAxI,EAAAQ,EAAA8G,cAMAS,EAAAU,OACAV,EAAA,SAMA,CACAxI,IAAA,YACAN,MAAA,SAAAe,GACAS,KAAAiI,SAAA1I,KAEA,CACAT,IAAA,WACAN,MAAA,SAAAe,GACA,IAAAQ,EAAAC,KAGAD,EAAAwG,OACAhH,EAAAQ,EAAAwG,QAKAxG,EAAAmI,WAAA,WACA,IAAAC,OAAA,EAOA,GANApI,EAAAwG,UACA4B,EAAA3I,SAAA4I,cAAA,QACAC,MAAAC,QAAA,QAIA,YAAAvI,EAAAmF,KAAA,CACAnF,EAAAwI,cAAA,GACAxI,EAAAwI,cAAAC,QAAAzI,EAAAkE,QACAlE,EAAAwI,cAAAE,WAAA,CACAC,SAAA,EACAC,IAAA,EACAlF,SAAA,EAEAmF,YAAA,GAIA7I,EAAAqD,QAAAS,cACA9D,EAAAwI,cAAAE,WAAAI,eAAA,EACA9I,EAAAwI,cAAAE,WAAAK,eAAA,EACA/I,EAAAwI,cAAAE,WAAAM,SAAA,EACAhJ,EAAAwI,cAAAE,WAAAO,SAAA,EACAjJ,EAAAwI,cAAAE,WAAAQ,UAAA,GAIA,IAAAC,OAAA,EACAC,OAAA,EACApJ,EAAAwI,cAAAa,OAAA,CACAC,QAAA,SAAA7I,GAgBA,GAdAT,EAAAqD,QAAAO,KACAnD,EAAAY,OAAAuC,OACA5D,EAAAqD,QAAAQ,QACApD,EAAAY,OAAAkF,UAAAvG,EAAAqD,QAAAQ,QAIA7D,EAAAqD,QAAAK,UACA1D,EAAAmG,KAAAnG,EAAAqD,QAAAU,WAEA/D,EAAAuJ,KAAA,QAAA9I,GAIAT,EAAAqD,QAAAM,OAAA3D,EAAAqD,QAAAW,QAAA,CAEAhE,EAAAqD,QAAAW,QAAAhE,EAAAwF,OAAAgE,cADA,GAKAC,YAAA,WACAzJ,EAAA4G,UAAA,SAAA/C,GACA7D,EAAAqD,QAAAQ,SAAAA,IACA7D,EAAAqD,QAAAQ,OAAAA,EACA7D,EAAAuJ,KAAA,eAAA9I,OAGA,MAEAiJ,cAAA,SAAAjJ,GAEAT,EAAAqD,QAAAM,MAAAlD,EAAAkJ,OAAAhE,GAAAC,YAAAgE,OACA5J,EAAAmG,KAAAnG,EAAAqD,QAAAU,WAEAoF,GAAA1I,EAAAkJ,OAAAhE,GAAAC,YAAAC,UACAsD,EAAA,EACAnJ,EAAAuJ,KAAA,UAAA9I,IAEAA,EAAAkJ,OAAAhE,GAAAC,YAAAC,SACA7F,EAAAuJ,KAAA,OAAA9I,GAEAA,EAAAkJ,OAAAhE,GAAAC,YAAAiE,QACA7J,EAAAuJ,KAAA,QAAA9I,GAEAA,EAAAkJ,OAAAhE,GAAAC,YAAAgE,OACA5J,EAAAuJ,KAAA,QAAA9I,GAIAA,EAAAkJ,OAAAhE,GAAAC,YAAAC,QACAuD,EAAAK,YAAA,WACAzJ,EAAAuJ,KAAA,aAAA9I,GAGAT,EAAAqD,QAAAW,SAAAhE,EAAAwF,OAAAsE,kBAAA9J,EAAAqD,QAAAW,UACAhE,EAAAqD,QAAAM,KACA3D,EAAAmG,KAAAnG,EAAAqD,QAAAU,WAEA/D,EAAAsG,UAGA,KAEAyD,cAAAX,KAKA,IAAAY,GAAAhK,EAAAwG,OACA,GAAAwD,EAAA,CACA,IAAAC,EAAAxK,SAAA4I,cAAA,OACA4B,EAAAC,aAAA,KAAAlK,EAAAmK,UACA/B,EAAAgC,YAAAH,GACAxK,SAAA4K,KAAAD,YAAAhC,GAEApI,EAAAwF,OAAAxF,EAAAwF,QAAA,IAAAzF,OAAA4F,GAAA2E,OAAAtK,EAAAmK,SAAAnK,EAAAwI,eACAwB,IACAhK,EAAAwG,OAAA/G,SAAA8K,eAAAvK,EAAAmK,UAGAnK,EAAAwK,WAAAC,SAAAzK,EAAAwG,OAAAkE,aAAA,SAAA,KAAA,KACA1K,EAAA2K,YAAAF,SAAAzK,EAAAwG,OAAAkE,aAAA,UAAA,KAAA,KAKA,GAAA,UAAA1K,EAAAmF,KAAA,CAsBA,GArBAnF,EAAAwI,cAAA,CACAoC,GAAA5K,EAAAkE,QACA2G,UAAA,EACAC,YAAA,EACApH,SAAA1D,EAAAqD,QAAAK,SAAA,EAAA,EACAC,KAAA3D,EAAAqD,QAAAM,KAAA,EAAA,EACA8C,MAAAzG,EAAAqD,QAAAO,KAAA,EAAA,GAGA5D,EAAAqD,QAAAQ,SACA7D,EAAAwI,cAAA3E,OAAA7D,EAAAqD,QAAAQ,QAIA7D,EAAAqD,QAAAS,cACA9D,EAAAwI,cAAAuC,MAAA,EACA/K,EAAAwI,cAAAwC,OAAA,EACAhL,EAAAwI,cAAAyC,SAAA,EACAjL,EAAAwI,cAAA0C,MAAA,IAGAlL,EAAAwG,OAAA,CACA,IAAA2E,EAAA,GACAjN,OAAAsG,KAAAxE,EAAAwI,eAAA/D,QAAA,SAAA1F,GACA,KAAAoM,IACAA,GAAA,KAEAA,GAAApM,EAAA,IAAAqM,mBAAApL,EAAAwI,cAAAzJ,MAKAiB,EAAAwG,OAAA/G,SAAA4I,cAAA,UACArI,EAAAwG,OAAA0D,aAAA,KAAAlK,EAAAmK,UACAnK,EAAAwG,OAAA0D,aAAA,MAAA,kCAAAlK,EAAAkE,QAAA,IAAAiH,GACAnL,EAAAwG,OAAA0D,aAAA,cAAA,KACAlK,EAAAwG,OAAA0D,aAAA,qBAAA,IACAlK,EAAAwG,OAAA0D,aAAA,kBAAA,IAEA9B,EAAAgC,YAAApK,EAAAwG,QACA/G,SAAA4K,KAAAD,YAAAhC,GAEApI,EAAAwF,OAAAxF,EAAAwF,QAAA,IAAAT,MAAAuF,OAAAtK,EAAAwG,OAAAxG,EAAAwI,eAGAxI,EAAAqD,QAAAU,WAAA/D,EAAAqD,QAAAK,UACA1D,EAAAwF,OAAAO,eAAA/F,EAAAqD,QAAAU,WAIA/D,EAAAwF,OAAA6F,gBAAApF,KAAA,SAAAoB,GACArH,EAAAwK,WAAAnD,GAAA,OAEArH,EAAAwF,OAAA8F,iBAAArF,KAAA,SAAAsF,GACAvL,EAAA2K,YAAAY,GAAA,MAIA,IAAAC,OAAA,EACAxL,EAAAwF,OAAAiG,GAAA,aAAA,SAAAhL,GACA+K,IACAxL,EAAAuJ,KAAA,UAAA9I,GACA+K,EAAA,GAGAxL,EAAAuJ,KAAA,aAAA9I,GAGAT,EAAAqD,QAAAW,SACAhE,EAAAqD,QAAAW,SAAAvD,EAAAiL,SAAA1L,EAAAqD,QAAAW,UACAhE,EAAAqD,QAAAM,KACA3D,EAAAmG,KAAAnG,EAAAqD,QAAAU,WAEA/D,EAAAsG,WAKAtG,EAAAwF,OAAAiG,GAAA,OAAA,SAAAhL,GACAT,EAAAuJ,KAAA,OAAA9I,GAGAT,EAAAqD,QAAAU,WAAA,IAAAtD,EAAAiL,SACA1L,EAAAmG,KAAAnG,EAAAqD,QAAAU,aAGA/D,EAAAwF,OAAAiG,GAAA,QAAA,SAAAhL,GACAT,EAAAuJ,KAAA,QAAA9I,KAEAT,EAAAwF,OAAAiG,GAAA,QAAA,SAAAhL,GACAT,EAAAuJ,KAAA,QAAA9I,KAEAT,EAAAwF,OAAAiG,GAAA,SAAA,SAAAhL,GACAT,EAAAuJ,KAAA,QAAA9I,KAEAT,EAAAwF,OAAAiG,GAAA,eAAA,SAAAhL,GACAT,EAAAuJ,KAAA,eAAA9I,KAWA,GAAA,UAAAT,EAAAmF,KAAA,CACAnF,EAAAwG,SACAxG,EAAAwG,OAAA/G,SAAA4I,cAAA,SAGArI,EAAAqD,QAAAS,cACA9D,EAAAwG,OAAAwC,UAAA,GAIAhJ,EAAAqD,QAAAO,KACA5D,EAAAwG,OAAAC,OAAA,EACAzG,EAAAwG,OAAA3C,SACA7D,EAAAwG,OAAA3C,OAAA7D,EAAAqD,QAAAQ,OAAA,KAIA7D,EAAAqD,QAAAM,OACA3D,EAAAwG,OAAA7C,MAAA,GAIA3D,EAAAwG,OAAA0D,aAAA,cAAA,IACAlK,EAAAwG,OAAA0D,aAAA,qBAAA,IAEAlK,EAAAwG,OAAA0D,aAAA,KAAAlK,EAAAmK,UACA/B,EAAAgC,YAAApK,EAAAwG,QACA/G,SAAA4K,KAAAD,YAAAhC,GAEAlK,OAAAsG,KAAAxE,EAAAkE,SAAAO,QAAA,SAAA1F,GAnCA,IAAA4M,EAAArE,EAAAnC,EACAyG,EADAD,EAoCA3L,EAAAwG,OApCAc,EAoCAtH,EAAAkE,QAAAnF,GApCAoG,EAoCA,SAAApG,GAnCA6M,EAAAnM,SAAA4I,cAAA,WACAf,IAAAA,EACAsE,EAAAzG,KAAAA,EACAwG,EAAAvB,YAAAwB,MAoCA5L,EAAAwF,OAAAxF,EAAAwF,QAAAxF,EAAAwG,OAEA,IAAAqF,OAAA,EACA7L,EAAAwF,OAAA5F,iBAAA,UAAA,SAAAa,GACAoL,GACA7L,EAAAuJ,KAAA,UAAA9I,GAEAoL,EAAA,IAEA7L,EAAAwF,OAAA5F,iBAAA,aAAA,SAAAa,GACAT,EAAAuJ,KAAA,aAAA9I,GAGAT,EAAAqD,QAAAW,SACAhE,EAAAqD,QAAAW,SAAA/D,KAAAmG,aAAApG,EAAAqD,QAAAW,UACAhE,EAAAqD,QAAAM,KACA3D,EAAAmG,KAAAnG,EAAAqD,QAAAU,WAEA/D,EAAAsG,WAKAtG,EAAAwF,OAAA5F,iBAAA,OAAA,SAAAa,GACAT,EAAAuJ,KAAA,OAAA9I,KAEAT,EAAAwF,OAAA5F,iBAAA,QAAA,SAAAa,GACAT,EAAAuJ,KAAA,QAAA9I,KAEAT,EAAAwF,OAAA5F,iBAAA,QAAA,SAAAa,GACAT,EAAAuJ,KAAA,QAAA9I,KAEAT,EAAAwF,OAAA5F,iBAAA,iBAAA,WAEAI,EAAAwK,WAAAvK,KAAAuK,YAAA,KACAxK,EAAA2K,YAAA1K,KAAA0K,aAAA,IAEA3K,EAAAuJ,KAAA,SAGAvJ,EAAAqD,QAAAK,UACA1D,EAAAmG,KAAAnG,EAAAqD,QAAAU,aAGA/D,EAAAwF,OAAA5F,iBAAA,eAAA,SAAAa,GACAT,EAAA4G,UAAA,SAAA/C,GACA7D,EAAAqD,QAAAQ,OAAAA,IAEA7D,EAAAuJ,KAAA,eAAA9I,KAGAjB,EAAAQ,EAAAwG,YAGA,CACAzH,IAAA,OACAN,MAAA,WACAwB,KAEAkK,SAAA,eAFAlK,KAEA4C,KAEA,CACA9D,IAAA,UACAN,MAAA,WAGA,IAAAqE,IAAAC,EAAA,CAIA,IAAAuE,EAAA,GAcA,GAXA,YATArH,KASAkF,MAAArC,IACAA,EAAA,EACAwE,EAAA,sCAIA,UAfArH,KAeAkF,MAAApC,IACAA,EAAA,EACAuE,EAAA,0CAGAA,EAAA,CAKA,IAAAwE,EAAArM,SAAA4I,cAAA,UACA0D,EAAAtM,SAAAuM,qBAAA,QAAA,GACAF,EAAAxE,IAAAA,EAEAyE,EAAA3B,YAAA0B,GAGAA,EADAC,EAAA,SAGA,CACAhN,IAAA,aACAN,MAAA,SAAAe,GA0BA,GAtBA,YAHAS,KAGAkF,OAEA,oBAAAQ,IAAA,IAAAA,GAAAsG,QAAAjJ,EAUA,YAAA,oBAAA2C,GAAA,YAAAxF,EAAAwF,MAAA,IAAAA,GAAAsG,OACAzM,IAEA0D,EAAAR,KAAA,WACAlD,OAZAwD,EAAA,EAGAjD,OAAAmM,wBAAA,WACAnM,OAAAmM,wBAAA,KACAhJ,EAAAX,QAAA,QACA/C,OAYA,UAzBAS,KAyBAkF,KACA,GAAA,oBAAAJ,OAAA9B,EASA,oBAAA8B,MACAvF,IAEA2D,EAAAT,KAAA,WACAlD,UAbA,CACAyD,EAAA,EACA,IAAAkJ,EAAA1C,YAAA,WACA,oBAAA1E,QACAgF,cAAAoC,GACAhJ,EAAAZ,QAAA,QACA/C,MAEA,IAWA,UA7CAS,KA6CAkF,MACA3F,QAKAwB,EA5yBA,GA+yBAzD,EAAAwD,QAAAC,GAIA,SAAAxD,EAAAD,EAAAF,GAEA,aAGAa,OAAAC,eAAAZ,EAAA,aAAA,CACAkB,OAAA,IAEAlB,EAAAwD,QAYA,WACA,IAAAE,EAAA,EAAAuB,UAAAjB,aAAAoF,IAAAnE,UAAA,GAAAA,UAAA,GAAA5B,EAAAG,QAAAE,SAEA,QAAA,IAAAA,EACA,OAGA,IAAAmL,EAAAnL,EAAAX,YAGA+L,EAAAD,EAAAhN,UAAAiF,KACA+H,EAAAhN,UAAAiF,KAAA,WACA,IAAArE,EAAAC,KAEAoM,EAAA/J,MAAAtC,GAEAA,EAAAsM,QAAAtM,EAAAqD,QAAAkJ,gBACAvM,EAAAsM,MAAApE,SAAA,SAAAoE,GACA,IAAAE,EAAAF,EAAAG,WACAzM,EAAA0M,IAAAJ,EAAA,CACAK,SAAA3M,EAAA4M,MAAAD,SACAE,IAAA,MACAC,KAAA,MACAC,MAAA,MACAC,OAAA,MACA3F,MAAA,OACAkE,OAAA,OACA0B,SAAA,OACAC,UAAA,OACAC,OAAA,EACAC,QAAA,IAEApN,EAAAwG,OAAA8F,EACAtM,EAAA4M,MAAAS,WAAAjD,YAAAkC,GAGAE,EAAAC,WAAAa,YAAAd,MAMA,IAAAe,EAAAnB,EAAAhN,UAAAoO,WACApB,EAAAhN,UAAAoO,WAAA,WACA,IAAAxN,EAAAC,KACAwN,EAAAF,EAAAjL,MAAAtC,GACA0N,IAAA1N,EAAA4M,MAAAe,OAAA3N,EAAA4M,MAAAe,MAAAC,SAEA,GAAAH,GAAAzN,EAAAsM,OAAAoB,IAAA,WAAAA,GAAA,UAAAA,GAAA,CACA,IAAAG,EAAAJ,EAAAb,MAAArB,OACAuC,EAAAD,EAAA7N,EAAA4M,MAAAvF,MAAArH,EAAA4M,MAAArB,OACAwC,GAAAN,EAAAO,UAAA3G,MAAAyG,GAAA,EACAG,EAAAR,EAAAb,MAAAsB,UAEAT,EAAAO,UAAA3G,MAAAyG,IACAA,EAAAL,EAAAO,UAAA3G,MACAwG,EAAAC,EAAA9N,EAAA4M,MAAArB,OAAAvL,EAAA4M,MAAAvF,MACA0G,EAAA,EACAE,IAAAR,EAAAb,MAAArB,OAAAsC,GAAA,GAIA,WAAAH,IACAG,GAAA,IACAI,GAAA,KAGAjO,EAAA0M,IAAA1M,EAAAwG,OAAA,CACAa,MAAAyG,EAAA,KACAK,WAAAJ,EAAA,KACAxC,OAAAsC,EAAA,KACAK,UAAAD,EAAA,OAIA,OAAAR,GAIA,IAAAW,EAAAhC,EAAAhN,UAAAiP,QACAjC,EAAAhN,UAAAiP,QAAA,WACA,IAAArO,EAAAC,KACAqO,EAAAF,EAAA9L,MAAAtC,GAMA,OAJAA,EAAAqD,QAAAkL,WACAvO,EAAAqD,QAAAkL,SAAAvO,EAAA2N,MAAAjD,aAAA,wBAAA,MAGA1K,EAAAqD,QAAAkL,UACAvO,EAAAwO,qBAAAF,GACA,GAGAA,GAGA,IAAAG,EAAArC,EAAAhN,UAAAsP,gBACAtC,EAAAhN,UAAAsP,gBAAA,WACA,IAAA1O,EAAAC,KACAqO,EAAAG,EAAAnM,MAAAtC,GAEA,IAAAA,EAAAqD,QAAAkL,SACA,OAAAD,EAGA,IAAAhC,EAAA,IAAA5L,EAAAK,QAAAf,EAAAqD,QAAAkL,SAAA,CACA7K,UAAA,EACAC,KAAA3D,EAAAqD,QAAAsL,UACA7K,aAAA,EACAC,UAAA/D,EAAAqD,QAAAuL,gBAAA,EACA5K,QAAAhE,EAAAqD,QAAAwL,cAAA,EACAjL,KAAA5D,EAAAqD,QAAAyL,YAAA,EAAA,EACAjL,OAAA7D,EAAAqD,QAAAyL,aAAA,IAGA,GAAAxC,EAAAyC,UAEA,GAAAT,GA2EA,GAvDAhC,EAAAb,GAAA,QAAA,WACA,GAAAzL,EAAAqD,QAAA2L,qBAAA,CACA,IAAAC,EAAAjP,EAAAkP,SACAlP,EAAAkP,SAAA,WACAD,EAAA3M,MAAAtC,IACAA,EAAAqD,QAAAsL,YAAA3O,EAAAqD,QAAAsL,YAAA3O,EAAAmP,cACAnP,EAAAoP,YACA9C,EAAAnG,OAEAmG,EAAAhG,eAKAgG,EAAAnG,SAIAmG,EAAAb,GAAA,UAAA,WACAzL,EAAA4M,MAAAyC,cAAArP,EAAA4M,MAAAe,MACA3N,EAAA4M,MAAAe,MAAA3N,EAAAwG,OAGAxG,EAAA4M,MAAAvF,MAAArH,EAAAsM,MAAA9B,YAAA,KACAxK,EAAA4M,MAAArB,OAAAvL,EAAAsM,MAAA3B,aAAA,IACA3K,EAAAwN,aACAxN,EAAAsP,gBACAtP,EAAAkP,WAGAlP,EAAA4M,MAAAyC,gBACArP,EAAA4M,MAAAyC,cAAA/G,MAAAC,QAAA,UAIA+D,EAAAb,GAAA,QAAA,WACAzL,EAAAmP,YAAA,EAEAnP,EAAAqD,QAAAsL,WAEA3O,EAAA4M,MAAAyC,gBACArP,EAAA4M,MAAAe,MAAA3N,EAAA4M,MAAAyC,cACArP,EAAA4M,MAAAe,MAAArF,MAAAC,QAAA,QAGAvI,EAAAwN,aACAxN,EAAAsP,gBACAtP,EAAAkP,cAKAlP,EAAAsM,MAAAA,GAGAtM,EAAAwO,qBACA,MAAA,UAAAlC,EAAAnH,MACAmH,EAAAiD,YAAA,SAAAnM,GACApD,EAAA4M,MAAAtF,IAAAlE,EACApD,EAAAqE,UAGA,IAIArE,EAAA4M,MAAAtF,IAAA,kFACA,QAtFAtH,EAAAwO,sBACAlC,EAAAiD,YAAA,SAAAnM,GAEA,IAAAoM,EAAAxP,EAAA2N,MAAAjD,aAAA,SACA8E,GACAxP,EAAA2N,MAAAzD,aAAA,gCAAAsF,GAIAxP,EAAA0M,IAAA1M,EAAA2N,MAAA,CACA8B,mBAAA,QAAArM,EAAA,KACAsM,sBAAA,SACAC,kBAAA,YA+EA,OAAArB,GAIA,IAAAsB,EAAAxD,EAAAhN,UAAAyQ,QACAzD,EAAAhN,UAAAyQ,QAAA,WACA,IAAA7P,EAAAC,KAEAD,EAAA4M,MAAAyC,gBACArP,EAAA4M,MAAAe,MAAA3N,EAAA4M,MAAAyC,qBACArP,EAAA4M,MAAAyC,eAGAO,EAAAtN,MAAAtC,KAxOA,IAEAU,EAAAC,EAFAtD,EAAA,IAMAuD,EAAAD,EAFAtD,EAAA,IAIA,SAAAsD,EAAAN,GAAA,OAAAA,GAAAA,EAAAzB,WAAAyB,EAAA,CAAAU,QAAAV", "file": "jarallax-video.min.js", "sourcesContent": ["/*!\n * Name    : Video Background Extension for Jarallax\n * Version : 1.0.1\n * Author  : nK <https://nkdev.info>\n * GitHub  : https://github.com/nk-o/jarallax\n */\n/******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// define __esModule on exports\n/******/ \t__webpack_require__.r = function(exports) {\n/******/ \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t}\n/******/ \t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t};\n/******/\n/******/ \t// create a fake namespace object\n/******/ \t// mode & 1: value is a module id, require it\n/******/ \t// mode & 2: merge all properties of value into the ns\n/******/ \t// mode & 4: return value when already ns object\n/******/ \t// mode & 8|1: behave like require\n/******/ \t__webpack_require__.t = function(value, mode) {\n/******/ \t\tif(mode & 1) value = __webpack_require__(value);\n/******/ \t\tif(mode & 8) return value;\n/******/ \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n/******/ \t\tvar ns = Object.create(null);\n/******/ \t\t__webpack_require__.r(ns);\n/******/ \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n/******/ \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n/******/ \t\treturn ns;\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 6);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */,\n/* 1 */,\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nmodule.exports = function (callback) {\n\n\tif (document.readyState === 'complete' || document.readyState === 'interactive') {\n\t\t// Already ready or interactive, execute callback\n\t\tcallback.call();\n\t} else if (document.attachEvent) {\n\t\t// Old browsers\n\t\tdocument.attachEvent('onreadystatechange', function () {\n\t\t\tif (document.readyState === 'interactive') callback.call();\n\t\t});\n\t} else if (document.addEventListener) {\n\t\t// Modern browsers\n\t\tdocument.addEventListener('DOMContentLoaded', callback);\n\t}\n};\n\n/***/ }),\n/* 3 */,\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n/* WEBPACK VAR INJECTION */(function(global) {\n\nvar win;\n\nif (typeof window !== \"undefined\") {\n    win = window;\n} else if (typeof global !== \"undefined\") {\n    win = global;\n} else if (typeof self !== \"undefined\") {\n    win = self;\n} else {\n    win = {};\n}\n\nmodule.exports = win;\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(5)))\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nvar g;\n\n// This works in non-strict mode\ng = function () {\n\treturn this;\n}();\n\ntry {\n\t// This works if eval is allowed (see CSP)\n\tg = g || Function(\"return this\")() || (1, eval)(\"this\");\n} catch (e) {\n\t// This works if the window reference is available\n\tif ((typeof window === \"undefined\" ? \"undefined\" : _typeof(window)) === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\nmodule.exports = __webpack_require__(7);\n\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _videoWorker = __webpack_require__(8);\n\nvar _videoWorker2 = _interopRequireDefault(_videoWorker);\n\nvar _global = __webpack_require__(4);\n\nvar _global2 = _interopRequireDefault(_global);\n\nvar _liteReady = __webpack_require__(2);\n\nvar _liteReady2 = _interopRequireDefault(_liteReady);\n\nvar _jarallaxVideo = __webpack_require__(10);\n\nvar _jarallaxVideo2 = _interopRequireDefault(_jarallaxVideo);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// add video worker globally to fallback jarallax < 1.10 versions\n_global2.default.VideoWorker = _global2.default.VideoWorker || _videoWorker2.default;\n\n(0, _jarallaxVideo2.default)();\n\n// data-jarallax-video initialization\n(0, _liteReady2.default)(function () {\n    if (typeof jarallax !== 'undefined') {\n        jarallax(document.querySelectorAll('[data-jarallax-video]'));\n    }\n});\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nmodule.exports = __webpack_require__(9);\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n// Deferred\n// thanks http://stackoverflow.com/questions/18096715/implement-deferred-object-without-using-jquery\nfunction Deferred() {\n    this._done = [];\n    this._fail = [];\n}\nDeferred.prototype = {\n    execute: function execute(list, args) {\n        var i = list.length;\n        args = Array.prototype.slice.call(args);\n        while (i--) {\n            list[i].apply(null, args);\n        }\n    },\n    resolve: function resolve() {\n        this.execute(this._done, arguments);\n    },\n    reject: function reject() {\n        this.execute(this._fail, arguments);\n    },\n    done: function done(callback) {\n        this._done.push(callback);\n    },\n    fail: function fail(callback) {\n        this._fail.push(callback);\n    }\n};\n\nvar ID = 0;\nvar YoutubeAPIadded = 0;\nvar VimeoAPIadded = 0;\nvar loadingYoutubePlayer = 0;\nvar loadingVimeoPlayer = 0;\nvar loadingYoutubeDefer = new Deferred();\nvar loadingVimeoDefer = new Deferred();\n\nvar VideoWorker = function () {\n    function VideoWorker(url, options) {\n        _classCallCheck(this, VideoWorker);\n\n        var self = this;\n\n        self.url = url;\n\n        self.options_default = {\n            autoplay: false,\n            loop: false,\n            mute: false,\n            volume: 100,\n            showContols: true,\n\n            // start / end video time in seconds\n            startTime: 0,\n            endTime: 0\n        };\n\n        self.options = self.extend({}, self.options_default, options);\n\n        // check URL\n        self.videoID = self.parseURL(url);\n\n        // init\n        if (self.videoID) {\n            self.ID = ID++;\n            self.loadAPI();\n            self.init();\n        }\n    }\n\n    // Extend like jQuery.extend\n\n\n    _createClass(VideoWorker, [{\n        key: 'extend',\n        value: function extend(out) {\n            var _arguments = arguments;\n\n            out = out || {};\n            Object.keys(arguments).forEach(function (i) {\n                if (!_arguments[i]) {\n                    return;\n                }\n                Object.keys(_arguments[i]).forEach(function (key) {\n                    out[key] = _arguments[i][key];\n                });\n            });\n            return out;\n        }\n    }, {\n        key: 'parseURL',\n        value: function parseURL(url) {\n            // parse youtube ID\n            function getYoutubeID(ytUrl) {\n                // eslint-disable-next-line no-useless-escape\n                var regExp = /.*(?:youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=)([^#\\&\\?]*).*/;\n                var match = ytUrl.match(regExp);\n                return match && match[1].length === 11 ? match[1] : false;\n            }\n\n            // parse vimeo ID\n            function getVimeoID(vmUrl) {\n                // eslint-disable-next-line no-useless-escape\n                var regExp = /https?:\\/\\/(?:www\\.|player\\.)?vimeo.com\\/(?:channels\\/(?:\\w+\\/)?|groups\\/([^\\/]*)\\/videos\\/|album\\/(\\d+)\\/video\\/|video\\/|)(\\d+)(?:$|\\/|\\?)/;\n                var match = vmUrl.match(regExp);\n                return match && match[3] ? match[3] : false;\n            }\n\n            // parse local string\n            function getLocalVideos(locUrl) {\n                // eslint-disable-next-line no-useless-escape\n                var videoFormats = locUrl.split(/,(?=mp4\\:|webm\\:|ogv\\:|ogg\\:)/);\n                var result = {};\n                var ready = 0;\n                videoFormats.forEach(function (val) {\n                    // eslint-disable-next-line no-useless-escape\n                    var match = val.match(/^(mp4|webm|ogv|ogg)\\:(.*)/);\n                    if (match && match[1] && match[2]) {\n                        // eslint-disable-next-line prefer-destructuring\n                        result[match[1] === 'ogv' ? 'ogg' : match[1]] = match[2];\n                        ready = 1;\n                    }\n                });\n                return ready ? result : false;\n            }\n\n            var Youtube = getYoutubeID(url);\n            var Vimeo = getVimeoID(url);\n            var Local = getLocalVideos(url);\n\n            if (Youtube) {\n                this.type = 'youtube';\n                return Youtube;\n            } else if (Vimeo) {\n                this.type = 'vimeo';\n                return Vimeo;\n            } else if (Local) {\n                this.type = 'local';\n                return Local;\n            }\n\n            return false;\n        }\n    }, {\n        key: 'isValid',\n        value: function isValid() {\n            return !!this.videoID;\n        }\n\n        // events\n\n    }, {\n        key: 'on',\n        value: function on(name, callback) {\n            this.userEventsList = this.userEventsList || [];\n\n            // add new callback in events list\n            (this.userEventsList[name] || (this.userEventsList[name] = [])).push(callback);\n        }\n    }, {\n        key: 'off',\n        value: function off(name, callback) {\n            var _this = this;\n\n            if (!this.userEventsList || !this.userEventsList[name]) {\n                return;\n            }\n\n            if (!callback) {\n                delete this.userEventsList[name];\n            } else {\n                this.userEventsList[name].forEach(function (val, key) {\n                    if (val === callback) {\n                        _this.userEventsList[name][key] = false;\n                    }\n                });\n            }\n        }\n    }, {\n        key: 'fire',\n        value: function fire(name) {\n            var _this2 = this;\n\n            var args = [].slice.call(arguments, 1);\n            if (this.userEventsList && typeof this.userEventsList[name] !== 'undefined') {\n                this.userEventsList[name].forEach(function (val) {\n                    // call with all arguments\n                    if (val) {\n                        val.apply(_this2, args);\n                    }\n                });\n            }\n        }\n    }, {\n        key: 'play',\n        value: function play(start) {\n            var self = this;\n            if (!self.player) {\n                return;\n            }\n\n            if (self.type === 'youtube' && self.player.playVideo) {\n                if (typeof start !== 'undefined') {\n                    self.player.seekTo(start || 0);\n                }\n                if (YT.PlayerState.PLAYING !== self.player.getPlayerState()) {\n                    self.player.playVideo();\n                }\n            }\n\n            if (self.type === 'vimeo') {\n                if (typeof start !== 'undefined') {\n                    self.player.setCurrentTime(start);\n                }\n                self.player.getPaused().then(function (paused) {\n                    if (paused) {\n                        self.player.play();\n                    }\n                });\n            }\n\n            if (self.type === 'local') {\n                if (typeof start !== 'undefined') {\n                    self.player.currentTime = start;\n                }\n                if (self.player.paused) {\n                    self.player.play();\n                }\n            }\n        }\n    }, {\n        key: 'pause',\n        value: function pause() {\n            var self = this;\n            if (!self.player) {\n                return;\n            }\n\n            if (self.type === 'youtube' && self.player.pauseVideo) {\n                if (YT.PlayerState.PLAYING === self.player.getPlayerState()) {\n                    self.player.pauseVideo();\n                }\n            }\n\n            if (self.type === 'vimeo') {\n                self.player.getPaused().then(function (paused) {\n                    if (!paused) {\n                        self.player.pause();\n                    }\n                });\n            }\n\n            if (self.type === 'local') {\n                if (!self.player.paused) {\n                    self.player.pause();\n                }\n            }\n        }\n    }, {\n        key: 'mute',\n        value: function mute() {\n            var self = this;\n            if (!self.player) {\n                return;\n            }\n\n            if (self.type === 'youtube' && self.player.mute) {\n                self.player.mute();\n            }\n\n            if (self.type === 'vimeo' && self.player.setVolume) {\n                self.player.setVolume(0);\n            }\n\n            if (self.type === 'local') {\n                self.$video.muted = true;\n            }\n        }\n    }, {\n        key: 'unmute',\n        value: function unmute() {\n            var self = this;\n            if (!self.player) {\n                return;\n            }\n\n            if (self.type === 'youtube' && self.player.mute) {\n                self.player.unMute();\n            }\n\n            if (self.type === 'vimeo' && self.player.setVolume) {\n                self.player.setVolume(self.options.volume);\n            }\n\n            if (self.type === 'local') {\n                self.$video.muted = false;\n            }\n        }\n    }, {\n        key: 'setVolume',\n        value: function setVolume() {\n            var volume = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n\n            var self = this;\n            if (!self.player || !volume) {\n                return;\n            }\n\n            if (self.type === 'youtube' && self.player.setVolume) {\n                self.player.setVolume(volume);\n            }\n\n            if (self.type === 'vimeo' && self.player.setVolume) {\n                self.player.setVolume(volume);\n            }\n\n            if (self.type === 'local') {\n                self.$video.volume = volume / 100;\n            }\n        }\n    }, {\n        key: 'getVolume',\n        value: function getVolume(callback) {\n            var self = this;\n            if (!self.player) {\n                callback(false);\n                return;\n            }\n\n            if (self.type === 'youtube' && self.player.getVolume) {\n                callback(self.player.getVolume());\n            }\n\n            if (self.type === 'vimeo' && self.player.getVolume) {\n                self.player.getVolume().then(function (volume) {\n                    callback(volume);\n                });\n            }\n\n            if (self.type === 'local') {\n                callback(self.$video.volume * 100);\n            }\n        }\n    }, {\n        key: 'getMuted',\n        value: function getMuted(callback) {\n            var self = this;\n            if (!self.player) {\n                callback(null);\n                return;\n            }\n\n            if (self.type === 'youtube' && self.player.isMuted) {\n                callback(self.player.isMuted());\n            }\n\n            if (self.type === 'vimeo' && self.player.getVolume) {\n                self.player.getVolume().then(function (volume) {\n                    callback(!!volume);\n                });\n            }\n\n            if (self.type === 'local') {\n                callback(self.$video.muted);\n            }\n        }\n    }, {\n        key: 'getImageURL',\n        value: function getImageURL(callback) {\n            var self = this;\n\n            if (self.videoImage) {\n                callback(self.videoImage);\n                return;\n            }\n\n            if (self.type === 'youtube') {\n                var availableSizes = ['maxresdefault', 'sddefault', 'hqdefault', '0'];\n                var step = 0;\n\n                var tempImg = new Image();\n                tempImg.onload = function () {\n                    // if no thumbnail, youtube add their own image with width = 120px\n                    if ((this.naturalWidth || this.width) !== 120 || step === availableSizes.length - 1) {\n                        // ok\n                        self.videoImage = 'https://img.youtube.com/vi/' + self.videoID + '/' + availableSizes[step] + '.jpg';\n                        callback(self.videoImage);\n                    } else {\n                        // try another size\n                        step++;\n                        this.src = 'https://img.youtube.com/vi/' + self.videoID + '/' + availableSizes[step] + '.jpg';\n                    }\n                };\n                tempImg.src = 'https://img.youtube.com/vi/' + self.videoID + '/' + availableSizes[step] + '.jpg';\n            }\n\n            if (self.type === 'vimeo') {\n                var request = new XMLHttpRequest();\n                request.open('GET', 'https://vimeo.com/api/v2/video/' + self.videoID + '.json', true);\n                request.onreadystatechange = function () {\n                    if (this.readyState === 4) {\n                        if (this.status >= 200 && this.status < 400) {\n                            // Success!\n                            var response = JSON.parse(this.responseText);\n                            self.videoImage = response[0].thumbnail_large;\n                            callback(self.videoImage);\n                        } else {\n                            // Error :(\n                        }\n                    }\n                };\n                request.send();\n                request = null;\n            }\n        }\n\n        // fallback to the old version.\n\n    }, {\n        key: 'getIframe',\n        value: function getIframe(callback) {\n            this.getVideo(callback);\n        }\n    }, {\n        key: 'getVideo',\n        value: function getVideo(callback) {\n            var self = this;\n\n            // return generated video block\n            if (self.$video) {\n                callback(self.$video);\n                return;\n            }\n\n            // generate new video block\n            self.onAPIready(function () {\n                var hiddenDiv = void 0;\n                if (!self.$video) {\n                    hiddenDiv = document.createElement('div');\n                    hiddenDiv.style.display = 'none';\n                }\n\n                // Youtube\n                if (self.type === 'youtube') {\n                    self.playerOptions = {};\n                    self.playerOptions.videoId = self.videoID;\n                    self.playerOptions.playerVars = {\n                        autohide: 1,\n                        rel: 0,\n                        autoplay: 0,\n                        // autoplay enable on mobile devices\n                        playsinline: 1\n                    };\n\n                    // hide controls\n                    if (!self.options.showContols) {\n                        self.playerOptions.playerVars.iv_load_policy = 3;\n                        self.playerOptions.playerVars.modestbranding = 1;\n                        self.playerOptions.playerVars.controls = 0;\n                        self.playerOptions.playerVars.showinfo = 0;\n                        self.playerOptions.playerVars.disablekb = 1;\n                    }\n\n                    // events\n                    var ytStarted = void 0;\n                    var ytProgressInterval = void 0;\n                    self.playerOptions.events = {\n                        onReady: function onReady(e) {\n                            // mute\n                            if (self.options.mute) {\n                                e.target.mute();\n                            } else if (self.options.volume) {\n                                e.target.setVolume(self.options.volume);\n                            }\n\n                            // autoplay\n                            if (self.options.autoplay) {\n                                self.play(self.options.startTime);\n                            }\n                            self.fire('ready', e);\n\n                            // For seamless loops, set the endTime to 0.1 seconds less than the video's duration\n                            // https://github.com/nk-o/video-worker/issues/2\n                            if (self.options.loop && !self.options.endTime) {\n                                var secondsOffset = 0.1;\n                                self.options.endTime = self.player.getDuration() - secondsOffset;\n                            }\n\n                            // volumechange\n                            setInterval(function () {\n                                self.getVolume(function (volume) {\n                                    if (self.options.volume !== volume) {\n                                        self.options.volume = volume;\n                                        self.fire('volumechange', e);\n                                    }\n                                });\n                            }, 150);\n                        },\n                        onStateChange: function onStateChange(e) {\n                            // loop\n                            if (self.options.loop && e.data === YT.PlayerState.ENDED) {\n                                self.play(self.options.startTime);\n                            }\n                            if (!ytStarted && e.data === YT.PlayerState.PLAYING) {\n                                ytStarted = 1;\n                                self.fire('started', e);\n                            }\n                            if (e.data === YT.PlayerState.PLAYING) {\n                                self.fire('play', e);\n                            }\n                            if (e.data === YT.PlayerState.PAUSED) {\n                                self.fire('pause', e);\n                            }\n                            if (e.data === YT.PlayerState.ENDED) {\n                                self.fire('ended', e);\n                            }\n\n                            // progress check\n                            if (e.data === YT.PlayerState.PLAYING) {\n                                ytProgressInterval = setInterval(function () {\n                                    self.fire('timeupdate', e);\n\n                                    // check for end of video and play again or stop\n                                    if (self.options.endTime && self.player.getCurrentTime() >= self.options.endTime) {\n                                        if (self.options.loop) {\n                                            self.play(self.options.startTime);\n                                        } else {\n                                            self.pause();\n                                        }\n                                    }\n                                }, 150);\n                            } else {\n                                clearInterval(ytProgressInterval);\n                            }\n                        }\n                    };\n\n                    var firstInit = !self.$video;\n                    if (firstInit) {\n                        var div = document.createElement('div');\n                        div.setAttribute('id', self.playerID);\n                        hiddenDiv.appendChild(div);\n                        document.body.appendChild(hiddenDiv);\n                    }\n                    self.player = self.player || new window.YT.Player(self.playerID, self.playerOptions);\n                    if (firstInit) {\n                        self.$video = document.getElementById(self.playerID);\n\n                        // get video width and height\n                        self.videoWidth = parseInt(self.$video.getAttribute('width'), 10) || 1280;\n                        self.videoHeight = parseInt(self.$video.getAttribute('height'), 10) || 720;\n                    }\n                }\n\n                // Vimeo\n                if (self.type === 'vimeo') {\n                    self.playerOptions = {\n                        id: self.videoID,\n                        autopause: 0,\n                        transparent: 0,\n                        autoplay: self.options.autoplay ? 1 : 0,\n                        loop: self.options.loop ? 1 : 0,\n                        muted: self.options.mute ? 1 : 0\n                    };\n\n                    if (self.options.volume) {\n                        self.playerOptions.volume = self.options.volume;\n                    }\n\n                    // hide controls\n                    if (!self.options.showContols) {\n                        self.playerOptions.badge = 0;\n                        self.playerOptions.byline = 0;\n                        self.playerOptions.portrait = 0;\n                        self.playerOptions.title = 0;\n                    }\n\n                    if (!self.$video) {\n                        var playerOptionsString = '';\n                        Object.keys(self.playerOptions).forEach(function (key) {\n                            if (playerOptionsString !== '') {\n                                playerOptionsString += '&';\n                            }\n                            playerOptionsString += key + '=' + encodeURIComponent(self.playerOptions[key]);\n                        });\n\n                        // we need to create iframe manually because when we create it using API\n                        // js events won't triggers after iframe moved to another place\n                        self.$video = document.createElement('iframe');\n                        self.$video.setAttribute('id', self.playerID);\n                        self.$video.setAttribute('src', 'https://player.vimeo.com/video/' + self.videoID + '?' + playerOptionsString);\n                        self.$video.setAttribute('frameborder', '0');\n                        self.$video.setAttribute('mozallowfullscreen', '');\n                        self.$video.setAttribute('allowfullscreen', '');\n\n                        hiddenDiv.appendChild(self.$video);\n                        document.body.appendChild(hiddenDiv);\n                    }\n                    self.player = self.player || new Vimeo.Player(self.$video, self.playerOptions);\n\n                    // set current time for autoplay\n                    if (self.options.startTime && self.options.autoplay) {\n                        self.player.setCurrentTime(self.options.startTime);\n                    }\n\n                    // get video width and height\n                    self.player.getVideoWidth().then(function (width) {\n                        self.videoWidth = width || 1280;\n                    });\n                    self.player.getVideoHeight().then(function (height) {\n                        self.videoHeight = height || 720;\n                    });\n\n                    // events\n                    var vmStarted = void 0;\n                    self.player.on('timeupdate', function (e) {\n                        if (!vmStarted) {\n                            self.fire('started', e);\n                            vmStarted = 1;\n                        }\n\n                        self.fire('timeupdate', e);\n\n                        // check for end of video and play again or stop\n                        if (self.options.endTime) {\n                            if (self.options.endTime && e.seconds >= self.options.endTime) {\n                                if (self.options.loop) {\n                                    self.play(self.options.startTime);\n                                } else {\n                                    self.pause();\n                                }\n                            }\n                        }\n                    });\n                    self.player.on('play', function (e) {\n                        self.fire('play', e);\n\n                        // check for the start time and start with it\n                        if (self.options.startTime && e.seconds === 0) {\n                            self.play(self.options.startTime);\n                        }\n                    });\n                    self.player.on('pause', function (e) {\n                        self.fire('pause', e);\n                    });\n                    self.player.on('ended', function (e) {\n                        self.fire('ended', e);\n                    });\n                    self.player.on('loaded', function (e) {\n                        self.fire('ready', e);\n                    });\n                    self.player.on('volumechange', function (e) {\n                        self.fire('volumechange', e);\n                    });\n                }\n\n                // Local\n                function addSourceToLocal(element, src, type) {\n                    var source = document.createElement('source');\n                    source.src = src;\n                    source.type = type;\n                    element.appendChild(source);\n                }\n                if (self.type === 'local') {\n                    if (!self.$video) {\n                        self.$video = document.createElement('video');\n\n                        // show controls\n                        if (self.options.showContols) {\n                            self.$video.controls = true;\n                        }\n\n                        // mute\n                        if (self.options.mute) {\n                            self.$video.muted = true;\n                        } else if (self.$video.volume) {\n                            self.$video.volume = self.options.volume / 100;\n                        }\n\n                        // loop\n                        if (self.options.loop) {\n                            self.$video.loop = true;\n                        }\n\n                        // autoplay enable on mobile devices\n                        self.$video.setAttribute('playsinline', '');\n                        self.$video.setAttribute('webkit-playsinline', '');\n\n                        self.$video.setAttribute('id', self.playerID);\n                        hiddenDiv.appendChild(self.$video);\n                        document.body.appendChild(hiddenDiv);\n\n                        Object.keys(self.videoID).forEach(function (key) {\n                            addSourceToLocal(self.$video, self.videoID[key], 'video/' + key);\n                        });\n                    }\n\n                    self.player = self.player || self.$video;\n\n                    var locStarted = void 0;\n                    self.player.addEventListener('playing', function (e) {\n                        if (!locStarted) {\n                            self.fire('started', e);\n                        }\n                        locStarted = 1;\n                    });\n                    self.player.addEventListener('timeupdate', function (e) {\n                        self.fire('timeupdate', e);\n\n                        // check for end of video and play again or stop\n                        if (self.options.endTime) {\n                            if (self.options.endTime && this.currentTime >= self.options.endTime) {\n                                if (self.options.loop) {\n                                    self.play(self.options.startTime);\n                                } else {\n                                    self.pause();\n                                }\n                            }\n                        }\n                    });\n                    self.player.addEventListener('play', function (e) {\n                        self.fire('play', e);\n                    });\n                    self.player.addEventListener('pause', function (e) {\n                        self.fire('pause', e);\n                    });\n                    self.player.addEventListener('ended', function (e) {\n                        self.fire('ended', e);\n                    });\n                    self.player.addEventListener('loadedmetadata', function () {\n                        // get video width and height\n                        self.videoWidth = this.videoWidth || 1280;\n                        self.videoHeight = this.videoHeight || 720;\n\n                        self.fire('ready');\n\n                        // autoplay\n                        if (self.options.autoplay) {\n                            self.play(self.options.startTime);\n                        }\n                    });\n                    self.player.addEventListener('volumechange', function (e) {\n                        self.getVolume(function (volume) {\n                            self.options.volume = volume;\n                        });\n                        self.fire('volumechange', e);\n                    });\n                }\n                callback(self.$video);\n            });\n        }\n    }, {\n        key: 'init',\n        value: function init() {\n            var self = this;\n\n            self.playerID = 'VideoWorker-' + self.ID;\n        }\n    }, {\n        key: 'loadAPI',\n        value: function loadAPI() {\n            var self = this;\n\n            if (YoutubeAPIadded && VimeoAPIadded) {\n                return;\n            }\n\n            var src = '';\n\n            // load Youtube API\n            if (self.type === 'youtube' && !YoutubeAPIadded) {\n                YoutubeAPIadded = 1;\n                src = 'https://www.youtube.com/iframe_api';\n            }\n\n            // load Vimeo API\n            if (self.type === 'vimeo' && !VimeoAPIadded) {\n                VimeoAPIadded = 1;\n                src = 'https://player.vimeo.com/api/player.js';\n            }\n\n            if (!src) {\n                return;\n            }\n\n            // add script in head section\n            var tag = document.createElement('script');\n            var head = document.getElementsByTagName('head')[0];\n            tag.src = src;\n\n            head.appendChild(tag);\n\n            head = null;\n            tag = null;\n        }\n    }, {\n        key: 'onAPIready',\n        value: function onAPIready(callback) {\n            var self = this;\n\n            // Youtube\n            if (self.type === 'youtube') {\n                // Listen for global YT player callback\n                if ((typeof YT === 'undefined' || YT.loaded === 0) && !loadingYoutubePlayer) {\n                    // Prevents Ready event from being called twice\n                    loadingYoutubePlayer = 1;\n\n                    // Creates deferred so, other players know when to wait.\n                    window.onYouTubeIframeAPIReady = function () {\n                        window.onYouTubeIframeAPIReady = null;\n                        loadingYoutubeDefer.resolve('done');\n                        callback();\n                    };\n                } else if ((typeof YT === 'undefined' ? 'undefined' : _typeof(YT)) === 'object' && YT.loaded === 1) {\n                    callback();\n                } else {\n                    loadingYoutubeDefer.done(function () {\n                        callback();\n                    });\n                }\n            }\n\n            // Vimeo\n            if (self.type === 'vimeo') {\n                if (typeof Vimeo === 'undefined' && !loadingVimeoPlayer) {\n                    loadingVimeoPlayer = 1;\n                    var vimeoInterval = setInterval(function () {\n                        if (typeof Vimeo !== 'undefined') {\n                            clearInterval(vimeoInterval);\n                            loadingVimeoDefer.resolve('done');\n                            callback();\n                        }\n                    }, 20);\n                } else if (typeof Vimeo !== 'undefined') {\n                    callback();\n                } else {\n                    loadingVimeoDefer.done(function () {\n                        callback();\n                    });\n                }\n            }\n\n            // Local\n            if (self.type === 'local') {\n                callback();\n            }\n        }\n    }]);\n\n    return VideoWorker;\n}();\n\nexports.default = VideoWorker;\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.default = jarallaxVideo;\n\nvar _videoWorker = __webpack_require__(8);\n\nvar _videoWorker2 = _interopRequireDefault(_videoWorker);\n\nvar _global = __webpack_require__(4);\n\nvar _global2 = _interopRequireDefault(_global);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction jarallaxVideo() {\n    var jarallax = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _global2.default.jarallax;\n\n    if (typeof jarallax === 'undefined') {\n        return;\n    }\n\n    var Jarallax = jarallax.constructor;\n\n    // append video after init Jarallax\n    var defInit = Jarallax.prototype.init;\n    Jarallax.prototype.init = function () {\n        var self = this;\n\n        defInit.apply(self);\n\n        if (self.video && !self.options.disableVideo()) {\n            self.video.getVideo(function (video) {\n                var $parent = video.parentNode;\n                self.css(video, {\n                    position: self.image.position,\n                    top: '0px',\n                    left: '0px',\n                    right: '0px',\n                    bottom: '0px',\n                    width: '100%',\n                    height: '100%',\n                    maxWidth: 'none',\n                    maxHeight: 'none',\n                    margin: 0,\n                    zIndex: -1\n                });\n                self.$video = video;\n                self.image.$container.appendChild(video);\n\n                // remove parent video element (created by VideoWorker)\n                $parent.parentNode.removeChild($parent);\n            });\n        }\n    };\n\n    // cover video\n    var defCoverImage = Jarallax.prototype.coverImage;\n    Jarallax.prototype.coverImage = function () {\n        var self = this;\n        var imageData = defCoverImage.apply(self);\n        var node = self.image.$item ? self.image.$item.nodeName : false;\n\n        if (imageData && self.video && node && (node === 'IFRAME' || node === 'VIDEO')) {\n            var h = imageData.image.height;\n            var w = h * self.image.width / self.image.height;\n            var ml = (imageData.container.width - w) / 2;\n            var mt = imageData.image.marginTop;\n\n            if (imageData.container.width > w) {\n                w = imageData.container.width;\n                h = w * self.image.height / self.image.width;\n                ml = 0;\n                mt += (imageData.image.height - h) / 2;\n            }\n\n            // add video height over than need to hide controls\n            if (node === 'IFRAME') {\n                h += 400;\n                mt -= 200;\n            }\n\n            self.css(self.$video, {\n                width: w + 'px',\n                marginLeft: ml + 'px',\n                height: h + 'px',\n                marginTop: mt + 'px'\n            });\n        }\n\n        return imageData;\n    };\n\n    // init video\n    var defInitImg = Jarallax.prototype.initImg;\n    Jarallax.prototype.initImg = function () {\n        var self = this;\n        var defaultResult = defInitImg.apply(self);\n\n        if (!self.options.videoSrc) {\n            self.options.videoSrc = self.$item.getAttribute('data-jarallax-video') || null;\n        }\n\n        if (self.options.videoSrc) {\n            self.defaultInitImgResult = defaultResult;\n            return true;\n        }\n\n        return defaultResult;\n    };\n\n    var defCanInitParallax = Jarallax.prototype.canInitParallax;\n    Jarallax.prototype.canInitParallax = function () {\n        var self = this;\n        var defaultResult = defCanInitParallax.apply(self);\n\n        if (!self.options.videoSrc) {\n            return defaultResult;\n        }\n\n        var video = new _videoWorker2.default(self.options.videoSrc, {\n            autoplay: true,\n            loop: self.options.videoLoop,\n            showContols: false,\n            startTime: self.options.videoStartTime || 0,\n            endTime: self.options.videoEndTime || 0,\n            mute: self.options.videoVolume ? 0 : 1,\n            volume: self.options.videoVolume || 0\n        });\n\n        if (video.isValid()) {\n            // if parallax will not be inited, we can add thumbnail on background.\n            if (!defaultResult) {\n                if (!self.defaultInitImgResult) {\n                    video.getImageURL(function (url) {\n                        // save default user styles\n                        var curStyle = self.$item.getAttribute('style');\n                        if (curStyle) {\n                            self.$item.setAttribute('data-jarallax-original-styles', curStyle);\n                        }\n\n                        // set new background\n                        self.css(self.$item, {\n                            'background-image': 'url(\"' + url + '\")',\n                            'background-position': 'center',\n                            'background-size': 'cover'\n                        });\n                    });\n                }\n\n                // init video\n            } else {\n                video.on('ready', function () {\n                    if (self.options.videoPlayOnlyVisible) {\n                        var oldOnScroll = self.onScroll;\n                        self.onScroll = function () {\n                            oldOnScroll.apply(self);\n                            if (self.options.videoLoop || !self.options.videoLoop && !self.videoEnded) {\n                                if (self.isVisible()) {\n                                    video.play();\n                                } else {\n                                    video.pause();\n                                }\n                            }\n                        };\n                    } else {\n                        video.play();\n                    }\n                });\n\n                video.on('started', function () {\n                    self.image.$default_item = self.image.$item;\n                    self.image.$item = self.$video;\n\n                    // set video width and height\n                    self.image.width = self.video.videoWidth || 1280;\n                    self.image.height = self.video.videoHeight || 720;\n                    self.coverImage();\n                    self.clipContainer();\n                    self.onScroll();\n\n                    // hide image\n                    if (self.image.$default_item) {\n                        self.image.$default_item.style.display = 'none';\n                    }\n                });\n\n                video.on('ended', function () {\n                    self.videoEnded = true;\n\n                    if (!self.options.videoLoop) {\n                        // show image if Loop disabled\n                        if (self.image.$default_item) {\n                            self.image.$item = self.image.$default_item;\n                            self.image.$item.style.display = 'block';\n\n                            // set image width and height\n                            self.coverImage();\n                            self.clipContainer();\n                            self.onScroll();\n                        }\n                    }\n                });\n\n                self.video = video;\n\n                // set image if not exists\n                if (!self.defaultInitImgResult) {\n                    if (video.type !== 'local') {\n                        video.getImageURL(function (url) {\n                            self.image.src = url;\n                            self.init();\n                        });\n\n                        return false;\n                    }\n\n                    // set empty image on local video if not defined\n                    self.image.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';\n                    return true;\n                }\n            }\n        }\n\n        return defaultResult;\n    };\n\n    // Destroy video parallax\n    var defDestroy = Jarallax.prototype.destroy;\n    Jarallax.prototype.destroy = function () {\n        var self = this;\n\n        if (self.image.$default_item) {\n            self.image.$item = self.image.$default_item;\n            delete self.image.$default_item;\n        }\n\n        defDestroy.apply(self);\n    };\n}\n\n/***/ })\n/******/ ]);"]}