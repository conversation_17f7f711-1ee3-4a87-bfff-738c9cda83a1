.irs {
  position: relative;
  display: block;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.irs-line {
  position: relative;
  display: block;
  overflow: hidden;
  outline: none !important;
}
.irs-line-left,
.irs-line-mid,
.irs-line-right {
  position: absolute;
  display: block;
  top: 0;
}
.irs-line-left {
  left: 0;
  width: 11%;
}
.irs-line-mid {
  left: 9%;
  width: 82%;
}
.irs-line-right {
  right: 0;
  width: 11%;
}
.irs-bar {
  position: absolute;
  display: block;
  left: 0;
  width: 0;
}
.irs-bar-edge {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  border-radius: 5px 0 0 5px;
}
.irs-shadow {
  position: absolute;
  display: none;
  left: 0;
  width: 0;
}
.irs-slider {
  position: absolute;
  display: block;
  cursor: pointer;
  z-index: 1;
}
.irs-slider.type_last {
  z-index: 2;
}
.irs-min {
  position: absolute;
  display: block;
  left: 0;
  cursor: default;
}
.irs-max {
  position: absolute;
  display: block;
  right: 0;
  cursor: default;
}
.irs-from,
.irs-to,
.irs-single {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  cursor: default;
  white-space: nowrap;
}
.irs-grid {
  position: absolute;
  display: none;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 20px;
}
.irs-with-grid .irs-grid {
  display: block;
}
.irs-grid-pol {
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 8px;
  background: #000;
}
.irs-grid-pol.small {
  height: 4px;
}
.irs-grid-text {
  position: absolute;
  bottom: 0;
  left: 0;
  white-space: nowrap;
  text-align: center;
  font-size: 9px;
  line-height: 9px;
  padding: 0 3px;
  color: #000;
}
.irs-disable-mask {
  position: absolute;
  display: block;
  top: 0;
  left: -1%;
  width: 102%;
  height: 100%;
  cursor: default;
  background: rgba(0, 0, 0, 0);
  z-index: 2;
}
.irs-disabled {
  opacity: 0.4;
}
.lt-ie9 .irs-disabled {
  filter: alpha(opacity=40);
}
.irs-hidden-input {
  position: absolute !important;
  display: block !important;
  top: 0 !important;
  left: 0 !important;
  width: 0 !important;
  height: 0 !important;
  font-size: 0 !important;
  line-height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  outline: none !important;
  z-index: -9999 !important;
  background: none !important;
  border-style: solid !important;
  border-color: transparent !important;
}
.bfb-ionslider-flat .irs-line-mid,
.bfb-ionslider-flat .irs-line-left,
.bfb-ionslider-flat .irs-line-right,
.bfb-ionslider-flat .irs-bar,
.bfb-ionslider-flat .irs-bar-edge {
  background: url('../images/sprite-skin-flat.png') repeat-x;
}
.bfb-ionslider-flat .irs {
  height: 40px;
}
.bfb-ionslider-flat .irs-with-grid {
  height: 60px;
}
.bfb-ionslider-flat .irs-line {
  height: 12px;
  top: 25px;
}
.bfb-ionslider-flat .irs-line-left {
  height: 12px;
  background-position: 0 -30px;
}
.bfb-ionslider-flat .irs-line-mid {
  height: 12px;
  background-position: 0 0;
}
.bfb-ionslider-flat .irs-line-right {
  height: 12px;
  background-position: 100% -30px;
}
.bfb-ionslider-flat .irs-bar {
  height: 12px;
  top: 25px;
  background-position: 0 -60px;
}
.bfb-ionslider-flat .irs-bar-edge {
  top: 25px;
  height: 12px;
  width: 9px;
  background-position: 0 -90px;
}
.bfb-ionslider-flat .irs-shadow {
  height: 3px;
  top: 34px;
  background: #000;
  opacity: 0.25;
}
.bfb-ionslider-flat .lt-ie9 .irs-shadow {
  filter: alpha(opacity=25);
}
.bfb-ionslider-flat .irs-slider {
  width: 16px;
  height: 18px;
  top: 22px;
  background-position: 0 -120px;
  text-align: center;
}
.bfb-ionslider-flat .irs-slider.state_hover,
.bfb-ionslider-flat .irs-slider:hover {
  background-position: 0 -150px;
}
.bfb-ionslider-flat .irs-min,
.bfb-ionslider-flat .irs-max {
  color: #999;
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  top: 0;
  padding: 1px 3px;
  background: #e1e4e9;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.bfb-ionslider-flat .irs-from,
.bfb-ionslider-flat .irs-to,
.bfb-ionslider-flat .irs-single {
  color: #fff !important;
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  padding: 1px 5px;
  background: #ed5565;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.bfb-ionslider-flat .irs-from:after,
.bfb-ionslider-flat .irs-to:after,
.bfb-ionslider-flat .irs-single:after {
  position: absolute;
  display: block;
  content: "";
  bottom: -6px;
  left: 50%;
  width: 0;
  height: 0;
  margin-left: -3px;
  overflow: hidden;
  border: 3px solid transparent;
  border-top-color: #ed5565;
}
.bfb-ionslider-flat .irs-grid-pol {
  background: #e1e4e9;
}
.bfb-ionslider-flat .irs-grid-text {
  color: #999;
}
.bfb-ionslider-flat .irs-slider:before {
  content: '';
  position: absolute;
  left: auto;
  right: auto;
  width: 2px;
  background: #ed5565;
  height: 100%;
}
.bfb-ionslider-modern .irs-line-mid,
.bfb-ionslider-modern .irs-line-left,
.bfb-ionslider-modern .irs-line-right,
.bfb-ionslider-modern .irs-bar,
.bfb-ionslider-modern .irs-bar-edge,
.bfb-ionslider-modern .irs-slider {
  background: url('../images/sprite-skin-modern.png') repeat-x;
}
.bfb-ionslider-modern .irs {
  height: 50px;
}
.bfb-ionslider-modern .irs-with-grid {
  height: 70px;
}
.bfb-ionslider-modern .irs-line {
  height: 6px;
  top: 25px;
}
.bfb-ionslider-modern .irs-line-left {
  height: 6px;
  background-position: 0 -30px;
}
.bfb-ionslider-modern .irs-line-mid {
  height: 6px;
  background-position: 0 0;
}
.bfb-ionslider-modern .irs-line-right {
  height: 6px;
  background-position: 100% -30px;
}
.bfb-ionslider-modern .irs-bar {
  height: 6px;
  top: 25px;
  background-position: 0 -60px;
}
.bfb-ionslider-modern .irs-bar-edge {
  top: 25px;
  height: 6px;
  width: 6px;
  background-position: 0 -90px;
}
.bfb-ionslider-modern .irs-shadow {
  height: 5px;
  top: 25px;
  background: #000;
  opacity: 0.25;
}
.bfb-ionslider-modern .lt-ie9 .irs-shadow {
  filter: alpha(opacity=25);
}
.bfb-ionslider-modern .irs-slider {
  width: 11px;
  height: 18px;
  top: 31px;
  background-position: 0 -120px;
}
.bfb-ionslider-modern .irs-slider.state_hover,
.bfb-ionslider-modern .irs-slider:hover {
  background-position: 0 -150px;
}
.bfb-ionslider-modern .irs-min,
.bfb-ionslider-modern .irs-max {
  color: #999;
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  top: 0;
  padding: 1px 3px;
  background: #e1e4e9;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.bfb-ionslider-modern .irs-from,
.bfb-ionslider-modern .irs-to,
.bfb-ionslider-modern .irs-single {
  color: #fff !important;
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  padding: 1px 5px;
  background: #20b426;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.bfb-ionslider-modern .irs-from:after,
.bfb-ionslider-modern .irs-to:after,
.bfb-ionslider-modern .irs-single:after {
  position: absolute;
  display: block;
  content: "";
  bottom: -6px;
  left: 50%;
  width: 0;
  height: 0;
  margin-left: -3px;
  overflow: hidden;
  border: 3px solid transparent;
  border-top-color: #20b426;
}
.bfb-ionslider-modern .irs-grid {
  height: 34px;
}
.bfb-ionslider-modern .irs-grid-pol {
  background: #c0c0c0;
}
.bfb-ionslider-modern .irs-grid-text {
  bottom: 12px;
  color: #c0c0c0;
}
.bfb-ionslider-html5 .irs {
  height: 55px;
}
.bfb-ionslider-html5 .irs-with-grid {
  height: 75px;
}
.bfb-ionslider-html5 .irs-line {
  height: 10px;
  top: 33px;
  background: #EEE;
  background: linear-gradient(to bottom, #DDD -50%, #FFF 150%);
  /* W3C */
  border: 1px solid #CCC;
  border-radius: 16px;
  -moz-border-radius: 16px;
}
.bfb-ionslider-html5 .irs-line-left {
  height: 8px;
}
.bfb-ionslider-html5 .irs-line-mid {
  height: 8px;
}
.bfb-ionslider-html5 .irs-line-right {
  height: 8px;
}
.bfb-ionslider-html5 .irs-bar {
  height: 10px;
  top: 33px;
  border-top: 1px solid #428bca;
  border-bottom: 1px solid #428bca;
  background: #428bca;
  background: linear-gradient(to top, #428bca 0%, #7fc3e8 100%);
  /* W3C */
}
.bfb-ionslider-html5 .irs-bar-edge {
  height: 10px;
  top: 33px;
  width: 14px;
  border: 1px solid #428bca;
  border-right: 0;
  background: #428bca;
  background: linear-gradient(to top, #428bca 0%, #7fc3e8 100%);
  /* W3C */
  border-radius: 16px 0 0 16px;
  -moz-border-radius: 16px 0 0 16px;
}
.bfb-ionslider-html5 .irs-shadow {
  height: 2px;
  top: 38px;
  background: #000;
  opacity: 0.3;
  border-radius: 5px;
  -moz-border-radius: 5px;
}
.bfb-ionslider-html5 .lt-ie9 .irs-shadow {
  filter: alpha(opacity=30);
}
.bfb-ionslider-html5 .irs-slider {
  top: 25px;
  width: 27px;
  height: 27px;
  border: 1px solid #AAA;
  background: #DDD;
  background: linear-gradient(to bottom, #ffffff 0%, #dcdcdc 20%, #ffffff 100%);
  /* W3C */
  border-radius: 27px;
  -moz-border-radius: 27px;
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
  cursor: pointer;
}
.bfb-ionslider-html5 .irs-slider.state_hover,
.bfb-ionslider-html5 .irs-slider:hover {
  background: #FFF;
}
.bfb-ionslider-html5 .irs-min,
.bfb-ionslider-html5 .irs-max {
  color: #333;
  font-size: 12px;
  line-height: 1.333;
  text-shadow: none;
  top: 0;
  padding: 1px 5px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  -moz-border-radius: 3px;
}
.bfb-ionslider-html5 .lt-ie9 .irs-min,
.bfb-ionslider-html5 .lt-ie9 .irs-max {
  background: #ccc;
}
.bfb-ionslider-html5 .irs-from,
.bfb-ionslider-html5 .irs-to,
.bfb-ionslider-html5 .irs-single {
  color: #fff !important;
  font-size: 14px;
  line-height: 1.333;
  text-shadow: none;
  padding: 1px 5px;
  background: #428bca;
  border-radius: 3px;
  -moz-border-radius: 3px;
}
.bfb-ionslider-html5 .lt-ie9 .irs-from,
.bfb-ionslider-html5 .lt-ie9 .irs-to,
.bfb-ionslider-html5 .lt-ie9 .irs-single {
  background: #999;
}
.bfb-ionslider-html5 .irs-grid {
  height: 27px;
}
.bfb-ionslider-html5 .irs-grid-pol {
  opacity: 0.5;
  background: #428bca;
}
.bfb-ionslider-html5 .irs-grid-pol.small {
  background: #999;
}
.bfb-ionslider-html5 .irs-grid-text {
  bottom: 5px;
  color: #99a4ac;
}
.bfb-ionslider-nice .irs-line-mid,
.bfb-ionslider-nice .irs-line-left,
.bfb-ionslider-nice .irs-line-right,
.bfb-ionslider-nice .irs-bar,
.bfb-ionslider-nice .irs-bar-edge,
.bfb-ionslider-nice .irs-slider {
  background: url('../images/sprite-skin-nice.png') repeat-x;
}
.bfb-ionslider-nice .irs {
  height: 40px;
}
.bfb-ionslider-nice .irs-with-grid {
  height: 60px;
}
.bfb-ionslider-nice .irs-line {
  height: 8px;
  top: 25px;
}
.bfb-ionslider-nice .irs-line-left {
  height: 8px;
  background-position: 0 -30px;
}
.bfb-ionslider-nice .irs-line-mid {
  height: 8px;
  background-position: 0 0;
}
.bfb-ionslider-nice .irs-line-right {
  height: 8px;
  background-position: 100% -30px;
}
.bfb-ionslider-nice .irs-bar {
  height: 8px;
  top: 25px;
  background-position: 0 -60px;
}
.bfb-ionslider-nice .irs-bar-edge {
  top: 25px;
  height: 8px;
  width: 11px;
  background-position: 0 -90px;
}
.bfb-ionslider-nice .irs-shadow {
  height: 1px;
  top: 34px;
  background: #000;
  opacity: 0.15;
}
.bfb-ionslider-nice .lt-ie9 .irs-shadow {
  filter: alpha(opacity=15);
}
.bfb-ionslider-nice .irs-slider {
  width: 22px;
  height: 22px;
  top: 17px;
  background-position: 0 -120px;
}
.bfb-ionslider-nice .irs-slider.state_hover,
.bfb-ionslider-nice .irs-slider:hover {
  background-position: 0 -150px;
}
.bfb-ionslider-nice .irs-min,
.bfb-ionslider-nice .irs-max {
  color: #999;
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  top: 0;
  padding: 1px 3px;
  background: rgba(0, 0, 0, 0.1);
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.bfb-ionslider-nice .lt-ie9 .irs-min,
.bfb-ionslider-nice .lt-ie9 .irs-max {
  background: #ccc;
}
.bfb-ionslider-nice .irs-from,
.bfb-ionslider-nice .irs-to,
.bfb-ionslider-nice .irs-single {
  color: #fff !important;
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  padding: 1px 5px;
  background: rgba(0, 0, 0, 0.3);
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.bfb-ionslider-nice .lt-ie9 .irs-from,
.bfb-ionslider-nice .lt-ie9 .irs-to,
.bfb-ionslider-nice .lt-ie9 .irs-single {
  background: #999;
}
.bfb-ionslider-nice .irs-grid-pol {
  background: #99a4ac;
}
.bfb-ionslider-nice .irs-grid-text {
  color: #99a4ac;
}
.bfb-ionslider-simple .irs-line-mid,
.bfb-ionslider-simple .irs-line-left,
.bfb-ionslider-simple .irs-line-right,
.bfb-ionslider-simple .irs-bar,
.bfb-ionslider-simple .irs-bar-edge,
.bfb-ionslider-simple .irs-slider {
  background: url('../images/sprite-skin-simple.png') repeat-x;
}
.bfb-ionslider-simple .irs {
  height: 40px;
}
.bfb-ionslider-simple .irs-with-grid {
  height: 60px;
}
.bfb-ionslider-simple .irs-line {
  height: 6px;
  top: 25px;
}
.bfb-ionslider-simple .irs-line-left {
  height: 6px;
  background-position: 0 -30px;
}
.bfb-ionslider-simple .irs-line-mid {
  height: 6px;
  background-position: 0 0;
}
.bfb-ionslider-simple .irs-line-right {
  height: 6px;
  background-position: 100% -30px;
}
.bfb-ionslider-simple .irs-bar {
  height: 6px;
  top: 25px;
  background-position: 0 -60px;
}
.bfb-ionslider-simple .irs-bar-edge {
  top: 25px;
  height: 6px;
  width: 7px;
  background-position: 0 -90px;
}
.bfb-ionslider-simple .irs-shadow {
  height: 1px;
  top: 34px;
  background: #000;
  opacity: 0.75;
}
.bfb-ionslider-simple .lt-ie9 .irs-shadow {
  filter: alpha(opacity=75);
}
.bfb-ionslider-simple .irs-slider {
  width: 8px;
  height: 15px;
  top: 21px;
  background-position: 0 -120px;
}
.bfb-ionslider-simple .irs-slider.state_hover,
.bfb-ionslider-simple .irs-slider:hover {
  background-position: 0 -150px;
}
.bfb-ionslider-simple .irs-min,
.bfb-ionslider-simple .irs-max {
  color: #c0c0c0;
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  top: 0;
  padding: 1px 3px;
  background: rgba(0, 0, 0, 0.1);
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.bfb-ionslider-simple .lt-ie9 .irs-min,
.bfb-ionslider-simple .lt-ie9 .irs-max {
  background: #3654b0;
}
.bfb-ionslider-simple .irs-from,
.bfb-ionslider-simple .irs-to,
.bfb-ionslider-simple .irs-single {
  color: #fff !important;
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  padding: 1px 5px;
  background: rgba(255, 255, 255, 0.8);
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.bfb-ionslider-simple .lt-ie9 .irs-from,
.bfb-ionslider-simple .lt-ie9 .irs-to,
.bfb-ionslider-simple .lt-ie9 .irs-single {
  background: #d8dff3;
}
.bfb-ionslider-simple .irs-grid-pol {
  background: #777;
}
.bfb-ionslider-simple .irs-grid-text {
  color: #e0e0e0;
}
