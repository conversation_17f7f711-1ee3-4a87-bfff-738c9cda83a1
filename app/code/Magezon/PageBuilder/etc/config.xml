<?xml version="1.0"?>
<!--
/**
 * Magezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license.
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  Magezon
 * @package   Magezon_PageBuilder
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
	<default>
		<mgzpagebuilder>
			<general>
				<enable>1</enable>
				<enable_pages>1</enable_pages>
				<enable_blocks>1</enable_blocks>
				<enable_products>1</enable_products>
				<enable_categories>1</enable_categories>
			</general>
			<instagram>
				<password backend_model="Magento\Config\Model\Config\Backend\Encrypted"/>
			</instagram>
		</mgzpagebuilder>
	</default>
</config>