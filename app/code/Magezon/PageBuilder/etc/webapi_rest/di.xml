<?xml version="1.0"?>
<!--
/**
 * Magezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license.
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  Magezon
 * @package   Magezon_PageBuilder
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<type name="Magento\Catalog\Model\Product">
		<plugin name="filter_product_attributes" type="Magezon\PageBuilder\Plugin\Model\Product" />
	</type>
	<type name="Magento\Catalog\Model\Category">
		<plugin name="filter_category_attributes" type="Magezon\PageBuilder\Plugin\Model\Category" />
	</type>
	<type name="Magento\Cms\Model\Page">
		<plugin name="filter_page_attributes" type="Magezon\PageBuilder\Plugin\Model\Page" />
	</type>
</config>