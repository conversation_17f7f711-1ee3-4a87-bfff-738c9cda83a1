@-moz-keyframes newsletterSpin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@-webkit-keyframes newsletterSpin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@-ms-keyframes newsletterSpin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.mgz-newsletter-form .mgz-newsletter-btn:before {
    -webkit-animation: newsletterSpin .6s linear infinite;
    animation: newsletterSpin .6s linear infinite;
}