<?php

namespace Rottner\Customization\Plugin;

use Magento\Catalog\Helper\Output;
use Magento\Catalog\Model\Product as ModelProduct;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Framework\Phrase;

class AttributeIntegerRound
{
    /**
     * @var EavConfig
     */
    private $eavConfig;

    /**
     * @param EavConfig $eavConfig
     */
    public function __construct(
        EavConfig $eavConfig
    ) {
        $this->eavConfig = $eavConfig;
    }

    /**
     * Format numeric attribute values (int/float) to display with 2 decimal places
     * and remove trailing zeros for whole numbers
     *
     * @param Output $subject
     * @param string $result
     * @param ModelProduct $product
     * @param Phrase|string $attributeHtml
     * @param string $attributeName
     * @return string
     */
    public function afterProductAttribute(
        Output $subject,
        $result,
        $product,
        $attributeHtml,
        $attributeName
    ) {
        if (!$result) {
            return $result;
        }
        try {
            $attribute = $this->eavConfig->getAttribute('catalog_product', $attributeName);
            if ($attribute && in_array($attribute->getFrontendInput(), ['text', 'price','weight']) &&
                in_array($attribute->getBackendType(), ['decimal', 'int'])) {

                if (is_numeric($result)) {
                    return (float)$result;
                }
            }
        } catch (\Exception $e) { }

        return $result;
    }
}