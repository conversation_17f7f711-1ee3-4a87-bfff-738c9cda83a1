<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="sw_megamenu" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <field id="hide" translate="label" type="select" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Hide submenu</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>When Megamenu is disabled, there will still be a submenu display. With this setting you can disable that, when using an other theme.</comment>
                </field>
            </group>
        </section>
        <section id="porto_settings">
            <group id="product">
                <field id="related_position" translate="label" type="select" sortOrder="365" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Position of related products</label>
                    <source_model>Rottner\Customization\Model\Config\Settings\RelatedPosition</source_model>
                </field>
            </group>
        </section>
        <section id="catalog">
            <group id="frontend">
                <field id="name_replacement" translate="label" type="textarea" sortOrder="366" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Replace Name in Listing</label>
                    <comment>Strings that will be replaced in the product name on product listing. Separated by Pipe (|)</comment>
                </field>
            </group>
        </section>
    </system>
</config>
