<?php

namespace Rottner\Customization\Helper;

use Magento\Store\Model\ScopeInterface;

class ProductNameCleanup extends \Magento\Framework\App\Helper\AbstractHelper
{

    private $configValues = null;
    private $keepValues = ["Rottner" => "RRRRRRRRRRRRRRRRRRRR"];

    public function getCleanedName($product, $name)
    {
        if ($this->configValues === null) {
            $this->configValues = explode("|", (string)$this->scopeConfig->getValue("catalog/frontend/name_replacement", ScopeInterface::SCOPE_STORE) ?? "");
        }

        $name = str_replace(array_keys($this->keepValues), $this->keepValues, $name);

        $name = trim(str_replace(
            array_merge($this->configValues ?? [],[
                '  ',
                $product->getAttributeText('lock_type') . " ",
                strtolower($product->getAttributeText('color')) . " ",
                $product->getAttributeText('color') . " ",
            ]), " ", $name));

        $name = str_replace($this->keepValues, array_keys($this->keepValues), $name);
        return $name;
    }

}