<?php
/**
 * @copyright Copyright (c) 2016 www.tigren.com
 */
// @codingStandardsIgnoreFile

/** @var $block \Magento\Catalog\Block\Product\View */
?>
<?php
$_product = $block->getProduct();
$buttonTitle = __('Add to Cart');
?>
<?php if ($_product->isSaleable()): ?>
    <div class="box-tocart">
        <div class="fieldset">
            <?php if ($block->shouldRenderQuantity()): ?>
                <div class="field qty">
                    <label class="label" for="qty"><span><?php /* @escapeNotVerified */
                            echo __('Qty') ?></span></label>
                    <div class="control">
                        <input type="number"
                               name="qty"
                               id="qty"
                               maxlength="12"
                               value="<?php /* @escapeNotVerified */
                               echo $block->getProductDefaultQty() * 1 ?>"
                               title="<?php /* @escapeNotVerified */
                               echo __('Qty') ?>" class="input-text qty"
                               data-validate="<?php echo $block->escapeHtml(json_encode($block->getQuantityValidators())) ?>"
                        />
                    </div>
                </div>
            <?php endif; ?>
            <div class="actions">
                <button type="button" title="Cancel" class="action primary" id="ajaxcart_cancel">
                    <span><?php echo __('Cancel') ?></span>
                </button>
                <button type="submit"
                        title="<?php /* @escapeNotVerified */
                        echo $buttonTitle ?>"
                        class="action ajax-popup-to-cart primary"
                        id="product-addtocart-button">
                    <span><?php /* @escapeNotVerified */
                        echo $buttonTitle ?></span>
                </button>
                <?php echo $block->getChildHtml('', true) ?>
            </div>
        </div>
    </div>
<?php endif; ?>