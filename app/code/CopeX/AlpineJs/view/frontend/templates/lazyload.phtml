<?php
/** @var \Magento\Framework\Escaper $escaper */
/** @var $block \Magento\Framework\View\Element\Template */
?>
<script>
    const loadedScripts = {};
    const lazyLoadJS = function(source, callback = null) {
        if(! (source in loadedScripts)){
            loadedScripts[source] = {
                initialized: false,
                script: document.createElement('script')
            };
            loadedScripts[source].script.type = 'module';
            loadedScripts[source].script.src = source;
            loadedScripts[source].script.addEventListener('load', () => { loadedScripts[source].initialized = true;});
            typeof callback === "function" && loadedScripts[source].script.addEventListener('load', callback);
            document.head.appendChild(loadedScripts[source].script)
        }
        else if(loadedScripts[source].initialized === false){
            typeof callback === "function" && loadedScripts[source].script.addEventListener('load', callback);
        }
        else {
            typeof callback === "function" && callback();
        }
        return loadedScripts[source].script;
    }
    window.lazyLoadJs = lazyLoadJS;

    const loadedCss = {};
    const lazyLoadCss = function(source){
        if(! (source in loadedCss)){
            loadedCss[source] = document.createElement('link')
            loadedCss[source].rel = 'stylesheet';
            loadedCss[source].type = 'text/css';
            loadedCss[source].href = source;
            document.head.append(loadedCss[source]);
        }
        return loadedCss[source];
    }

    window.lazyLoadCss = lazyLoadCss;

</script>