<?php
/**
 * Copyright © <PERSON> All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\ProductSeries\Model\Config\Source;

class Persona implements \Magento\Framework\Option\ArrayInterface
{

    public function toOptionArray()
    {
        return [['value' => '', 'label' => __('default')],['value' => 'green', 'label' => __('green')],['value' => 'lightgreen', 'label' => __('lightgreen')],['value' => 'lightblue', 'label' => __('lightblue')]];
    }

    public function toArray()
    {
        return ['' => __('default'),'green' => __('green'),'lightgreen' => __('lightgreen'),'lightblue' => __('lightblue')];
    }
}
