<?xml version="1.0"?>
<page layout="1column" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="product.info.details">
            <block class="Magento\Framework\View\Element\Template" name="product.tab.series.table"
                   template="CopeX_ProductSeries::product/view/table.phtml" group="detailed_info" before="-">
                <arguments>
                    <argument translate="true" name="title" xsi:type="string">Models</argument>
                    <argument name="sort_order" xsi:type="string">101</argument>
                    <argument name="view_model" xsi:type="object">CopeX\ProductSeries\ViewModel\Info</argument>
                </arguments>
            </block>
            <block class="Magento\Framework\View\Element\Template" name="product.tab.series.overview"
                   template="CopeX_ProductSeries::product/view/size.phtml" group="detailed_info" before="-">
                <arguments>
                    <argument translate="true" name="title" xsi:type="string">Model Overview</argument>
                    <argument name="sort_order" xsi:type="string">102</argument>
                    <argument name="view_model" xsi:type="object">CopeX\ProductSeries\ViewModel\Info</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
