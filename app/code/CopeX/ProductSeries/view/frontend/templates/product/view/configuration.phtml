<?php
/** @var $viewModel \CopeX\ProductSeries\ViewModel\Info */
/** @var $block \Magento\Framework\View\Element\Template */
/** @var \Magento\Framework\Escaper $escaper */

$viewModel = $block->getViewModel();
$product = $viewModel->getProduct();
$colorProducts = $viewModel->getColorProducts();
?>
<script>
    lazyLoadJs('<?= $escaper->escapeUrl($block->getViewFileUrl("CopeX_AlpineJs::js/alpine.js")); ?>');
</script>
<div class="product-series__selection">
    <?php if(count($colorProducts) > 1) : ?>
    <?php $firstProduct = array_shift($colorProducts); ?>
    <div class="product-series__section--color">
        <div class="label"><?= $viewModel->getAttributeLabel('color'); ?></div>
        <div class="options">
            <span class="own-image">
                <img src="<?= $viewModel->getImage($product, 'product_page_image_small')->getUrl() ?>" alt="<?= $block->escapeHtml($firstProduct->getAttributeText('color'))?>">
            </span>
            <?php foreach($colorProducts as $colorProduct) : ?>
            <a href="<?= $colorProduct->getProductUrl() ?>">
                <img src="<?= $viewModel->getImage($colorProduct, 'product_page_image_small')->getUrl() ?>" alt="<?= $block->escapeHtml($colorProduct->getAttributeText('color'))?>">
            </a>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>
    <?php foreach ( $viewModel->getAdditionalOptionAttributes() as $attributeCode): ?>
    <?php $optionProducts = $viewModel->getUniqueProductsByAttribute($attributeCode); ?>
    <?php if( count($optionProducts) > 1) : ?>
        <?php $firstProduct = array_shift($optionProducts); ?>
        <div class="product-series__section__<?= $attributeCode ?>">
            <div class="label"><?= $viewModel->getAttributeLabel($attributeCode); ?></div>
            <div class="options" x-data="{ open: false }">
                <div class="open" @click="open = ! open"><?= $viewModel->getAttributeValueText($firstProduct, $attributeCode) ?>
                    <div class="triangle">
                        <div class="over-triangle"></div>
                    </div>
                </div>
                <div style="display: none;" x-show="open" @click.outside="open = false">
                    <a class="dropdown-item current"><?= $viewModel->getAttributeValueText($firstProduct, $attributeCode) ?></a>
                    <?php foreach ($optionProducts as $optionProduct): ?>
                        <a class="dropdown-item" href="<?= $optionProduct->getProductUrl()?>"><?= $viewModel->getAttributeValueText($optionProduct, $attributeCode) ?></a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    <?php endforeach; ?>
</div>