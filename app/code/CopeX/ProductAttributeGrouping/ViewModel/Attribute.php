<?php

declare(strict_types=1);

namespace CopeX\ProductAttributeGrouping\ViewModel;

use Hyva\Theme\ViewModel\ProductAttributes;
use Magento\Catalog\Api\ProductAttributeGroupRepositoryInterface;
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Catalog\Model\ResourceModel\Product as ProductResource;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\SortOrderBuilder;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Eav\Api\Data\AttributeGroupInterface;
use Magento\Catalog\Api\Data\ProductAttributeInterface;

class Attribute implements ArgumentInterface
{
    /**
     * @var ProductAttributes
     */
    private ProductAttributes $hyvaProductAttributes;

    /**
     * @var SearchCriteriaBuilder
     */
    private SearchCriteriaBuilder $searchCriteriaBuilder;

    /**
     * @var ProductAttributeGroupRepositoryInterface
     */
    private ProductAttributeGroupRepositoryInterface $attributeGroupRepository;

    /**
     * @var ProductAttributeRepositoryInterface
     */
    private ProductAttributeRepositoryInterface $attributeRepository;

    /**
     * @var SortOrderBuilder
     */
    private SortOrderBuilder $sortOrderBuilder;

    /**
     * @var ProductResource
     */
    private ProductResource $productResource;

    /**
     * Attribute constructor.
     * @param ProductResource $productResource
     * @param ProductAttributes $hyvaProductAttributes
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param ProductAttributeGroupRepositoryInterface $attributeGroupRepository
     * @param ProductAttributeRepositoryInterface $attributeRepository
     * @param SortOrderBuilder $sortOrderBuilder
     */
    public function __construct(
        ProductResource $productResource,
        ProductAttributes $hyvaProductAttributes,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        ProductAttributeGroupRepositoryInterface $attributeGroupRepository,
        ProductAttributeRepositoryInterface $attributeRepository,
        SortOrderBuilder $sortOrderBuilder
    ) {
        $this->hyvaProductAttributes = $hyvaProductAttributes;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->attributeRepository = $attributeRepository;
        $this->sortOrderBuilder = $sortOrderBuilder;
        $this->productResource = $productResource;
        $this->attributeGroupRepository = $attributeGroupRepository;
    }

    /**
     * @param $groupName
     * @return array|ProductAttributeInterface[]
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getAttributesByGroup($groupName)
    {
        $groupAttributes = [];
        $sortOrder = $this->sortOrderBuilder
            ->setField('sort_order')
            ->setAscendingDirection()
            ->create();
        $group = current($this->attributeGroupRepository->getList($searchCriteria = $this->searchCriteriaBuilder
                ->addFilter(AttributeGroupInterface::GROUP_NAME, $groupName)->create())->getItems() ?? []);
        if ($group) {
            $searchCriteria = $this->searchCriteriaBuilder
                ->addFilter(AttributeGroupInterface::GROUP_ID, $group->getId())
                ->addFilter(ProductAttributeInterface::IS_VISIBLE, 1)
                ->addSortOrder($sortOrder)
                ->create();
            $groupAttributes = $this->attributeRepository->getList($searchCriteria)->getItems();
        }

        return $groupAttributes;
    }

    /**
     * @param $attribute
     * @param $product
     * @return mixed|string
     */
    public function getAttributeValue($attribute, $product)
    {
        $productAttributeData = $this->getAttributeData($attribute, $product);

        return $productAttributeData['value'];
    }

    /**
     * @param $attribute
     * @param $product
     * @return mixed|string
     */
    public function getAttributeStoreLabel($attribute,$product){
        $productAttributeData = $this->getAttributeData($attribute, $product);

        return $productAttributeData['label'];
    }

    /**
     * @param $attribute
     * @param $product
     * @return array|string[]
     */
    public function getAttributeData($attribute, $product){
        $attribute = $this->getAttribute($attribute);
        if ($attribute) {
            return $this->hyvaProductAttributes->getAttributeData($attribute, $product);
        }

        return [];
    }

    /**
     * @param $attribute
     * @return false|\Magento\Catalog\Model\ResourceModel\Eav\Attribute|\Magento\Eav\Model\Entity\Attribute\AbstractAttribute|null
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getAttribute($attribute)
    {
        if($attribute instanceof \Magento\Catalog\Model\ResourceModel\Eav\Attribute){
            return $attribute;
        }
        if(is_string($attribute)){
            return $this->productResource->getAttribute($attribute);
        }

        return null;
    }
}

