
.form-check {
    margin-top: 20px;
    margin-bottom: 20px;
    display: flex;
}

.form-check-input {
    margin-right: 10px;
    margin-top: 3px;
}

.form-check .price {
    margin-right: 5px;
    margin-left: 5px;
}

.form-check .add-block {
    display: block;
}

.form-check .method-info {
    font-size: 14px;
}

.form-check-label {
    display: flex;
    color: #333;
}

.form-check .method-info {
    display: flex;
}

.tocart-button {
    padding: 8px;
    border: solid 1px;
    margin-bottom: 16px;
    margin-top: 16px;
}

.other-countries-block {
    width: max-content;
}

#btn-estimate-shipping-popup {
    color: #333;
    border: unset;
}

#product-shipping__shipping-modal .message {
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: bolder;
}

#country {
    width: 100%;
}

.product-shipping {
    margin-top: 30px;
    margin-bottom: 30px;
}

@media (min-width: 768px) {
    .estimation-popup .modal-inner-wrap {
        max-width: 500px;
        margin-top: 200px;
    }
}

@media (max-width: 767px) {
    .estimation-popup {
        left: 0 !important;
    }
    .shipping-price-note, .price-details {
        margin-left: 10px;
    }
}

.estimation-popup .modal-footer {
    display: none;
}

#btn-estimate-shipping {
    margin-top: 20px;
    width: 100%;
}

.total-price-shipping {
    margin-top: 30px;
}

#btn-estimate-shipping-results {
    margin-top: 20px;
    padding: 0;
}

#btn-estimate-shipping-results ul, #btn-estimate-shipping-results li {
    list-style-type: none;
    padding: 0;
}

#btn-estimate-shipping-results .price{
    margin-left: 5px;
}
