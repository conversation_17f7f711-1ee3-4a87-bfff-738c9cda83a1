<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="CopeX_EstimateShippingOnProduct::css/custom.css"/>
    </head>
    <body>
        <referenceContainer name="product.info.main">
            <container name="product.shipping" htmlTag="div" htmlClass="product-shipping" before="-">
                <block class="Magento\Framework\View\Element\Template" name="product.shipping.estimate" template="CopeX_EstimateShippingOnProduct::product/shipping.phtml">
                    <arguments>
                        <argument name="view_model" xsi:type="object">CopeX\EstimateShippingOnProduct\ViewModel\View</argument>
                    </arguments>
                </block>
            </container>
        </referenceContainer>
        <referenceContainer name="product.info.price">
            <block class="CopeX\EstimateShippingOnProduct\Block\Price" name="shipping.calculation.price" template="CopeX_EstimateShippingOnProduct::product/price.phtml" after="-"/>
        </referenceContainer>
    </body>
</page>
