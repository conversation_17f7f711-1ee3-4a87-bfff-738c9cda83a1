<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="hyva_modal"/>
    <head>
        <remove src="CopeX_EstimateShippingOnProduct::css/custom.css"/>
    </head>
    <body>
        <referenceBlock name="product.info">
            <action method="setTemplate">
                <argument name="template" xsi:type="string">CopeX_EstimateShippingOnProduct::product/view/product-info.phtml</argument>
            </action>
            <block class="Magento\Catalog\Block\Product\View" name="product.delivery.info"
                   template="CopeX_EstimateShippingOnProduct::product/view/delivery-info.phtml"/>
            <block class="Magento\Framework\View\Element\Template" name="product.shipping.estimate"
                   template="CopeX_EstimateShippingOnProduct::product/shipping_hyva.phtml">
                <arguments>
                    <argument name="view_model" xsi:type="object">CopeX\EstimateShippingOnProduct\ViewModel\View</argument>
                </arguments>
            </block>
        </referenceBlock>
        <referenceBlock name="product.info.delivery" remove="true"/>
    </body>
</page>
