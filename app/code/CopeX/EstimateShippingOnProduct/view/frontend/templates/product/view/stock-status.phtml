<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentProduct;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\ViewModel\ProductStockItem;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var CurrentProduct $currentProduct */
$currentProduct = $viewModels->require(CurrentProduct::class);
$stock = $viewModels->require(ProductStockItem::class);

/** @var Product $product */
$product = $block->hasData('product')
    ? $block->getData('product')
    : $currentProduct->get();

if (!$product || !$product->getId()) {
    return;
}

$qty = $stock->getQty($product);
$inStock = $product->getIsSalable() && $qty > 0;
?>
<?php if ($block->getParentBlock()->displayProductStockStatus()): ?>
    <div class="flex items-center justify-end md:justify-start align-middle mr-1">
        <span class="flex items-center gap-x-2 stock before:w-3 before:h-3 before:rounded-full before:shrink-0 w-max
        <?= $inStock ? 'available before:bg-green-500' : 'unavailable before:bg-red-500' ?>"
           title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>">
            <?= $escaper->escapeHtml(($inStock ? __('In stock') : __('Out of stock')) . ' / ' .  __('Stock quantity: %1 Piece', intval($qty))) ?>
        </span>
    </div>
<?php endif; ?>
