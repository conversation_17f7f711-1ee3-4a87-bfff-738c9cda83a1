<?php
declare(strict_types=1);

namespace CopeX\EstimateShippingOnProduct\Plugin\Magento\Checkout\Model;

use CopeX\EstimateShippingOnProduct\Helper\Calculate;
use CopeX\EstimateShippingOnProduct\Helper\Data;
use Magento\Checkout\Model\Session;
use Magento\Quote\Model\ResourceModel\Quote;

/**
 * Class DefaultConfigProvider
 * @package CopeX\EstimateShippingOnProduct\Plugin\Magento\Checkout\Model
 */
class DefaultConfigProvider
{
    /** @var Session */
    protected $checkoutSession;

    /** @var Quote */
    protected $quoteRepository;

    /** @var Calculate */
    protected $calculateHelper;

    /**
     * DefaultConfigProvider constructor.
     * @param Session $checkoutSession
     * @param Quote $quoteRepository
     * @param Calculate $calculateHelper
     */
    public function __construct(
        Session $checkoutSession,
        Quote $quoteRepository,
        Calculate $calculateHelper
    )
    {
        $this->checkoutSession = $checkoutSession;
        $this->quoteRepository = $quoteRepository;
        $this->calculateHelper = $calculateHelper;
    }

    /**
     * @param \Magento\Checkout\Model\DefaultConfigProvider $subject
     * @return array
     */
    public function beforeGetConfig(
        \Magento\Checkout\Model\DefaultConfigProvider $subject
    )
    {
        $shouldUpdateQuote = $this->calculateHelper->updateQuote();
        $shippingMethod = $this->checkoutSession->getData(Data::KEY_SHIPPING_METHOD);

        if ($shouldUpdateQuote && $shippingMethod) {
            try {
                $quote = $this->checkoutSession->getQuote();
            } catch (\Exception $e) {
                return [];
            }

            $shippingAddress = $quote->getShippingAddress();

            if ($shippingAddress && $shippingAddress->getShippingMethod() !== $shippingMethod) {
                if (!$shippingAddress->getCountryId()) {
                    $shippingAddress->setCountryId($this->calculateHelper->getDefaultCountryCode());
                }

                $shippingAddress->setShippingMethod($shippingMethod);
                $shippingAddress->setCollectShippingRates(true)->collectShippingRates();

                try {
                    $this->quoteRepository->save($quote);
                } catch (\Exception $e) {
                    return [];
                }
            }
        }

        return [];
    }
}
