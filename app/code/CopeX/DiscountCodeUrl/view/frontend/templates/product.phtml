<?php
/**
 * CopeX
 *
 * @category   CopeX
 * @package    CopeX_CookieNotification
 */

/**
 * @var $block \Magento\Framework\View\Element\Template
 * @var $viewModel \CopeX\DiscountCodeUrl\Helper\Config
 * @var $productViewModel \CopeX\DiscountCodeUrl\Helper\Product
 */
$viewModel = $block->getViewModel();
$productViewModel = $block->getProductViewModel();
if ($viewModel->isEnabled()) {
    ?>
    <script type="text/x-magento-init">
        {
            "*": {
                "discountCodeProduct": {
                    "cookieName": "<?= $viewModel->getCookieName() ?>",
                    "controllerUrl": "<?= $productViewModel->getControllerUrl() ?>",
                    "productId": "<?= $productViewModel->getProductIdFromRequest() ?>",
                    "message": "<?= $block->escapeJs($viewModel->getMessage()) ?>",
                    "showCountdown": "<?= $block->escapeJs($viewModel->isCountdownEnabled()) ?>",
                    "countdownMessage": "<?= $block->escapeJs($viewModel->getCountdownMessage()) ?>"
                }
            }
        }
    </script>

<?php } ?>
