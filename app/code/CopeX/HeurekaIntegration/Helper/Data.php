<?php

namespace CopeX\HeurekaIntegration\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{
    const CONFIG_PATH_IS_ENABLED = 'heureka/settings/is_enabled';
    const CONFIG_PATH_API_KEY = 'heureka/settings/api_key';
    const CONFIG_PATH_CERTIFICATION = 'heureka/settings/certification';

    /**
     * @return bool
     */
    public function getIsEnabled($storeId = null): bool
    {
        return (bool)$this->scopeConfig->getValue(self::CONFIG_PATH_IS_ENABLED, ScopeInterface::SCOPE_STORE, $storeId);
    }

    /**
     * @return string
     */
    public function getApiKey($storeId = null): string
    {
        return $this->scopeConfig->getValue(self::CONFIG_PATH_API_KEY, ScopeInterface::SCOPE_STORE, $storeId);
    }

    /**
     * @return string
     */
    public function getCertification($storeId = null): string
    {
        return $this->scopeConfig->getValue(self::CONFIG_PATH_CERTIFICATION, ScopeInterface::SCOPE_STORE, $storeId);
    }
}
