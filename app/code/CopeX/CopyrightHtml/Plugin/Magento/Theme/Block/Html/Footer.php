<?php
/**
 * Module for override the original copyright in footer block.
 * Copyright (C) 2017 Roman Hutterer
 * 
 * This file is part of CopeX/CopyrightHtml.
 * 
 * CopeX/CopyrightHtml is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 * 
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

namespace CopeX\CopyrightHtml\Plugin\Magento\Theme\Block\Html;

class Footer
{

    const COMPANY_NAME = 'general/imprint/company_first';

    private $companyName;
    public function __construct(\Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig)
    {
        $this->companyName = $scopeConfig->getValue(self::COMPANY_NAME, \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
    }

    public function afterGetCopyright(
        \Magento\Theme\Block\Html\Footer $subject,
        $result
    ) {
        return __('&copy; '. $this->companyName .' - Onlineshop made with &hearts; by <a href="//copex.io" title="100% Magento Agentur aus Österreich">CopeX.io</a>');
    }
}
