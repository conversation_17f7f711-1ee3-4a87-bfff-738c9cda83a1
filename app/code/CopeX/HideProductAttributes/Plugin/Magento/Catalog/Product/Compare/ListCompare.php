<?php

namespace CopeX\HideProductAttributes\Plugin\Magento\Catalog\Product\Compare;

class ListCompare
{

    /**
     * @var \CopeX\HideProductAttributes\Helper\Data
     */
    private $helper;

    public function __construct( \CopeX\HideProductAttributes\Helper\Data $helper)
    {
        $this->helper = $helper;
    }

    /**
     * @param ListCompare $subject
     * @param array       $result
     * @return array
     */
    public function afterGetAttributes(\Magento\Catalog\Block\Product\Compare\ListCompare $subject, array $result): array
    {
        if ($this->helper->getEnabled()) {
            $excluded = $this->helper->getExcludedAttributes();
            if($excluded)
                return array_diff_key($result,array_flip($excluded));
        }

        return $result;
    }
}