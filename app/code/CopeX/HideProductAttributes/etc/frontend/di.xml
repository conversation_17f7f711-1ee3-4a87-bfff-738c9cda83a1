<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Catalog\Model\Layer\Category\FilterableAttributeList">
        <plugin name="filterable_attribute_list"
                type="CopeX\HideProductAttributes\Plugin\Layer\Category\FilterableAttributeList" sortOrder="10"/>
    </type>
    <type name="Magento\Catalog\Block\Product\Compare\ListCompare">
        <plugin name="filter_product_compare_attributes"
                type="CopeX\HideProductAttributes\Plugin\Magento\Catalog\Product\Compare\ListCompare"/>
    </type>
</config>
