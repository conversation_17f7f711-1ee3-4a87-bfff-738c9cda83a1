<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.com/)
 * @package CopeX_ElasticSuiteSorting
 */
namespace CopeX\ElasticSuiteSorting\Plugin;

use Magento\Catalog\Model\Config;

class ListSortFront extends Base
{
    /**
     * @param Config $subject
     * @param $result
     * @return mixed
     */
    public function afterGetAttributeUsedForSortByArray(Config $subject, $result)
    {
        $result = array_merge($result, $this->getElasticSuiteSorting());
        return $result;
    }
}
