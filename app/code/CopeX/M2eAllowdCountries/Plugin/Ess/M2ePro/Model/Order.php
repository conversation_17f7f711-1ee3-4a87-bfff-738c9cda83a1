<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\M2eAllowdCountries\Plugin\Ess\M2ePro\Model;

use CopeX\M2eAllowdCountries\Model\IsM2E;

class Order
{

    /**
     * @var IsM2E
     */
    private $isM2E;

    public function __construct(IsM2E $isM2E)
    {
        $this->isM2E = $isM2E;
    }

    public function beforeCreateMagentoOrder(\Ess\M2ePro\Model\Order $subject,$canCreateExistOrder = false)
    {
        $this->isM2E->setIsM2e(true);
        return [$canCreateExistOrder];
    }

    public function afterCreateMagentoOrder(\Ess\M2ePro\Model\Order $subject, $result) {
      //  $this->isM2E->setIsM2e(false); dont reset because invoice and shipment would otherwise not be possible
        return $result;
    }
}

