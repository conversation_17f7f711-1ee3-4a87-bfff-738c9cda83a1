<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

?>
<?php $_shipment = $block->getShipment() ?>
<?php $_order    = $block->getOrder() ?>
<?php if ($_shipment && $_order) : ?>
    <table class="email-items">
        <thead>
            <tr>
                <th class="item-image">
                    <?= $block->escapeHtml(__('Image')) ?>
                </th>
                <th class="item-info">
                    <?= $block->escapeHtml(__('Items')) ?>
                </th>
                <th class="item-qty">
                    <?= $block->escapeHtml(__('Qty')) ?>
                </th>
            </tr>
        </thead>
        <?php foreach ($_shipment->getAllItems() as $_item) : ?>
            <?php if (!$_item->getOrderItem()->getParentItem()) : ?>
                <tbody>
                    <?= $block->getItemHtml($_item) ?>
                </tbody>
            <?php endif; ?>
        <?php endforeach; ?>
    </table>
<?php endif; ?>
