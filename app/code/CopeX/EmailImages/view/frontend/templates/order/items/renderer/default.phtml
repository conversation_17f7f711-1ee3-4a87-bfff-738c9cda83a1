<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/** @var  $block \Magento\Sales\Block\Order\Item\Renderer\DefaultRenderer */
$_item = $block->getItem();
$width = '65';
$height = '65';
$productId = $_item->getProductId();
$productImage = "";
try{
    $product = \Magento\Framework\App\ObjectManager::getInstance()->get('Magento\Catalog\Model\ProductRepository')->getById($productId);
    $productImage = $this->helper('Magento\Catalog\Helper\Image')->init($product, 'category_page_list', ['height' => $height , 'width'=> $width])->getUrl();
}catch(\Exception $e){}
?>
<tr id="order-item-row-<?= (int) $_item->getId() ?>">
    <td class="col image" data-th="<?= $block->escapeHtmlAttr(__('Image')) ?>"><img height="<?= $height ?>" width="<?= $width ?>" src="<?= $productImage ?>" alt="<?= __('Product Image');?>"></td>
    <td class="col name" data-th="<?= $block->escapeHtmlAttr(__('Product Name')) ?>">
        <strong class="product name product-item-name"><?= $block->escapeHtml($_item->getName()) ?></strong>
        <?php if ($_options = $block->getItemOptions()) : ?>
            <dl class="item-options">
                <?php foreach ($_options as $_option) : ?>
                    <dt><?= $block->escapeHtml($_option['label']) ?></dt>
                    <?php if (!$block->getPrintStatus()) : ?>
                        <?php $_formatedOptionValue = $block->getFormatedOptionValue($_option) ?>
                        <dd<?= (isset($_formatedOptionValue['full_view']) ? ' class="tooltip wrapper"' : '') ?>>
                            <?= $block->escapeHtml($_formatedOptionValue['value'], ['a', 'img']) ?>
                            <?php if (isset($_formatedOptionValue['full_view'])) : ?>
                                <div class="tooltip content">
                                    <dl class="item options">
                                        <dt><?= $block->escapeHtml($_option['label']) ?></dt>
                                        <dd><?= $block->escapeHtml($_formatedOptionValue['full_view']) ?></dd>
                                    </dl>
                                </div>
                            <?php endif; ?>
                        </dd>
                    <?php else : ?>
                        <dd><?= $block->escapeHtml((isset($_option['print_value']) ? $_option['print_value'] : $_option['value'])) ?></dd>
                    <?php endif; ?>
                <?php endforeach; ?>
            </dl>
        <?php endif; ?>
        <?php $addtInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
        <?php if ($addtInfoBlock) : ?>
            <?= $addtInfoBlock->setItem($_item)->toHtml() ?>
        <?php endif; ?>
        <?= $block->escapeHtml($_item->getDescription()) ?>
    </td>
    <td class="col sku" data-th="<?= $block->escapeHtmlAttr(__('SKU')) ?>"><?= /* @noEscape */ $block->prepareSku($block->getSku()) ?></td>
    <td class="col price" data-th="<?= $block->escapeHtmlAttr(__('Price')) ?>">
        <?= $block->getItemPriceHtml() ?>
    </td>
    <td class="col qty" data-th="<?= $block->escapeHtmlAttr(__('Qty')) ?>">
        <ul class="items-qty">
            <?php if ($block->getItem()->getQtyOrdered() > 0) : ?>
                <li class="item">
                    <span class="title"><?= $block->escapeHtml(__('Ordered')) ?></span>
                    <span class="content"><?= (float) $block->getItem()->getQtyOrdered() ?></span>
                </li>
            <?php endif; ?>
            <?php if ($block->getItem()->getQtyShipped() > 0) : ?>
                <li class="item">
                    <span class="title"><?= $block->escapeHtml(__('Shipped')) ?></span>
                    <span class="content"><?= (float) $block->getItem()->getQtyShipped() ?></span>
                </li>
            <?php endif; ?>
            <?php if ($block->getItem()->getQtyCanceled() > 0) : ?>
                <li class="item">
                    <span class="title"><?= $block->escapeHtml(__('Canceled')) ?></span>
                    <span class="content"><?= (float) $block->getItem()->getQtyCanceled() ?></span>
                </li>
            <?php endif; ?>
            <?php if ($block->getItem()->getQtyRefunded() > 0) : ?>
                <li class="item">
                    <span class="title"><?= $block->escapeHtml(__('Refunded')) ?></span>
                    <span class="content"><?= (float) $block->getItem()->getQtyRefunded() ?></span>
                </li>
            <?php endif; ?>
        </ul>
    </td>
    <td class="col subtotal" data-th="<?= $block->escapeHtmlAttr(__('Subtotal')) ?>">
        <?= $block->getItemRowTotalHtml() ?>
    </td>
</tr>
