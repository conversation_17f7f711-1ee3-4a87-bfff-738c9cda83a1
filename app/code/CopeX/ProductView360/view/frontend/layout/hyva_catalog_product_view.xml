<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <script defer="defer" src="CopeX_ProductView360::js/lib/360degree/object2vr_player.js"/>
        <script defer="defer" src="CopeX_ProductView360::js/lib/360degree/skin.js"/>
        <css src="CopeX_ProductView360::css/hyva.css" type="text/css" />
    </head>
    <body>
        <referenceBlock name="product.media">
            <action method="setTemplate">
                <argument name="name" xsi:type="string">CopeX_ProductView360::product/view/hyva-gallery.phtml</argument>
            </action>
        </referenceBlock>
    </body>
</page>
