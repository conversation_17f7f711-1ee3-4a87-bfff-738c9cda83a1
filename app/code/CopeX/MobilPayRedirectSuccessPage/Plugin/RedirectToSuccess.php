<?php

namespace CopeX\MobilPayRedirectSuccessPage\Plugin;

use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Controller\ResultInterface;
use Mobilpay\Credit\Controller\Cc\Success;

class RedirectToSuccess
{

    private $resultRedirectFactory;

    public function __construct(
        RedirectFactory $resultRedirectFactory
    ) {
        $this->resultRedirectFactory = $resultRedirectFactory;
    }

    /**
     * @param Success         $subject
     * @param ResultInterface $result
     * @return ResultInterface
     */
    public function afterExecute(Success $subject, ResultInterface $result): ResultInterface
    {
        $resultRedirect = $this->resultRedirectFactory->create();
        $resultRedirect->setPath('checkout/onepage/success');
        return $resultRedirect;
    }
}