<?php
/**
 * @var $block \CopeX\ProductDetailSeoLink\Block\Seolink
 */

$productSeoLinkEnabled = $block->isProductSeoLinkEnabled();

if ($productSeoLinkEnabled) {
    $brandInfo = $block->getBrandInfo();
    $categoryInfos = $block->getCategoryInfo();
    $categoryUrls = [];
    $brandUrl = '';

    if ($brandInfo && isset($brandInfo['url']) && isset($brandInfo['name'])) {
        $brandUrl = '<a href="' . $brandInfo['url'] . '">' . $brandInfo['name'] . '</a>';
    }

    foreach ($categoryInfos as $categoryInfo) {
        if (isset($categoryInfo['url']) && isset($categoryInfo['name'])) {
            $categoryUrls[] = '<a href="' . $categoryInfo['url'] . '">' . $categoryInfo['name'] . '</a>';
        }
    }
}
?>
<?php if ($productSeoLinkEnabled && $categoryUrls) : ?>
    <div class="short-description seo-link">
        <?php if ($brandUrl) : ?>
            <?= __("See more %1 at %2.", implode(", ", $categoryUrls), $brandUrl); ?>
        <?php else: ?>
            <?= __("See more %1.", implode(", ", $categoryUrls)); ?>
        <?php endif; ?>
    </div>
<?php endif; ?>
