<?php
/**
 * LocalizedFields
 * @copyright Copyright © 2019 CopeX GmbH. All rights reserved.
 * <AUTHOR>
 */

namespace CopeX\Pimcore\Model\Pimcore\Type;

use CopeX\Pimcore\Model\Pimcore\AbstractType;
use CopeX\Pimcore\Model\Pimcore\Types;

class ManyToManyRelation extends AbstractType
{


    public function process($element)
    {
        $newValues = [];

        foreach ($element[self::PIMCORE_VALUE] as $relationObject) {
            $relationData = $this->handleType($relationObject);
            if ($relationData) {
                if (isset($relationData[$relationObject[self::ID]])) {
                    $relationData = $relationData[$relationObject[self::ID]];
                }
                if (is_array($relationData)) {
                    $relationData[self::ID] = $relationObject[self::ID];
                }
            }
            $newValues[$relationObject[self::ID]] = $relationData;
        }
        return [$element[self::PIMCORE_NAME] => $newValues];
    }
}
