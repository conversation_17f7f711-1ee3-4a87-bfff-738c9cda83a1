<?php
/**
 * Types
 * @copyright Copyright © 2019 CopeX GmbH. All rights reserved.
 * <AUTHOR>
 */

namespace CopeX\Pimcore\Model\Pimcore;

class Types
{
    const PIMCORE_TYPE = "type";
    const TYPE_DEFAULT = "default";

    /**
     * @var TypeInterface[]
     */
    protected $types;

    /**
     * Constructor
     * @param array $types
     */
    public function __construct(array $types = [])
    {
        $this->types = $types;
    }

    /**
     * {@inheritdoc}
     */
    public function getTypes()
    {
        return $this->types;
    }

    public function handleType($element)
    {
        $type = $element[self::PIMCORE_TYPE];
        if (array_key_exists($type, $this->types)) {
            return $this->types[$type]->setTypes($this)->process($element);
        }
        return $this->types[self::TYPE_DEFAULT]->setTypes($this)->process($element);
    }

    public function handleElements($items)
    {
        $newValues = [];
        if (array_key_exists(TypeInterface::PIMCORE_ELEMENTS, $items)) {
            foreach ($items[TypeInterface::PIMCORE_ELEMENTS] as $element) {
                $newValues += $this->handleType($element);
            }
        }
        return $newValues;
    }
}
