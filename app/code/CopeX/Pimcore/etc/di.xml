<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="CopeX\Pimcore\Helper\Data">
        <plugin disabled="false" name="CopeX_Pimcore_Plugin_Link" sortOrder="10" type="CopeX\Pimcore\Plugin\Link"/>
    </type>
    <type name="CopeX\Import\Rewrite\Magento\CatalogImportExport\Model\Import\Product\CategoryProcessor">
        <plugin type="CopeX\Pimcore\Plugin\ImportExport\UsePimcoreCategoryId" disabled="false" sortOrder="10"
                name="copex_pimcore_use_pimcore_category_id"/>
    </type>

    <type name="CopeX\Pimcore\Model\Pimcore\Types">
        <arguments>
            <argument name="types" xsi:type="array">
                <item name="default" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\SingleValue</item>
                <item name="singleValue" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\SingleValue</item>
                <item name="quantityValue" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\QuantityValue</item>
                <item name="href" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\Href</item>
                <item name="object" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\PimcoreObject</item>
                <item name="asset" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\Asset</item>
                <item name="image" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\Image</item>
                <item name="localizedfields" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\LocalizedFields</item>
                <item name="dynamicDropdown" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\DynamicDropdown</item>
                <item name="dynamicDropdownMultiple"
                      xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\DynamicDropdownMultiple
                </item>
                <item name="objectbricks" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\ObjectBricks</item>
                <item name="manyToManyObjectRelation"
                      xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\ManyToManyRelation
                </item>
                <item name="manyToManyRelation" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\ManyToManyRelation
                </item>
                <item name="multihrefMetadata" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\MultiHrefMetadata
                </item>
                <item name="coreShopDynamicDropdown"
                      xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\DynamicDropdown
                </item>
                <item name="coreShopDropdownMultiple"
                      xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\DynamicDropdownMultiple
                </item>
            </argument>
        </arguments>
    </type>
    <type name="CopeX\Pimcore\Model\Pimcore\Types\GraphQL">
        <arguments>
            <argument name="types" xsi:type="array">
                <item name="default" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\GraphQL\Input</item>
                <item name="quantityValue" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\GraphQL\QuantityValue</item>
                <item name="inputQuantityValue" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\GraphQL\QuantityValue</item>
                <item name="image" xsi:type="object">CopeX\Pimcore\Model\Pimcore\Type\GraphQL\Image</item>
            </argument>
        </arguments>
    </type>
</config>