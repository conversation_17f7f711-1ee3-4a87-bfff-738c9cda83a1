<?php

namespace CopeX\Cleanup\Plugin;

use Smile\ElasticsuiteCore\Model\System\Message\NotificationAboutVersions;

class HideElasticSuiteNotice
{
    /**
     * @param NotificationAboutVersions $subject
     * @param callable                  $proceed
     * @return bool
     */
    public function aroundIsDisplayed(NotificationAboutVersions $subject, callable $proceed): bool
    {
        return false;
    }
}