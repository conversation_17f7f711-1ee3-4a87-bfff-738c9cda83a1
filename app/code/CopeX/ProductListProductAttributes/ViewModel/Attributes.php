<?php

namespace CopeX\ProductListProductAttributes\ViewModel;

use Magento\Catalog\Api\Data\CategoryInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class Attributes implements ArgumentInterface
{

    const CATEGORY_ATTRIBUTE_DATA = 'product_list_attributes';
    /**
     * @var \Magento\Framework\Registry
     */
    private $registry;
    private $cache = [];
    private $attributesCache = [];

    public function __construct(\Magento\Framework\Registry $registry)
    {
        $this->registry = $registry;
    }

    public function getAttributeCodesByCategory(CategoryInterface $category){
        return array_map("trim", explode(",", (string)trim((string)$category->getData(self::CATEGORY_ATTRIBUTE_DATA) ?? "")));
    }

    public function getAttributeCodesByCurrentCategory(){
        /** @var CategoryInterface $currentCategory */
        $currentCategory = $this->registry->registry('current_category');
        if ($currentCategory) {
            if (!isset($this->cache[$currentCategory->getId()])) {
                $this->cache[$currentCategory->getId()] = array_filter($this->getAttributeCodesByCategory($currentCategory));
            }
            return $this->cache[$currentCategory->getId()];
        }
        return [];
    }

    public function getAttributes($product){
        if(!isset($this->attributesCache[$product->getAttributeSetId()])){
            $this->attributesCache[$product->getAttributeSetId()] = $product->getTypeInstance()->getSetAttributes($product);
        }
        return $this->attributesCache[$product->getAttributeSetId()];
    }

}