<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\CatalogReadOnlyByDomain\Plugin\Frontend\Magento\Framework\View;

use CopeX\CatalogReadOnlyByDomain\Model\Config;

class Layout
{

    /**
     * @var Config
     */
    private $config;

    public function __construct(Config $config)
    {
        $this->config = $config;
    }

    public function beforeGetOutput(\Magento\Framework\View\Layout $subject)
    {
        if ($this->config->isActive()) {
            if(!$this->config->isProductSalaeable()){
                $subject->unsetElement("ajaxCartJs");
                $subject->unsetElement("minicart");
                $subject->unsetElement("checkout.cart");
                $subject->unsetElement("product.info.addtocart");
                $subject->unsetElement("product.info.addtocart.additional");
                $subject->unsetElement("category.product.addto");
            }
            $subject->unsetElement("my-account-link");
            $subject->unsetElement("authorization-link");
            $subject->unsetElement("authorization-link-login");
            $subject->unsetElement("register-link");
            foreach ($this->config->getElementsToRemove() ?? [] as $element) {
                $subject->unsetElement($element);
            }
            if($this->config->isCatalogReadOnlyDomainNoPrice()){
                $subject->unsetElement('product.price.render.default');
            }
        }
        return [];
    }
}
