<?php

namespace CopeX\Pictograms\Api;

use Cope<PERSON>\Pictograms\Api\Data\PictogramInterface;
use Cope<PERSON>\Pictograms\Api\Data\PictogramSearchResultsInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Interface PictogramRepositoryInterface
 * repository interface for pictograms.
 * @package CopeX\Pictograms\Api
 * @api
 */
interface PictogramRepositoryInterface
{
    /**
     * Save pictogram.
     *
     * @param PictogramInterface $pictogram
     * @return PictogramInterface
     * @throws LocalizedException
     */
    public function save(PictogramInterface $pictogram);

    /**
     * Retrieve pictogram.
     *
     * @param int $pictogramId
     * @return PictogramInterface
     * @throws LocalizedException
     */
    public function getById($pictogramId);

    /**
     * Retrieve pictograms matching the specified criteria.
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return PictogramSearchResultsInterface
     * @throws LocalizedException
     */
    public function getList(SearchCriteriaInterface $searchCriteria);

    /**
     * Delete pictogram.
     *
     * @param PictogramInterface $pictogram
     * @return bool true on success
     * @throws LocalizedException
     */
    public function delete(PictogramInterface $pictogram);

    /**
     * Delete pictogram by ID.
     *
     * @param int $pictogramId
     * @return bool true on success
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function deleteById($pictogramId);
}
