<?php

namespace CopeX\Pictograms\Api\Data;

/**
 * Interface PictogramInterface
 * @package CopeX\Pictograms\Api\Data
 */
interface PictogramInterface
{
    const PICTOGRAM_ID = 'pictogram_id';
    const PRIORITY = 'priority';
    const NAME = 'name';
    const SHORT_DESCRIPTION = 'short_description';
    const IMAGE = 'image';

    /**
     * Get ID
     *
     * @return int|null
     */
    public function getId();

    /**
     * Get priority
     *
     * @return int|null
     */
    public function getPriority();

    /**
     * Get name
     *
     * @return string|null
     */
    public function getName();

    /**
     * Get short_description
     *
     * @return string|null
     */
    public function getShortDescription();

    /**
     * Get image
     *
     * @return string|null
     */
    public function getImage();

    /**
     * Set ID
     *
     * @param int $id
     * @return PictogramInterface
     */
    public function setId($id);

    /**
     * Set priority
     *
     * @param int $priority
     * @return PictogramInterface
     */
    public function setPriority($priority);


    /**
     * Set name
     *
     * @param string $name
     * @return PictogramInterface
     */
    public function setName($name);

    /**
     * Set short description
     *
     * @param string $short_description
     * @return PictogramInterface
     */
    public function setShortDescription($short_description);

    /**
     * Set image
     *
     * @param string $image
     * @return PictogramInterface
     */
    public function setImage($image);
}
