.pictogram-container {
    vertical-align: middle;
    display: inline-block;
    width: 100%;
    padding-bottom: 10px;
    z-index: 1;

    .pictogram {
        max-width: 50px;
        margin-right: 5px;
        margin-bottom: 5px;
        vertical-align: middle;
    }
}

.catalog-category-view {
    .pictogram-container {
        position: absolute;
        bottom: 0;
        left: 0;
        height: unset;
        width: unset;
        padding-left: 5px;
        padding-bottom: 0;
        display: flex;
        align-items: flex-end;
        flex-wrap: wrap-reverse;

        .pictogram {
            opacity: 0.5;
            transition: opacity .3s ease-in-out;

            &:hover {
                opacity: 1;
                cursor: pointer;
                transition: opacity .3s ease-in-out;
            }

            @media only screen and (max-width: 767px) {
                max-width: 40px;
            }

            @media only screen and (max-width: 640px) {
                max-width: 30px;
            }
        }
    }
}
