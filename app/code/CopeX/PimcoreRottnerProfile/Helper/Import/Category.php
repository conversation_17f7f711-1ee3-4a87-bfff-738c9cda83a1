<?php
/**
 * Created by PhpStorm.
 * User: pointi
 * Date: 01.06.18
 * Time: 07:01
 */

namespace CopeX\PimcoreRottnerProfile\Helper\Import;

use Magento\Catalog\Model\ResourceModel\Category as CategoryResource;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory;
use Magento\Framework\Filter\FilterManager;

class Category extends \CopeX\Pimcore\Helper\Category
{
    protected $errors;
    protected $processed;
    protected $attributesToSelect = ['ax_id', 'pimcore_id', 'name'];

    /**
     * @var CategoryResource
     */
    protected $categoryResource;
    protected $categoryAxCache;
    protected $pimCategories;
    /**
     * @var \Magento\Catalog\Model\CategoryFactory
     */
    protected $categoryFactory;
    private $langMapping = ["_en" => "en_eu", "_cs" => "cs_cz", "_fr" => "fr_fr", "_it" => "it_it"];

    public function __construct(
        CollectionFactory $categoryCollectionFactory,
        FilterManager $filter,
        \Magento\Catalog\Model\ResourceModel\CategoryFactory $categoryResourceFactory,
        \Magento\Catalog\Model\CategoryFactory $categoryFactory
    ) {
        parent::__construct($categoryCollectionFactory, $filter);
        $this->categoryResource = $categoryResourceFactory->create();
        $this->categoryFactory = $categoryFactory;
    }

    /**
     * @param $transport
     * @throws \Exception
     */
    public function importCategories($transport)
    {
        $items = $transport->getItems();
        $item = reset($items);
        $this->setMissingIdsToCategories($item);
        $transport->addItem(['skip' => 1]);
        return;
    }

    protected function setMissingIdsToCategories($item)
    {
        $this->pimCategories[$item['axId']] = $item['name'];
        $categoryIndex = $this->getCategoryIndex($item);
        $categoryPimId = $item['id'];
        $axId = $item['axId'];
        $categoryId = false;
        if (isset($this->categoryPimcoreCache[$categoryPimId])) {
            $categoryId = $this->categoryPimcoreCache[$categoryPimId];
        } elseif (isset($this->categories[$categoryIndex])) {
            $categoryId = $this->categories[$categoryIndex];
        }
        if (!isset($this->processed[$categoryId])) {
            if ($categoryId) {
                /** @var \Magento\Catalog\Model\Category $category */
                $category = $this->categoryCollection->getItemById($categoryId);
                $category->setPimcoreId($categoryPimId);
                $category->setAxId($axId);
                try {
                    $this->categoryResource->saveAttribute($category, 'pimcore_id');
                    $this->categoryResource->saveAttribute($category, 'ax_id');
                    $this->setLangNames($item, $category);
                } catch (\Exception $e) {
                    $this->errors [] = "Category " . $category->getID() . ": " . $e->getMessage();
                }
                $this->processed[$categoryId] = true;
            } else { //Create new categories
                $category = $this->categoryFactory->create();
                $category->setName(html_entity_decode($item['name']));
                $category->setPimcoreId($item['id']);
                $category->setAxId($item['axId']);
                $category->setIsActive(false);
                $parentId = $this->getParentCategoryIdByIndex($categoryIndex);
                $category->setParentId($parentId);
                $path = $this->categoriesCache[$parentId]->getPath();
                $category->setPath($path);
                try {
                    $this->categoryResource->save($category);
                    $categoryId = $category->getId();
                    $this->categories[$categoryIndex] = $categoryId;
                    $this->categoriesCache[$categoryId] = $category;
                    $this->processed[$categoryId] = true;
                    $this->setLangNames($item, $category);
                    $this->errors [] = "Creating category: $categoryIndex ($categoryPimId)";
                } catch (\Exception $exception) {
                    $this->errors [] = "Couldn't create category: $categoryIndex ($categoryPimId)";
                }
            }
        }
    }

    protected function getCategoryIndex($item)
    {
        $path = $this->getPimcorePath($item[self::FULLPATH]);
        $namePath = [];
        foreach ($path as $category) {
            $namePath [] = $this->formatUrlKey(
                isset($category['id'], $this->pimCategories[$category['id']]) ? $this->pimCategories[$category['id']]
                    : $category['name']
            );
        }
        return implode(self::DELIMITER_CATEGORY, $namePath);
    }

    /**
     * @param                                 $item
     * @param \Magento\Catalog\Model\Category $category
     * @throws \Exception
     */
    protected function setLangNames($item, \Magento\Catalog\Model\Category $category): void
    {
        $defaultName = $item['name'];
        foreach ($this->langMapping as $lang => $storeViewCode
        ) {
            $name = $item['name' . $lang] ?? "";
            if ($name) {
                $category->setStoreId($storeViewCode);
                $name = html_entity_decode($item['name' . $lang]);
                $category->setName($name);
                $this->categoryResource->saveAttribute($category, 'name');
                $langUrlKey = $this->categoryResource->getAttributeRawValue(
                    $category->getId(),
                    "url_key",
                    $category->getStoreId()
                );
                if ($category->formatUrlKey($defaultName) == $langUrlKey) {
                    $category->setUrlKey($category->formatUrlKey($name));
                    $this->categoryResource->saveAttribute($category, 'url_key');
                }
            }
        }
    }

    protected function initCategoriesItemAfter($category)
    {
        if ($category->hasPimcoreId()) {
            $this->categoryPimcoreCache[$category->getPimcoreId()] = $category->getId();
            $this->categoryAxCache[$category->getAxId()] = $category->getId();
        }
    }

    /**
     * @throws \Exception
     */
    public function successMessage()
    {
        if ($this->errors) {
            throw new \Exception(implode("\n", $this->errors));
        }
        throw new \Exception(__(
            "Category name import successful. Imported %1 categories.",
            count($this->categoryPimcoreCache)
        ));
    }
}
