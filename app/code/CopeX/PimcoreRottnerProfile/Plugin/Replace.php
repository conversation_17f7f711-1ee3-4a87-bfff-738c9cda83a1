<?php

namespace CopeX\PimcoreRottnerProfile\Plugin;

use CopeX\Pimcore\Helper\Pimcore;
use CopeX\PimcoreRottnerProfile\Helper\Delta;
use Magento\Framework\App\ResourceConnection;

class Replace
{

    /**
     * @var ResourceConnection
     */
    private $resourceConnection;

    private $lastRun = null;

    public function __construct(ResourceConnection $resourceConnection)
    {
        $this->resourceConnection = $resourceConnection;
    }

    /**
     * @param Pimcore $subject
     * @param         $result
     * @param         $value
     * @param null    $store
     */
    public function afterReplacePlaceholders(Pimcore $subject, $result, $value, $store = null)
    {
        if ($this->lastRun === null) {
            $this->lastRun = $this->resourceConnection->getConnection()->fetchOne("select value from core_config_data where path = '" . Delta::XML_DELTA_START . "'") ?? false;
            $this->lastRun = max(strtotime($this->lastRun),strtotime('today midnight'));
        }
        $result = strtr(
            $result,
            [
                "{{last_run}}" => $this->lastRun
            ]
        );
        return $result;
    }
}