<?php
/**
 * @var $block Magento\Catalog\Block\Category\View
 * @var $helper Magento\Catalog\Helper\Output
 */
$category = $block->getCurrentCategory();
$helper = $this->helper(Magento\Catalog\Helper\Output::class);
?>
<?php if ($category && $category->getTextBelow()): ?>
    <div class="category-text-below">
        <?= $helper->categoryAttribute($category, $category->getTextBelow(), 'text_below'); ?>
    </div>
<?php endif; ?>
