<?php
/**
 * Copyright © 2018 Porto. All rights reserved.
 */

?>
<?php
/**
 * Top menu for store
 *
 * @see \Smartwave\Megamenu\Block\Topmenu
 */
?>
<?php
    $_helper = $this->helper('Smartwave\Megamenu\Helper\Data');
    $_portohelper = $this->helper('Smartwave\Porto\Helper\Data');
    
    $categories = $_helper->getFirstLevelCategories();
    $_category_config = $_portohelper->getConfig('porto_settings/category');
    $_category_grid_config = $_portohelper->getConfig('porto_settings/category_grid');
    
    $aspect_ratio = $this->getData("aspect_ratio");
    if($aspect_ratio == null) {
        $aspect_ratio = $_category_config['aspect_ratio'];
    }
    $ratio_width = $this->getData("image_width");
    if(!$ratio_width) {
        $ratio_width = ($_category_config['ratio_width'])?$_category_config['ratio_width']:300;
    }
    $ratio_height = $this->getData("image_height");
    if(!$ratio_height) {
        $ratio_height = ($_category_config['ratio_height'])?$_category_config['ratio_height']:300; 
    }

    $move_actions= $this->getData("move_actions");
    if(!$move_actions)
        $move_actions = 0;
    $lazy_owl = $this->getData("lazy_owl");
    if(!$lazy_owl)
        $lazy_owl = 0;
    $product_count = $this->getData("product_count");
    if(!$product_count)
        $product_count = 12;
    $columns = $this->getData("columns");
    if(!$columns)
        $columns = "{0:{items:2},768:{items:3},992:{items:4},1200:{items:5}}";

    $product_type = $this->getData("product_type");
    if($product_type == null) {
        $product_type = $_category_grid_config['product_type'];
    }
    if($product_type == null) {
        $product_type = 1;
    }
    
    $custom_styles='<style type="text/css">';
?>
<div class="onepage-category">
    <div class="category-list">
        <ul>
        <?php
            $i = 0;
            foreach($categories as $category) {
                $cat_id = $category->getId();
                $cat_model = $_helper->getCategoryModel($cat_id);
                if($category->getIsActive() && !$cat_model->getData("sw_ocat_hide_this_item")) {
                    $icon = '<em class="porto-icon-circle-empty"></em>';
                    if($cat_icon_img=$cat_model->getData("sw_ocat_category_icon_image")) {
                        $icon = '<img src="'.'catalog/category/'.$cat_icon_img.'" alt=""/>';
                    } else if($cat_font_icon=$cat_model->getData("sw_ocat_category_font_icon")){
                        $icon = '<em class="'.$cat_font_icon.'"></em>';
                    }
                    if($hover_bg_color = $cat_model->getData("sw_ocat_category_hoverbgcolor"))
                        $custom_styles .= '.onepage-category .category-list > ul > li > a[data-cat="'.$cat_id.'"]:hover,.onepage-category .category-list > ul > li > a[data-cat="'.$cat_id.'"].active{background-color:'.$hover_bg_color.';}.onepage-category .category-list > ul > li > a[data-cat="'.$cat_id.'"]:hover:after,.onepage-category .category-list > ul > li > a[data-cat="'.$cat_id.'"].active:after{border-left-color:'.$hover_bg_color.';}';
                    echo '<li data-index="'.$i.'"><a href="javascript:void(0)" data-cat="'.$cat_id.'">'.$icon.'<span>'.$category->getName().'</span></a></li>';
                    $i++;
                }
            }
        ?>
        </ul>
        <?php
            $custom_styles .= '</style>';
            echo $custom_styles;
        ?>
    </div>
    <div class="category-details">
    <?php
        foreach($categories as $category) {
            $cat_id = $category->getId();
            $cat_model = $_helper->getCategoryModel($cat_id);
            if($category->getIsActive() && !$cat_model->getData("sw_ocat_hide_this_item")) {
                $activeChildren = $_helper->getActiveChildCategories($cat_model);        
                $a_class = '';
                $popup = '';
                if (count($activeChildren)) {
                    $a_class = ' class="parent"';
                    $popup = '<div class="menu-popup">'.$_helper->getSubmenuItemsHtml($activeChildren, 0, 2).'</div>';
                }
                
                $title_menu = '<a href="javascript:void(0)" data-id="'.$cat_id.'"'.$a_class.'><span>'.$cat_model->getName().'</span></a>'.$popup;
                
                $resultPage = $_helper->getResultPageFactory()->create();
                $products = $resultPage->getLayout()->createBlock('Smartwave\Filterproducts\Block\Home\LatestList')
                    ->setTemplate('Smartwave_Megamenu::onepagecategory/products_area.phtml')
                    ->setData('category_id',$cat_id)
                    ->setData('aspect_ratio',$aspect_ratio)
                    ->setData('image_width',$ratio_width)
                    ->setData('image_height',$ratio_height)
                    ->setData('lazy_owl',$lazy_owl)
                    ->setData('product_count',$product_count)
                    ->setData('columns',$columns)
                    ->setData('product_type',$product_type)
                    ->toHtml();
                
                $additional_content = $_helper->getBlockContent($cat_model->getData("sw_ocat_additional_content"));
    ?>
        <div id="category_<?php echo $cat_id; ?>" class="category-detail">
            <div class="title-menu"><?php echo $title_menu; ?></div>
            <div class="products-area"><?php echo $products; ?></div>
            <div class="additional-content"><?php echo $additional_content; ?></div>
        </div>
    <?php
            }
        }
    ?>
    </div>
    <script type="text/javascript">
    require([
        'jquery'
    ], function ($) {
        $(".category-detail > .title-menu > a.parent").off("click").on("click", function(e){
            if($(this).hasClass("opened")) {
                $(this).parent().children(".menu-popup").fadeOut(200);
                $(this).removeClass("opened");
            } else {
                $(this).addClass("opened");
                $(this).parent().children(".menu-popup").fadeIn(200);                
            }
            e.stopPropagation();
        });
        $(".category-detail > .title-menu > a.parent").parent().click(function(e){
            e.stopPropagation();
        });
        $("html,body").click(function(){
            $(".category-detail > .title-menu > a.parent").parent().children(".menu-popup").fadeOut(200);
            $(".category-detail > .title-menu > a.parent").removeClass("opened");
        });
        $(".onepage-category .category-list > ul > li > a").off("click").on("click", function(){
            cat_id = $(this).attr("data-cat");
            $("#category_"+cat_id).scrollToMe();
            var cur_item = $(this);
            setTimeout(function(){
                $(".onepage-category .category-list > ul > li > a").removeClass("active");
                $(cur_item).addClass("active");
            },500);
        });
        $(window).scroll(function(){
            $(".onepage-category .category-list > ul > li > a").each(function(){
                if($("#category_"+$(this).attr("data-cat")).offset() && ($(window).scrollTop() >= $("#category_"+$(this).attr("data-cat")).offset().top - $(window).innerHeight() / 2) && ($(window).scrollTop() <= $("#category_"+$(this).attr("data-cat")).offset().top + $("#category_"+$(this).attr("data-cat")).height() - $(window).innerHeight() / 2)) {
                    $(this).addClass("active");
                    $(".onepage-category .category-list > ul > li > a:not([data-cat='"+$(this).attr("data-cat")+"'])").removeClass("active");
                }
            });
            if($(".onepage-category .category-list > ul").outerHeight() < $(this).innerHeight()) {
                $(".onepage-category .category-list > ul").removeClass("fixed-bottom");
                if($(this).scrollTop() >= $(".onepage-category .category-list").offset().top - 24) {
                    $(".onepage-category .category-list > ul").addClass("fixed-top");
                } else {
                    $(".onepage-category .category-list > ul").removeClass("fixed-top");
                }
            } else {
                $(".onepage-category .category-list > ul").removeClass("fixed-top");
                if($(this).scrollTop() >= $(".onepage-category .category-list").offset().top + $(".onepage-category .category-list > ul").outerHeight() + 46 - $(this).innerHeight()) {
                    $(".onepage-category .category-list > ul").addClass("fixed-bottom");
                } else {
                    $(".onepage-category .category-list > ul").removeClass("fixed-bottom");
                }
            }
            if(($(".onepage-category .category-list > ul").hasClass("fixed-bottom") && ($(this).scrollTop() + $(window).innerHeight() >= $(".page-footer").offset().top)) || ($(".onepage-category .category-list > ul").hasClass("fixed-top") && ($(this).scrollTop() + $(window).innerHeight() >= $(".page-footer").offset().top) && ($(".onepage-category .category-list > ul").offset().top + $(".onepage-category .category-list > ul").outerHeight() >= $(".page-footer").offset().top) && ($(this).scrollTop() + $(".onepage-category .category-list > ul").outerHeight() + 70 >= $(".page-footer").offset().top))) {
                $(".onepage-category .category-list > ul").addClass("absolute-bottom");
            } else {
                $(".onepage-category .category-list > ul").removeClass("absolute-bottom");
            }
        });
    });
    </script>
</div>