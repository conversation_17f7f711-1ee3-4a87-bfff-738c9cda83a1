<root>
<config>
    <web>
        <default>
            <cms_home_page>porto_home_23</cms_home_page>
            <cms_no_route>no-route-2</cms_no_route>
        </default>
    </web>
    <cms>
        <wysiwyg>
            <enabled>hidden</enabled>
        </wysiwyg>
    </cms>
	<design>
		<header>
			<logo_width></logo_width>
			<logo_height></logo_height>
			<logo_src>default/logo_grey_italic.png</logo_src>
		</header>
	</design>
	<dev>
		<js>
			<merge_files>1</merge_files>
		</js>
	</dev>
    <porto_settings>
        <general>
            <boxed>wide</boxed>
            <layout>1140</layout>
            <disable_border_radius>1</disable_border_radius>
            <show_site_notice>1</show_site_notice>
            <custom_notice_block>porto_custom_notice_23</custom_notice_block>
        </general>
        <header>
            <header_type>21</header_type>
            <new_skin>1</new_skin>
            <static_block>porto_custom_block_for_header_21</static_block>
            <sticky_header>1</sticky_header>
            <sticky_header_logo></sticky_header_logo>
            <sticky_header_logo_src></sticky_header_logo_src>
            <language_flag>1</language_flag>
            <currency_short>1</currency_short>
        </header>
        <footer>
            <footer_newsletter_title><![CDATA[Newsletter]]></footer_newsletter_title>
            <footer_newsletter_text><![CDATA[Get all the latest information on Events,<br/>Sales and Offers. Sign up for newsletter today.]]></footer_newsletter_text>
            <footer_top>0</footer_top>
            <footer_top_block>custom</footer_top_block>
            <footer_top_custom></footer_top_custom>
            <footer_middle>1</footer_middle>
            <footer_ribbon>0</footer_ribbon>
            <footer_ribbon_text>Ribbon Text</footer_ribbon_text>
            <footer_middle_column_1>custom</footer_middle_column_1>
            <footer_middle_column_1_custom>porto_footer_middle_1_for_23</footer_middle_column_1_custom>
            <footer_middle_column_1_size>3</footer_middle_column_1_size>
            <footer_middle_column_2>custom</footer_middle_column_2>
            <footer_middle_column_2_custom>porto_footer_middle_2_for_23</footer_middle_column_2_custom>
            <footer_middle_column_2_size>3</footer_middle_column_2_size>
            <footer_middle_column_3>custom</footer_middle_column_3>
            <footer_middle_column_3_custom>porto_footer_middle_3_for_23</footer_middle_column_3_custom>
            <footer_middle_column_3_size>3</footer_middle_column_3_size>
            <footer_middle_column_4>newsletter</footer_middle_column_4>
            <footer_middle_column_4_custom></footer_middle_column_4_custom>
            <footer_middle_column_4_size>3</footer_middle_column_4_size>
            <footer_middle_2>0</footer_middle_2>
            <footer_middle_2_column_1>custom</footer_middle_2_column_1>
            <footer_middle_2_column_1_custom>porto_footer_links</footer_middle_2_column_1_custom>
            <footer_middle_2_column_1_size>3</footer_middle_2_column_1_size>
            <footer_middle_2_column_2>custom</footer_middle_2_column_2>
            <footer_middle_2_column_2_custom>porto_footer_links</footer_middle_2_column_2_custom>
            <footer_middle_2_column_2_size>3</footer_middle_2_column_2_size>
            <footer_middle_2_column_3>custom</footer_middle_2_column_3>
            <footer_middle_2_column_3_custom>porto_footer_links</footer_middle_2_column_3_custom>
            <footer_middle_2_column_3_size>3</footer_middle_2_column_3_size>
            <footer_middle_2_column_4>custom</footer_middle_2_column_4>
            <footer_middle_2_column_4_custom>porto_footer_links</footer_middle_2_column_4_custom>
            <footer_middle_2_column_4_size>3</footer_middle_2_column_4_size>
            <footer_bottom>1</footer_bottom>
            <footer_store_switcher>0</footer_store_switcher>
            <footer_logo_src></footer_logo_src>
            <footer_bottom_copyrights><![CDATA[&copy; Porto eCommerce. 2020. All Rights Reserved]]></footer_bottom_copyrights>
            <footer_bottom_custom_1></footer_bottom_custom_1>
            <footer_bottom_custom_2>porto_footer_bottom_custom_block_23</footer_bottom_custom_2>
        </footer>
        <category>
            <alternative_image>1</alternative_image>
            <aspect_ratio>1</aspect_ratio>
            <ratio_width>300</ratio_width>
            <ratio_height>300</ratio_height>
            <rating_star>1</rating_star>
            <product_price>1</product_price>
            <actions>1</actions>
            <addtocompare>1</addtocompare>
            <addtowishlist>1</addtowishlist>
            <page_layout>2columns-left</page_layout>
            <category_description>full_width</category_description>
            <side_custom_block></side_custom_block>
        </category>
        <category_grid>
            <product_type>3</product_type>
            <columns>4</columns>
        </category_grid>
        <product>
            <product_image_size>6</product_image_size>
            <side_custom_block></side_custom_block>
            <main_custom_block></main_custom_block>
            <main_custom_block2></main_custom_block2>
            <move_addtolinks>0</move_addtolinks>
            <move_upsell_full>0</move_upsell_full>
            <page_layout>1column</page_layout>
            <tab_full_background>0</tab_full_background>
            <custom_block_next_tab></custom_block_next_tab>
            <upsell_columns>5</upsell_columns>
            <move_upsell>0</move_upsell>
        </product>
		<newsletter>
			<logo_src>default/logo_black.png</logo_src>
		</newsletter>
        <contacts>
            <enable>1</enable>
            <full_width>0</full_width>
            <address>Porto2 Store</address>
            <latitude>-34.398</latitude>
            <longitude>150.884</longitude>
            <zoom>18</zoom>
            <infoblock><![CDATA[<div class="row">
<div class="col-md-12">
    <i class="porto-icon-phone"></i>
    <p>0201 203 2032</p>
    <p>0201 203 2032</p>
</div>
</div>
<div class="row">
<div class="col-md-12">
    <i class="porto-icon-mobile"></i>
    <p>************</p>
    <p>************</p>
</div>
</div>
<div class="row">
<div class="col-md-12">
    <i class="porto-icon-mail-alt"></i>
    <p><EMAIL></p>
    <p><EMAIL></p>
</div>
</div>
<div class="row">
<div class="col-md-12">
    <i class="porto-icon-skype"></i>
    <p>porto_skype</p>
    <p>proto_template</p>
</div>
</div>]]></infoblock>
            <customblock></customblock>
        </contacts>
        <custom_settings>
            <custom_google_fonts><![CDATA[@import url("https://fonts.googleapis.com/css/?family=Nanum+Brush+Script:400");]]></custom_google_fonts>
            <custom_style><![CDATA[body {
    letter-spacing: 0.2px;
}
@media (min-width: 1220px) {
    .navigation, .page-wrapper > .breadcrumbs, .header.content, .footer.content, .page-wrapper > .widget, .page-wrapper > .page-bottom, .block.category.event, .top-container, .page-main, .container, .page-wrapper > .breadcrumbs .items {
        max-width: 1200px;
        padding-left: 10px;
        padding-right: 10px;
    }
}
.products-grid .product-item-details .product-item-actions .tocart {
    text-transform: uppercase;
    font-size: 12.53px;
    font-family: 'Oswald';
    font-weight: 400;
    letter-spacing: 0.025em;
    color: #6f6e6c;
    line-height: 30px;
    background-color: #f4f4f4;
    border-color: #f4f4f4;
}
.product-item .tocart:before {
    content: '\e87f';
    font-size: 17px;
    vertical-align: middle;
}
.product-social-links .action.towishlist:before, .product-addto-links .action.towishlist:before, .block-bundle-summary .action.towishlist:before, .product-item .action.towishlist:before, .table-comparison .action.towishlist:before {
    content: '\e889';
}
@media (min-width: 1200px) {
.products-grid.columns6 {
    margin-left: -10px;
    margin-right: -10px;
}
.products-grid.columns6 .product-item {
    padding: 10px;
}
}
.filterproduct-title {
    background: none;
    border-bottom: 1px solid rgba(0,0,0,0.08);
    margin-bottom: 24px;
}
.filterproduct-title .content strong {
    font-weight: 600;
}
.filterproduct-title .content {
    padding: 0;
    background: transparent;
    text-transform: none;
}
.shop-by-cat-item.cat-has-icon:hover {
    color: #f4631b;
}
.footer-top .block.newsletter {
    width: 100%;
    margin: 20px 0;
}
.footer-top {
    padding: 5px 0;
}
.footer-top .block.newsletter input {
    border-radius: 30px 0 0 30px;
    height: 48px;
    padding-left: 25px;
    border: 0;
}
.footer-top .block.newsletter .control:before {
    display: none;
}
.footer-top .block.newsletter .actions button {
    background-color: #222529;
    height: 48px;
    text-transform: uppercase;
    padding: 0 25px;
    border-radius: 0 30px 30px 0;
    border: 0;
}

.custom-support {
    text-align: center;
}
.custom-support i {
    background-color: transparent;
    float: none;
    color: #f4631b;
    width: auto;
    height: auto;
    border-radius: 0;
    padding-bottom: 20px;
    display: inline-block;
}
.custom-support div.content {
    margin-left: 0;
}
.custom-support div.content>h2 {
    text-transform: uppercase;
    font-size: 16px;
    color: #2b2b2d;
    line-height: 22px;
}
.custom-support div.content>em {
    color: #465157;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    margin-bottom: 20px;
}
.custom-support div.content>p {
    font-size: 13px;
    line-height: 24px;
    color: #687176;
}
.product-item-name > a, .product.name a > a {
    color: #222329;
}

.footer .social-icons [class^="porto-icon-"] {
    color: #222529;
    font-size: 16px;
}
.social-icons > a {
    width: 30px;
    height: 30px;
    line-height: 30px;
    display: inline-block;
    text-align: center;
    border-radius: 20px;
}
.footer .social-icons a [class^="porto-icon-"] {
    color: inherit;
}
.footer .social-icons a[title="Facebook"]:hover {
    background-color: #3b5a9a;
    color: #fff;
}
.footer  .social-icons a[title="Twitter"]:hover {
    background-color: #1aa9e1;
    color: #fff;
}
.footer .social-icons a[title="Linkedin"]:hover {
    background-color: #7c4a3a;
    color: #fff;
}
.footer-middle .social-icons a {
    color: #222329;
    transition: all .3s;
    margin-right: 3px;
}
.footer-middle {
    padding: 65px 0 30px;
}
.footer-middle .block .block-title strong {
    font-size: 16px;
    font-weight: 600;
}
.footer-middle ul.links li {
    padding: 6px 0;
}
.footer-middle .links a {
    line-height: 19px;
    text-decoration: none;
}
.footer-bottom {
    border-top: 1px solid #e7e7e7;
    padding: 16px 0;
}
.footer-bottom address {
    float: left;
    line-height: 42px;
}
.page-wrapper > .breadcrumbs {
    max-width: 100%;
    border-top: 1px solid #dfdfdf;
    border-bottom: 1px solid #dfdfdf;
    padding: 7px 0;
}
.page-wrapper > .breadcrumbs a, .page-wrapper > .breadcrumbs strong {
    text-transform: uppercase;
    font-weight: 700;
}
.page-main {
    padding-top: 30px;
}
.sidebar.sidebar-main {
    position: relative;
}
.block-category-list .block-title,.filter-options-title,.filter .filter-current-subtitle {
    border: none;
    background-color: transparent;
    padding: 16px 20px 4px 20px;
    font-size: 15px;
    text-transform: uppercase;
    font-weight: 600;
    color: #21293c;
    letter-spacing: 0.01em;
}
.block-category-list .block-title {
    padding: 0 20px 0 20px;
}
.block-category-list .block-title strong {
    font-weight: 600;
}
.block-category-list .block-content,.filter-options-content {
    border: none;
    background-color: transparent;
    padding: 10px 20px 26px 20px;
    border-bottom: #efefef solid 1px;
    position: relative;
    z-index: 2;
    border-radius: 0;
}
.filter-current .items {
    border: none;
    background-color: transparent;
    position: relative;
    z-index: 2;
}
.filter-current .item {
    padding-left: 20px;
}
.filter-current .action.remove {
    right: 20px;
    color: #21293c;
}
.filter-actions {
    border: none;
    background-color: transparent;
    border-bottom: #efefef solid 1px;
    position: relative;
    z-index: 2;
    padding-right: 20px;
}
.filter-actions a {
    color: #858585;
}
.filter-options-title:after {
    content: '\f882';
    border: none;
    color: #21293c;
    font-size: 11px;
    margin-top: -6px;
    right: 0;
}
.filter-options-title:hover:after {
    background: none;
    color: #21293c;
}
.active > .filter-options-title:after {
    content: '\f883';
}
#ln_slider_price.ui-slider-horizontal {
    height: 3px;
    box-shadow: none;
}
#ln_slider_price .ui-slider-handle {
    width: 12px;
    height: 12px;
    border-radius: 100%;
}
.sidebar-title {
    font-size: 15px;
    font-weight: 600;
    color: #21293c;
    letter-spacing: 0.01em;
    margin-bottom: 20px;
    padding-left:20px;
    padding-right: 20px;
}
.porto-icon-left-open-huge:before {
    content: '\f802';
    color: #21293c;
}
.porto-icon-right-open-huge:before {
    content: '\f801';
    color: #21293c;
}
.sidebar .owl-top-narrow .owl-theme .owl-controls {
    top: -40px;
    right: -7px;
}
.sidebar .owl-top-narrow .owl-theme .owl-controls .owl-nav div *:before {
    color:#21293c;
}
.sidebar .product-items .product-item-name a {
    font-size: 12px;
    color: #5b5b5f;
    font-weight: 400;
}
.sidebar .sidebar-filterproducts {
    margin-bottom: 30px;
    padding-bottom: 40px;
    background: none;
    border-bottom: #efefef solid 1px;
}
.sidebar .product-items .product-item .product-reviews-summary {
    display: block;
}
.sidebar-filterproducts.custom-block + h2 {
    font-size: 15px !important;
    text-transform: uppercase;
    font-weight: 600;
    color: #21293c !important;
    letter-spacing: 0.01em;
    padding: 0 20px;
}
.sidebar-filterproducts.custom-block + h2 +h5 {
    font-family: 'Open Sans' !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    color: #7a7d82 !important;
    letter-spacing: 0.022em;
    padding: 0 20px;
}
.sidebar-filterproducts.custom-block + h2 + h5 + p {
    color: #21293c !important;
    font-size: 15px !important;
    letter-spacing: 0.01em;
    padding: 0 20px;
}
.sidebar .custom-block {
    padding: 0 20px;
}
.category-boxed-banner.owl-theme .owl-controls {
    bottom: 0;
}
.page-products .toolbar .limiter {
    display: block;
}
.page-with-filter .toolbar-amount {
    display: none;
}
.full-width-image-banner {
    height: 300px;
}
.full-width-image-banner:after {
    display: none;
}
.full-width-image-banner h2 {
    letter-spacing: -0.025em;
    text-transform: uppercase;
}
.full-width-image-banner p {
    font-size:18px;
    line-height:38px;
    font-weight: 700;
    text-transform:uppercase;
}
.full-width-image-banner .btn-default {
    font-size: 14px;
    line-height: 25px;
    letter-spacing: 0.025em;
    padding: 10px 20px;
    background-color: #010204;
    color: #fff;
    font-family: 'Oswald';
    text-transform: uppercase;
    border-radius: 2px;
    margin-top: 31px;
}
.page-products .toolbar .limiter .limiter-text {
    display: none;
}
.modes-mode.active {
    border: none;
    background: none;
    color: #111;
}
.modes-mode,.modes-mode:hover {
    border: none;
    background: none;
    color: #111;
    width: 15px;
}
.toolbar select {
    border: 1px solid #e4e4e4;
    height: 37px;
    color: #7a7d82;
    font-weight: 400;
    font-size: 14px;
    text-transform: capitalize;
    padding: 0 10px;
    padding-right: 30px;
    line-height: 31px;
}
.toolbar-sorter .sorter-action {
    margin-top: 6px;
    color: #21293c;
}
.toolbar-sorter .sorter-action:before {
    color: #21293c;
}
.pages a.page,.pages strong.page,.pages .action {
    width: 32px;
    line-height: 32px;
}
.products-grid + .toolbar.toolbar-products {
    border-top: solid 1px #efefef;
    padding-top: 25px;
}
.product-item .product-reviews-summary {
    background: none;
}
.product.name a {
    color: #5b5b5f;
}
.price-box .price {
    font-family: Poppins;
    font-size: 18px;
    letter-spacing: 0.2px;
    font-weight: 600;
}
.old-price .price, .old.price {
    font-size: 14px;
}
.product-label.sale-label {
    border-radius: 20px;
    color: #ffffff;
}
.product-label.new-label {
    border-radius: 20px;
    color: #ffffff;
}
.product-labels .product-label {
    height: 21px;
    font-size: 11px;
    line-height: 21px;
    padding: 0 10px;
}
.products-grid .product-item-details .product-item-actions .tocart {
    text-transform: uppercase;
    font-size: 12.53px;
    font-family: 'Oswald';
    font-weight: 400;
    letter-spacing: 0.025em;
    color: #6f6e6c;
    line-height: 30px;
    background-color: #f4f4f4;
    border-color: #f4f4f4;
}
.product-item .tocart:before {
    content: '\e87f';
    font-size: 17px;
    vertical-align: middle;
}
.catalog-product-view .sidebar .custom-block {
    border: none;
    color: #6b7a83;
    padding-bottom: 0;
    margin-bottom: 33px;
    background: none;
}
.catalog-product-view .sidebar .custom-block-1>div i {
    color: #F4631B;
    border: none;
    font-size: 40px;
    float: left;
}
.catalog-product-view .sidebar .custom-block-1>div {
    min-height: 65px;
    clear: both;
    padding: 18px 0;
    border-bottom: solid 1px #dee5e8;
    margin-bottom: 0;
}
.catalog-product-view .sidebar .custom-block-1>div:last-child {
    border-bottom-width: 0;
}
.block-manufacturer {
    text-align: center;
    padding: 10px 20px 0;
    margin-bottom: 0;
}
.block-manufacturer hr {
    border-color: #dee5e8;
    margin-bottom: 0;
}
.catalog-product-view .sidebar .custom-block-1>div h3 {
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    letter-spacing: 0.005em;
    color: #6b7a83;
    margin-left: 80px;
}
.block.related {
    padding: 0 20px;
}
.block .title strong {
    font-size: 15px;
    font-weight: 600;
    color: #21293c;
    letter-spacing: 0.01em;
    margin-bottom: 20px !important;
    padding-top: 0;
    text-transform: uppercase;
}
.block.related .product-items .product-item-actions {
    display: none;
}
.product-info-main .page-title-wrapper h1 {
    font-size: 25px;
    font-weight: 600;
    letter-spacing: -0.01em;
    color: #21293c;
    margin: 3px 0 15px;
}
.prev-next-products a {
    color: #555;
}
.product-reviews-summary .reviews-actions a {
    line-height: 20px;
    font-size: 14px;
    color: #bdbdbd;
}
.product-info-main .product.overview {
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.005em;
    line-height: 27px;
    border-bottom: solid 1px #dae2e6;
}
.product.media {
    padding-right: 12px;
}
.fotorama__stage__shaft {
    border: none;
}
.fotorama__nav--thumbs .fotorama__thumb {
    border-color: #dae2e6;
}
.product-options-bottom .price-box .price-container .price, .product-info-price .price-box .price-container .price {
    font-family: 'Oswald';
    font-size: 21px;
    font-weight: 700;
    letter-spacing: 0.005em;
}
.product-info-main .product-info-price .old-price .price-wrapper .price {
    font-size: 16px;
    color: #2b2b2d;
    font-weight: 400;
}
.product-info-main .fieldset > .field.qty, .product-info-main .nested.options-list > .field.qty {
    position: relative;
    width: 106px;
}
.product-info-main .qty.field .control, .product-info-main .qty.field .qty-changer {
    margin-left: 29px;
}
.product-info-main .qty.field .qty-changer > a {
    position: absolute;
    top: 0;
    left: 0;
    height: 43px;
    width: 30px;
    line-height: 41px;
    text-align: center;
    margin: 0;
    border-color: #dae2e6;
}
.product-info-main .qty.field .qty-changer > a:first-child {
    left: auto;
    right: 4px;
}
.product-info-main .box-tocart .input-text.qty {
    font-family: 'Oswald';
    display: inline-block;
    vertical-align: middle;
    height: 43px;
    width: 44px!important;
    font-size: 14px;
    font-weight: 400;
    text-align: center;
    color: #61605a;
    margin: 0;
    border-color: #dae2e6;
}
.product-info-main .qty.field .qty-changer > a .porto-icon-up-dir:before {
    content: '\f882';
}
.product-info-main .qty.field .qty-changer > a .porto-icon-down-dir:before {
    content: '\f883';
}
.catalog-product-view:not(.weltpixel-quickview-catalog-product-view):not(.weltpixel_quickview-catalog_product-view) .box-tocart .action.tocart {
    height: 43px;
    font-size: 14px;
    letter-spacing: 0.05em;
    font-weight: 400;
}
.box-tocart .action.tocart:before {
    content: '\e87f';
    font-family: 'porto-icons';
    margin-right: 7px;
    font-size: 16px;
}
.product-addto-links .action.towishlist,.product-addto-links .action.tocompare,.product-social-links .action.mailto.friend {
    width: 43px;
    height: 43px;
    line-height: 41px;
    color: #302e2a;
}
.product.data.items > .item.content {
    background-color: #fff;
    box-shadow: none;
    border: none;
    border-top: #dae2e6 solid 1px;
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.005em;
    line-height: 27px;
}

.main-upsell-product-detail .block.upsell .title strong {
    background: none;
}
.block.upsell .title {
    background: none;
    border-bottom: #e1e1e1 solid 1px;
    font-weight: 700;
    margin-bottom: 16px;
    padding-bottom: 10px;
    text-transform: uppercase;
    text-align: left;
}
.block.upsell .title strong {
    font-size: 14px;
    font-weight: 400;
    font-family: 'Oswald';
    color: #302e2a;
}
.review-ratings .rating-label {
    display: block;
}
.page-products .sorter {
    float: left;
}
.modes {
    float: right;
    margin-right: 0;
    margin-left: 20px;
    margin-top: 7px;
}
.modes-mode:before {
    content: '\e880';
    font-size: 14px;
}
.mode-list:before {
    content: '\e87b';
    font-size: 14px;
}
.products.wrapper ~ .toolbar .limiter {
    float: left;
}
.products.wrapper ~ .toolbar .pages {
    float: right;
}
.block-category-list .block-title, .filter-options-title, .filter .filter-current-subtitle {
    padding-left: 0;
    padding-right: 0;
}
.swatch-option.text {
    font-weight: 400;
}
.catalog-category-view .column.main .products-grid {
    padding-top: 20px;
}
@media (min-width: 768px) {
    .product.data.items > .item.title {
        padding: 10px 30px 10px 0;
    }
    .product.data.items > .item.title > .switch {
        font-size: 14px;
        font-weight: 700;
        color: #818692;
        text-transform: uppercase;
        border: none;
        border-radius: 0;
        line-height: 30px;
        background: none;
        padding: 0;
    }
    .product.data.items > .item.title:not(.disabled) > .switch:focus,
    .product.data.items > .item.title:not(.disabled) > .switch:hover {
        background: none;
        color: #818692;
    }
    .product.data.items > .item.title.active > .switch,
    .product.data.items > .item.title.active > .switch:focus,
    .product.data.items > .item.title.active > .switch:hover {
        color: #21293c;
        position: relative;
        border-bottom: #c59b9c solid 2px !important;
    }
    .product.data.items > .item.content {
        padding: 35px 0 0;
        margin-top: 45px;
    }
}
@media(min-width: 768px){
.page-header.type21 .sw-megamenu.navigation > ul {
    padding-right: 110px;
}
}
.owl-middle-narrow .owl-theme .owl-controls .owl-nav [class*=owl-], .owl-middle-narrow.owl-theme .owl-controls .owl-nav [class*=owl-] {
    font-size: 24px;
    color: #333;
}
.row {
    margin-left: -10px;
    margin-right: -10px;
}
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12, .col, .col-auto, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm, .col-sm-auto, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-md, .col-md-auto, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg, .col-lg-auto, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl, .col-xl-auto {
    padding-right: 10px;
    padding-left: 10px;
}
.row.no-gutters {
    margin: 0;
}
.products-grid .product-item .product-item-info .weltpixel-quickview.weltpixel_quickview_button_v2 {
    font-family: Poppins;
    font-weight: 700;
}
.page-footer {
    border-top: 1px solid #e7e7e7;
}
.footer .contact-info strong {
    display: block;
    text-transform: uppercase;
    color: #222529;
    line-height: 26px;
}
.footer .contact-info span {
    display: block;
    line-height: 26px;
    margin-bottom: 10px;
}
.footer .social-icons {
    margin: 20px 0;
}
.footer-middle .block.newsletter p {
    line-height: 2;
}
.footer-middle .block.newsletter input {
    height: 44px;
}
.footer-middle .block.newsletter .action.subscribe {
    height: 44px;
    font-family: Poppins;
    font-weight: 700;
    font-size: 12px;
}
.breadcrumbs .items > li {
    vertical-align: middle;
}
.page-wrapper > .breadcrumbs a, .page-wrapper > .breadcrumbs strong {
    text-transform: uppercase;
    font-weight: 400;
    font-size: 10px;
    vertical-align: middle;
}
.page-wrapper > .breadcrumbs .item:not(:last-child):after {
    font-size: 7px;
}
.action.primary, .action.primary:active {
    text-transform: uppercase;
}
.minicart-wrapper .block-minicart:after {
    right: 19px;
}
.minicart-wrapper .block-minicart:before {
    right: 20px;
}
.footer-middle > .container, .footer-bottom > .container {
    padding-left: 3%;
    padding-right: 3%;
    max-width: 100%;
}
.footer-middle ul.links li {
    padding: 3.5px 0;
}
@media (min-width: 992px) {
    .footer-middle > .container > .row > div:nth-child(2n) {
        flex: 0 0 22%;
        max-width: 22%;
    }

    .footer-middle > .container > .row > div {
        flex: 0 0 28%;
        max-width: 28%;
    }
}]]></custom_style>
            <custom_style_2><![CDATA[]]></custom_style_2>
        </custom_settings>
    </porto_settings>
    <porto_design>
        <general>
            <theme_color>C59B9C</theme_color>
        </general>
        <font>
            <custom>1</custom>
            <font_size></font_size>
            <font_family>google</font_family>
            <custom_font_family></custom_font_family>
            <google_font_family>Poppins</google_font_family>
            <char_latin_ext></char_latin_ext>
            <char_subset></char_subset>
        </font>
        <colors>
            <custom>1</custom>
            <text_color></text_color>
            <link_color></link_color>
            <link_hover_color></link_hover_color>
            <button_bg_color></button_bg_color>
            <button_text_color></button_text_color>
            <button_hover_bg_color></button_hover_bg_color>
            <button_hover_text_color></button_hover_text_color>
            <addtowishlist_color>C1C3C5</addtowishlist_color>
            <addtowishlist_hover_color>302E2A</addtowishlist_hover_color>
            <addtocompare_color>C1C3C5</addtocompare_color>
            <addtocompare_hover_color>302E2A</addtocompare_hover_color>
            <breadcrumbs_bg_color>FFFFFF</breadcrumbs_bg_color>
            <breadcrumbs_color>000000</breadcrumbs_color>
            <breadcrumbs_links_color>000000</breadcrumbs_links_color>
            <breadcrumbs_links_hover_color>000000</breadcrumbs_links_hover_color>
            <sale_bg_color></sale_bg_color>
            <sale_font_color></sale_font_color>
            <new_bg_color></new_bg_color>
            <new_font_color></new_font_color>
            <price_slider_bg_color>1FC0A0</price_slider_bg_color>
            <price_slider_handle_color>1FC0A0</price_slider_handle_color>
        </colors>
        <header>
            <custom>1</custom>
            <header_bgcolor></header_bgcolor>
            <header_bg_image></header_bg_image>
            <header_bordercolor></header_bordercolor>
            <header_textcolor></header_textcolor>
            <header_linkcolor></header_linkcolor>
            <header_top_links_bgcolor></header_top_links_bgcolor>
            <header_top_links_color></header_top_links_color>
            <header_menu_bgcolor></header_menu_bgcolor>
            <header_menu_color></header_menu_color>
            <header_menu_hover_bgcolor></header_menu_hover_bgcolor>
            <header_menu_hover_color>C59B9C</header_menu_hover_color>
            <header_menu_classicmenu_bgcolor></header_menu_classicmenu_bgcolor>
            <header_menu_classicmenu_bordercolor></header_menu_classicmenu_bordercolor>
            <header_menu_classicmenu_color></header_menu_classicmenu_color>
            <header_menu_classicmenu_hover_bgcolor></header_menu_classicmenu_hover_bgcolor>
            <header_menu_classicmenu_hover_color></header_menu_classicmenu_hover_color>
            <header_menu_megamenu_color></header_menu_megamenu_color>
            <header_search_bgcolor></header_search_bgcolor>
            <header_search_text_color></header_search_text_color>
            <header_search_bordercolor></header_search_bordercolor>
            <header_minicart_bgcolor></header_minicart_bgcolor>
            <header_minicart_color></header_minicart_color>
            <header_minicart_icon_color></header_minicart_icon_color>
        </header>
        <footer>
            <custom>1</custom>
            <footer_top_bgcolor></footer_top_bgcolor>
            <footer_top_color></footer_top_color>
            <footer_top_link_color></footer_top_link_color>
            <footer_top_link_hover_color></footer_top_link_hover_color>
            <footer_middle_bgcolor>FFFFFF</footer_middle_bgcolor>
            <footer_middle_color>777777</footer_middle_color>
            <footer_middle_link_color>777777</footer_middle_link_color>
            <footer_middle_link_hover_color>777777</footer_middle_link_hover_color>
            <footer_middle_title_color>2B2B2D</footer_middle_title_color>
            <footer_middle_links_icon_color>777777</footer_middle_links_icon_color>
            <footer_middle_ribbon_bgcolor></footer_middle_ribbon_bgcolor>
            <footer_middle_ribbon_shadow_color></footer_middle_ribbon_shadow_color>
            <footer_middle_ribbon_color></footer_middle_ribbon_color>
            <footer_middle_2_bgcolor></footer_middle_2_bgcolor>
            <footer_middle_2_color></footer_middle_2_color>
            <footer_middle_2_link_color></footer_middle_2_link_color>
            <footer_middle_2_link_hover_color></footer_middle_2_link_hover_color>
            <footer_middle_2_title_color></footer_middle_2_title_color>
            <footer_middle_2_links_icon_color></footer_middle_2_links_icon_color>
            <footer_bottom_bgcolor>FFFFFF</footer_bottom_bgcolor>
            <footer_bottom_color>777777</footer_bottom_color>
            <footer_bottom_link_color>777777</footer_bottom_link_color>
            <footer_bottom_link_hover_color>777777</footer_bottom_link_hover_color>
        </footer>
        <page>
            <custom>0</custom>
            <page_bgcolor></page_bgcolor>
            <page_bg_image></page_bg_image>
            <page_custom_style><![CDATA[]]></page_custom_style>
        </page>
        <main>
            <custom>0</custom>
            <main_bgcolor></main_bgcolor>
            <main_bg_image></main_bg_image>
            <main_custom_style><![CDATA[]]></main_custom_style>
        </main>
    </porto_design>
    <mpsearch>
        <general>
            <enabled>1</enabled>
        </general>
    </mpsearch>
</config>
</root>