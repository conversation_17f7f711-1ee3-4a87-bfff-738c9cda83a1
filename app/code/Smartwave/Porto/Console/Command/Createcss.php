<?php

declare(strict_types=1);

namespace Smartwave\Porto\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class Createcss extends Command
{

    /** @var \Smartwave\Porto\Model\Cssconfig\Generator  */
    private \Smartwave\Porto\Model\Cssconfig\Generator\Proxy $cssenerator;
    private \Magento\Framework\App\State $state;

    public function __construct(\Smartwave\Porto\Model\Cssconfig\Generator\Proxy $cssenerator,
        \Magento\Framework\App\State $state,
        string $name = null)
    {
        parent::__construct($name);
        $this->cssenerator = $cssenerator;
        $this->state = $state;
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $this->state->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML); // or \Magento\Framework\App\Area::AREA_ADMINHTML, depending on your needs
        $output->writeln("Creating Settings CSS for Stores");
        $this->cssenerator->generateCss("settings",false,false);
        $output->writeln("Creating Design CSS for Stores");
        $this->cssenerator->generateCss("design",false,false);
        $output->writeln("Creating Porto Design Done!");
        return 0;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName("porto:createcss");
        $this->setDescription("create css for porto theme");
        parent::configure();
    }
}