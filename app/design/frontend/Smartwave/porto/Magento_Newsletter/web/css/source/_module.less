// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//    Common
//--------------------------------------

& when (@media-common = true) {

//
//    Newsletter subscription
//--------------------------------------

.block.newsletter {
    margin-bottom: 20px;
    .form.subscribe {
        display: table;
        width: 100%;
    }
    .fieldset {
        display: table-cell;
        margin: 0;
        padding: 0;
        vertical-align: top;
    }
    .field {
        margin: 0;
        .control {
            .lib-icon-font(
                @icon-envelope,
                @_icon-font-size: 16px,
                @_icon-font-line-height: 32px,
                @_icon-font-color: @form-element-input-placeholder__color,
                @_icon-font-margin: 0 0 0 8px
            );
            &:before {
                position: absolute;
            }
            display: block;
        }
    }
    input {
        padding-left: 35px;
    }
    .title {
        display: none;
    }
    .label {
        &:extend(.abs-visually-hidden all);
    }
    .actions {
        display: table-cell;
        vertical-align: top;
        width: 1%;
    }
    .action.subscribe {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        margin-left: -1px;
    }
}

}

//
//    Desktop
//--------------------------------------
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .block.newsletter {
        width: 34%;
    }
}
