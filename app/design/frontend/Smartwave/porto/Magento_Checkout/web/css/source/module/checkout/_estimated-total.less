// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //
    //  Checkout Estimated Total
    //  ---------------------------------------------

    .opc-estimated-wrapper {
        &:extend(.abs-no-display-desktop all);
        &:extend(.abs-add-clearfix all);
        .lib-css(background, @checkout-step-content-mobile__background);
        .lib-css(border-bottom, @checkout-step-title__border);
        .lib-css(border-top, @checkout-step-title__border);
        .lib-css(margin, -21px -(@checkout-step-content-mobile__margin-s) @checkout-step-content-mobile__margin-s);
        padding: 18px 15px;

        .estimated-block {
            .lib-css(font-size, @checkout-step-title-mobile__font-size);
            float: left;
            font-weight: 600;

            .estimated-label {
                display: block;
                margin: 0 0 @indent__xs;
            }
            .estimated-price {
                color: @theme-color;
            }
        }

        .minicart-wrapper {
            float: right;
            
            button {
                // todo ui: Change .action.showcart to .action-showcart
                &.action.showcart {
                    .lib-button-reset();
                    height: auto;
                    
                    .counter.qty {
                        left: 0;
                    }
                }
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .opc-estimated-wrapper {
        display: none;
    }
}
