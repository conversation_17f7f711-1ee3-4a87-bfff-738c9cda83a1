// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@minicart__padding-horizontal: @indent__base;

@minicart-qty__height: 24px;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //
    //  Minicart
    //  ---------------------------------------------

    .block-minicart {
        .items-total {
            float: right;
            margin-top: 20px;
            .count {
                font-weight: @font-weight__bold;
                color: #000;
            }
        }
        .subtotal {
            text-align: left;
            .label {
                &:extend(.abs-colon all);
            }
        }
        .amount {
            .price-wrapper {
                color: #000;
                &:first-child {
                    .price {
                        font-size: @font-size__l;
                        font-weight: @font-weight__bold;
                    }
                }
            }
        }
        .subtitle {
            display: none;
            &.empty {
                display: block;
                padding: @indent__l 0 @indent__base;
                text-align: center;
                font-size: 13px;
                font-weight: 400;
            }
        }
        .text {
            &.empty {
                text-align: center;
            }
        }
        .block-content {
            > .actions {
                margin-top: 15px;
                > .secondary {
                    text-align: center;
                    margin: 0 0 15px;
                    .action {
                        &.viewcart {
                            display: block;
                            width: 100%;
                            padding: 10px 15px;
                            text-transform: uppercase;
                            font-size: 15px;
                            border: 1px solid #ccc;
                            border-radius: 3px;
                        }
                    }
                }
                > .primary {
                    margin: 0 0 15px;
                    .action {
                        &.primary {
                            &:extend(.abs-button-l all);
                            display: block;
                            width: 100%;
                            padding: 10px 15px;
                            font-weight: 400;
                            text-transform: uppercase;
                            font-size: 15px;
                        }
                    }
                }
                .paypal-logo {
                    margin-top: 15px;
                    text-align: center;
                }
            }
        }
        .block-category-link,
        .block-product-link,
        .block-cms-link,
        .block-banners {
            margin: 15px 0 0;
            text-align: center;
        }
    }

    .minicart-wrapper {
        .lib-dropdown(
            @_toggle-selector: ~".action.showcart",
            @_options-selector: ~".block-minicart",
            @_dropdown-toggle-icon-content: @icon-mini-cart,
            @_dropdown-toggle-active-icon-content: @icon-mini-cart,
            @_dropdown-list-item-padding: false,
            @_dropdown-list-item-hover: false,
            @_dropdown-list-margin-top: 7px,
            @_dropdown-list-border: 0,
            @_dropdown-list-pointer-position-top: -21px,
            @_icon-font-position: before,
            @_icon-font-size: 35px,
            @_icon-font-line-height: 33px,
            @_icon-font-color: @minicart-icons-color,
            @_icon-font-color-hover: @minicart-icons-color-hover,
            @_icon-font-color-active: @minicart-icons-color
        );
        .block-minicart {
            .lib-css(padding, 25px @minicart__padding-horizontal);
            width: 320px;
            right: -15px;
            border-top: 6px solid @theme-color;
            color: @minicart__text-color;
            & when (@enable_border_radius = true) {
                border-radius: 8px;
            }
            box-shadow: 0 0 5px rgba(0,0,0,0.25);
            .block-title {
                display: none;
            }
            &:after {
                left: auto;
                right: 25px;
                border: 9px solid;
                border-color: transparent transparent rgba(0,0,0,0.1) transparent;
            }
            &:before {
                left: auto;
                right: 26px;
                border: 8px solid;
                border-color: transparent transparent @theme-color transparent;
            }
        }
        .product {
            .actions {
                float: right;
                margin: -30px 0 0;
                > .primary,
                > .secondary {
                    display: inline;
                    &:not(:last-child) {
                        margin-right: 15px;
                    }
                }
            }
        }
        .action {
            &.close {
                width: 40px;
                height: 40px;
                top: 0;
                right: 0;
                position: absolute;
                .lib-button-reset();
                .lib-button-icon(
                    @icon-remove,
                    @_icon-font-color: #777,
                    @_icon-font-size: 16px,
                    @_icon-font-line-height: 16px,
                    @_icon-font-text-hide: true
                );
            }
            &.showcart {
                white-space: nowrap;
                .text {
                    &:extend(.abs-visually-hidden all);
                }
                .counter.qty {
                    color: @minicart__qty_color;
                    .lib-css(height, @minicart-qty__height);
                    .lib-css(line-height, @minicart-qty__height);
                    border-radius: 2px;
                    display: inline-block;
                    margin: -11px 0 0;
                    min-width: 18px;
                    width: 100%;
                    overflow: hidden;
                    padding: 0 3px;
                    text-align: center;
                    white-space: normal;
                    position: absolute;
                    top: 50%;
                    left: 0;
                    font-weight: 600;
                    font-size: 14px;
                    .loader {
                        > img {
                            .lib-css(max-width, @minicart-qty__height);
                        }
                    }
                }
                .counter-label {
                    &:extend(.abs-visually-hidden all);
                }
                &:before, &.active:before {
                    color: @minicart__icon_color;
                    line-height: 40px;
                    margin: 0;
                }
                &:hover:before, &:active:before, &.active:before, &.active:hover:before {
                    color: @minicart__icon_color;
                }
            }
        }
        .minicart-widgets {
            margin-top: 15px;
        }
    }

    .minicart-items-wrapper {
        .lib-css(margin, 0 -15px);
        overflow-x: auto;
        padding: 15px;
    }

    .minicart-items {
        .lib-list-reset-styles();
        .product-item {
            &:not(:first-child) {
                .lib-css(border-top, 1px solid @minicart__border-color);
            }
            padding: @indent__base 0;
            &:first-child {
                padding-top: 0;
            }
            > .product {
                &:extend(.abs-add-clearfix all);
            }
        }
        .product-image-wrapper {
            &:extend(.abs-reset-image-wrapper all);
        }
        .product-item-pricing {
            .label {
                display: inline-block;
                width: 4.5rem;
            }
        }
        .price-minicart {
            margin-bottom: @indent__xs;
        }
        .product-item-name {
            font-size: 14px;
            font-weight: @font-weight__regular;
            margin: 0 0 @indent__s;
            a {
                .lib-css(color, @link__color);
            }
        }
        .product-item-details {
            padding-left: 95px;
            text-align: left;
            .price {
                font-weight: @font-weight__bold;
                color: #000;
            }
            .price-including-tax,
            .price-excluding-tax {
                margin: @indent__xs 0;
            }
            .weee[data-label] {
                .lib-font-size(11);
                .label {
                    &:extend(.abs-no-display all);
                }
            }
            .details-qty {
                margin-top: @indent__s;
            }
        }
        .product {
            > .product-item-photo,
            > .product-image-container {
                float: left;
                min-width: 0;
            }
            .toggle {
                &:after {
                    position: static;
                    margin: 0 0 0 @indent__xs;
                    .lib-css(color, @color-gray56);
                }
                border: 0;
                padding: 0 @indent__xl @indent__xs 0;
            }
            .product.options {
                > .toggle {
                    &:after {
                        font-family: @icon-font-family;
                    }
                    .lib-icon-font-symbol(
                        @_icon-font-content: @icon-down,
                        @_icon-font-position: after
                    );
                }
                &.active {
                    > .toggle {
                        .lib-icon-font-symbol(
                            @_icon-font-content: @icon-up,
                            @_icon-font-position: after
                        );
                    }
                }
            }
            &.pricing {
                margin-top: 3px;
            }
            &.options {
                .tooltip.toggle {
                    .lib-icon-font(
                        @icon-down,
                        @_icon-font-size: 12px,
                        @_icon-font-line-height: 12px,
                        @_icon-font-text-hide: true,
                        @_icon-font-margin: -3px 0 0 7px,
                        @_icon-font-position: after
                    );
                }
                .details {
                    display: none;
                }
            }
        }
        .details-qty,
        .price-minicart {
            .label {
                &:extend(.abs-colon all);
            }
        }
        .item-qty {
            width: 40px;
            text-align: center;
            margin-right: @indent__s;
            border-radius: 3px;
        }
        .update-cart-item {
            vertical-align: top;
            .lib-font-size(11);
        }
        .action {
            &.edit,
            &.delete {
                .lib-icon-font(
                    @icon-edit,
                    @_icon-font-size: 18px,
                    @_icon-font-line-height: 20px,
                    @_icon-font-text-hide: true,
                    @_icon-font-color: @minicart-icons-color,
                    @_icon-font-color-hover: @minicart-icons-color,
                    @_icon-font-color-active: @minicart-icons-color
                );
            }
            &.delete {
                .lib-icon-font-symbol(
                    @_icon-font-content: @icon-trash
                );
            }
        }
        .subtitle {
            display: none;
        }
    }

}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__xs) {
    .minicart-wrapper .block-minicart {
        width: 290px;
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .minicart-wrapper {
        margin-top: @indent__s;
    }
}


//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .minicart-wrapper {
        .block-minicart {
            right: -15px;
            width: 350px;
        }
    }
    .minilist .action.delete,
    .minicart-wrapper .action.edit {
        .lib-icon-font-size(
            @_icon-font-size: 16px
        );
    }
}
