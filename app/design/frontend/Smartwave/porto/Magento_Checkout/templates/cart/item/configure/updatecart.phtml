<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var $block \Magento\Catalog\Block\Product\View */
?>
<?php $_product = $block->getProduct(); ?>
<?php $buttonTitle = __('Update Cart'); ?>
<?php if ($_product->isSaleable()): ?>
    <div class="box-tocart update">
        <fieldset class="fieldset">
            <?php if ($block->shouldRenderQuantity()): ?>
            <div class="field qty">
                <label class="label" for="qty"><span><?php /* @escapeNotVerified */ echo __('Qty') ?></span></label>
                <div class="control">
                    <input type="number" name="qty" id="qty" maxlength="12" value="<?php /* @escapeNotVerified */ echo $block->getProductDefaultQty() * 1 ?>" title="<?php /* @escapeNotVerified */ echo __('Qty') ?>" class="input-text qty" data-validate="{'required-number':true,digits:true}"/>
                </div>
                <div class="qty-changer">
                    <a href="javascript:void(0)" class="qty-inc"><i class="porto-icon-up-dir"></i></a>
                    <a href="javascript:void(0)" class="qty-dec"><i class="porto-icon-down-dir"></i></a>
                </div>
            </div>
            <?php endif; ?>
            <div class="actions">
                <button type="submit"
                        title="<?php /* @escapeNotVerified */ echo $buttonTitle ?>"
                        class="action primary tocart"
                        id="product-updatecart-button">
                    <span><?php /* @escapeNotVerified */ echo $buttonTitle ?></span>
                </button>
                <?php echo $block->getChildHtml('', true) ?>
            </div>
        </fieldset>
    </div>
    <script type="text/x-magento-init">
        {
            "#product_addtocart_form": {
                "validation": {},
                "addToCart": {
                    "cartButtonId": "#product-updatecart-button",
                    "cartForm": "#product_addtocart_form"
                }
            }
        }
    </script>
<?php endif; ?>
