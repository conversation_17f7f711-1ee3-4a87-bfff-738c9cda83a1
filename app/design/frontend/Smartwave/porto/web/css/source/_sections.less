// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .product.data.items {
        .lib-data-accordion();
        border-bottom: @tab-control__border-width solid @tab-control__border-color;
        margin-bottom: @indent__base;
        margin-left: -@layout__width-xs-indent;
        margin-right: -@layout__width-xs-indent;
        .item.title {
            a {
                position: relative;
                .lib-icon-font(
                    @_icon-font-content: @icon-down,
                    @_icon-font-size: @font-size__base,
                    @_icon-font-line-height: @icon-font__line-height,
                    @_icon-font-color: @icon-font__color,
                    @_icon-font-color-hover: @icon-font__color-hover,
                    @_icon-font-color-active: @icon-font__color-active,
                    @_icon-font-margin: @icon-font__margin,
                    @_icon-font-vertical-align: @icon-font__vertical-align,
                    @_icon-font-position: after,
                    @_icon-font-display: false
                );
                &:after {
                    position: absolute;
                    right: @accordion-control__padding-right;
                    top: @accordion-control__padding-top;
                }
            }
            &.active a:after {
                content: @icon-up;
            }
        }
        .value p:last-child {
            margin-bottom: 0;
        }
        .table-wrapper {
            margin: 0;
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .product.data.items {
        .lib-data-tabs();
        border-bottom: 0;
        margin-left: 0;
        margin-right: 0;
        .item.title a:after {
            display: none;
        }
        
        > .item {
            &.title {
                margin: 0 1px 0 0;
                
                > .switch {
                    font-size: 14px;
                    border: 1px solid @tab-control__border-color;
                    border-top-width: 3px;
                    border-bottom-width: 0;
                    border-radius: 5px 5px 0 0;
                    line-height: 30px;
                    padding: 1px 15px 1px 15px;
                }
                &.active {
                    > .switch {
                        &, &:focus, &:hover {
                            border-top-color: @theme-color;
                            color: @theme-color;
                        }
                    }
                }
            }
            &.content {
                margin-top: 35px;
                border-color: @tab-control__border-color;
                box-shadow: 0 1px 2px #eee;
            }
        }
    }
}
