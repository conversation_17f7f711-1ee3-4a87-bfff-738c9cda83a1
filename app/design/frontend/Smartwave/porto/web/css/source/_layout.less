// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

@layout-column-main__sidebar-offset: 2%;
@layout-column__additional-sidebar-offset: @layout-column-main__sidebar-offset;

//
//    Common
//--------------------------------------

& when (@media-common = true) {

.columns {
    #lib-layout-columns();
    .column.main {
        &:extend(.abs-add-box-sizing all);
        .lib-vendor-prefix-flex-grow(1);
        .lib-vendor-prefix-flex-basis(100%);
        .lib-css(padding-bottom, @indent__xl);
        .lib-vendor-prefix-order(1);
        max-width: 100%;
    }
    .sidebar-main {
        &:extend(.abs-add-box-sizing all);
        .lib-vendor-prefix-flex-grow(1);
        .lib-vendor-prefix-flex-basis(100%);
        .lib-vendor-prefix-order(1);
        max-width: 100%;
    }
    .sidebar-additional {
        &:extend(.abs-add-box-sizing all);
        .lib-vendor-prefix-flex-grow(1);
        .lib-vendor-prefix-flex-basis(100%);
        .lib-vendor-prefix-order(2);
        max-width: 100%;
    }
}

.page-main {
    padding-top: 20px;
    padding-bottom: 50px;
    
    > .page-title-wrapper .page-title {
        font-size: 20px;
        margin: 0 0 15px;
        color: @theme-color;
    }
}

}

//
//    Mobile
//--------------------------------------
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .breadcrumbs,
    .page-header .header.panel,
    .header.content,
    .footer.content,
    .page-main,
    .page-wrapper > .widget,
    .page-wrapper > .page-bottom,
    .block.category.event,
    .top-container {
        padding-left: @layout__width-xs-indent;
        padding-right: @layout__width-xs-indent;
    }
    .page-main {
        .account &,
        .cms-privacy-policy & {
            position: relative;
            padding-top: 41px;
        }
    }
}

//
//    Desktop
//--------------------------------------
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .navigation,
    .breadcrumbs,
    .page-header .header.panel,
    .header.content,
    .footer.content,
    .page-wrapper > .widget,
    .page-wrapper > .page-bottom,
    .block.category.event,
    .top-container,
    .page-main {
        padding-left: 15px;
        padding-right: 15px;
        margin-left: auto;
        margin-right: auto;
        width: auto;
        max-width: @layout__max-width;
        box-sizing: border-box;
    }
    .page-main {
        width: 100%;
        .lib-vendor-prefix-flex-grow(1);
        .lib-vendor-prefix-flex-shrink(0);
        .lib-vendor-prefix-flex-basis(auto);
        .ie9 & {
            width: auto;
        }
    }

    .columns {
        display: block;
    }

    .column.main {
        #lib-layout-columns > .main();
        &:extend(.abs-add-box-sizing-desktop all);
        min-height: 300px;
    }

    .sidebar-main {
        #lib-layout-columns > .left();
        padding-right: @layout-column-main__sidebar-offset;
        &:extend(.abs-add-box-sizing-desktop all);
    }

    .page-layout-2columns-right .sidebar-main {
        padding-right: 0;
        padding-left: @layout-column-main__sidebar-offset;
    }

    .sidebar-additional {
        #lib-layout-columns > .right();
        padding-left: @layout-column__additional-sidebar-offset;
        clear: right;
        &:extend(.abs-add-box-sizing-desktop all);
    }

    .page-layout-2columns-left {
        .sidebar-additional {
            padding-right: @layout-column__additional-sidebar-offset;
            padding-left: 0;
            clear: left;
            float: left;
        }
    }

    .panel.header {
        padding: 10px 20px;
    }
}