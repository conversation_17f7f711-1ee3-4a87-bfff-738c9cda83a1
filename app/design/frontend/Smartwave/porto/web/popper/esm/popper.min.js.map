{"version": 3, "file": "popper.min.js", "sources": ["../../src/utils/debounce.js", "../../src/utils/isNative.js", "../../src/utils/isFunction.js", "../../src/utils/getStyleComputedProperty.js", "../../src/utils/getParentNode.js", "../../src/utils/getScrollParent.js", "../../src/utils/getOffsetParent.js", "../../src/utils/isOffsetContainer.js", "../../src/utils/getRoot.js", "../../src/utils/findCommonOffsetParent.js", "../../src/utils/getScroll.js", "../../src/utils/includeScroll.js", "../../src/utils/getBordersSize.js", "../../src/utils/isIE10.js", "../../src/utils/getWindowSizes.js", "../../src/utils/getClientRect.js", "../../src/utils/getBoundingClientRect.js", "../../src/utils/getOffsetRectRelativeToArbitraryNode.js", "../../src/utils/getViewportOffsetRectRelativeToArtbitraryNode.js", "../../src/utils/isFixed.js", "../../src/utils/getBoundaries.js", "../../src/utils/computeAutoPlacement.js", "../../src/utils/getReferenceOffsets.js", "../../src/utils/getOuterSizes.js", "../../src/utils/getOppositePlacement.js", "../../src/utils/getPopperOffsets.js", "../../src/utils/find.js", "../../src/utils/findIndex.js", "../../src/utils/runModifiers.js", "../../src/methods/update.js", "../../src/utils/isModifierEnabled.js", "../../src/utils/getSupportedPropertyName.js", "../../src/methods/destroy.js", "../../src/utils/setupEventListeners.js", "../../src/methods/enableEventListeners.js", "../../src/utils/removeEventListeners.js", "../../src/methods/disableEventListeners.js", "../../src/utils/isNumeric.js", "../../src/utils/setStyles.js", "../../src/utils/setAttributes.js", "../../src/modifiers/applyStyle.js", "../../src/modifiers/computeStyle.js", "../../src/utils/isModifierRequired.js", "../../src/modifiers/arrow.js", "../../src/utils/getOppositeVariation.js", "../../src/methods/placements.js", "../../src/utils/clockwise.js", "../../src/modifiers/flip.js", "../../src/modifiers/keepTogether.js", "../../src/modifiers/offset.js", "../../src/modifiers/preventOverflow.js", "../../src/modifiers/shift.js", "../../src/modifiers/hide.js", "../../src/modifiers/inner.js", "../../src/modifiers/index.js", "../../src/methods/defaults.js", "../../src/index.js"], "sourcesContent": ["import isNative from './isNative';\n\nconst isBrowser = typeof window !== 'undefined';\nconst longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\nlet timeoutDuration = 0;\nfor (let i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n  if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n    timeoutDuration = 1;\n    break;\n  }\n}\n\nexport function microtaskDebounce(fn) {\n  let scheduled = false;\n  let i = 0;\n  const elem = document.createElement('span');\n\n  // MutationObserver provides a mechanism for scheduling microtasks, which\n  // are scheduled *before* the next task. This gives us a way to debounce\n  // a function but ensure it's called *before* the next paint.\n  const observer = new MutationObserver(() => {\n    fn();\n    scheduled = false;\n  });\n\n  observer.observe(elem, { attributes: true });\n\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      elem.setAttribute('x-index', i);\n      i = i + 1; // don't use compund (+=) because it doesn't get optimized in V8\n    }\n  };\n}\n\nexport function taskDebounce(fn) {\n  let scheduled = false;\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(() => {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\n// It's common for MutationObserver polyfills to be seen in the wild, however\n// these rely on Mutation Events which only occur when an element is connected\n// to the DOM. The algorithm used in this module does not use a connected element,\n// and so we must ensure that a *native* MutationObserver is available.\nconst supportsNativeMutationObserver =\n  isBrowser && isNative(window.MutationObserver);\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nexport default (supportsNativeMutationObserver\n  ? microtaskDebounce\n  : taskDebounce);\n", "const nativeHints = [\n  'native code',\n  '[object MutationObserverConstructor]', // for mobile safari iOS 9.0\n];\n\n/**\n * Determine if a function is implemented natively (as opposed to a polyfill).\n * @method\n * @memberof Popper.Utils\n * @argument {Function | undefined} fn the function to check\n * @returns {Boolean}\n */\nexport default fn =>\n  nativeHints.some(hint => (fn || '').toString().indexOf(hint) > -1);\n", "/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nexport default function isFunction(functionToCheck) {\n  const getType = {};\n  return (\n    functionToCheck &&\n    getType.toString.call(functionToCheck) === '[object Function]'\n  );\n}\n", "/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nexport default function getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n", "/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nexport default function getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nexport default function getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (\n    !element ||\n    ['HTML', 'BODY', '#document'].indexOf(element.nodeName) !== -1\n  ) {\n    return window.document.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getStyleComputedProperty(element);\n  if (/(auto|scroll)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nexport default function getOffsetParent(element) {\n  // NOTE: 1 DOM access here\n  const offsetParent = element && element.offsetParent;\n  const nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return window.document.documentElement;\n  }\n\n  // .offsetParent will return the closest TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (\n    ['TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 &&\n    getStyleComputedProperty(offsetParent, 'position') === 'static'\n  ) {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n", "import getOffsetParent from './getOffsetParent';\n\nexport default function isOffsetContainer(element) {\n  const { nodeName } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return (\n    nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element\n  );\n}\n", "/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nexport default function getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n", "import isOffsetContainer from './isOffsetContainer';\nimport getRoot from './getRoot';\nimport getOffsetParent from './getOffsetParent';\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nexport default function findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return window.document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order =\n    element1.compareDocumentPosition(element2) &\n    Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  const { commonAncestorContainer } = range;\n\n  // Both nodes are inside #document\n  if (\n    (element1 !== commonAncestorContainer &&\n      element2 !== commonAncestorContainer) ||\n    start.contains(end)\n  ) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n", "/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nexport default function getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = window.document.documentElement;\n    const scrollingElement = window.document.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n", "import getScroll from './getScroll';\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nexport default function includeScroll(rect, element, subtract = false) {\n  const scrollTop = getScroll(element, 'top');\n  const scrollLeft = getScroll(element, 'left');\n  const modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n", "/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nexport default function getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return (\n    +styles[`border${sideA}Width`].split('px')[0] +\n    +styles[`border${sideB}Width`].split('px')[0]\n  );\n}\n", "/**\n * Tells if you are running Internet Explorer 10\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean} isIE10\n */\nlet isIE10 = undefined;\n\nexport default function() {\n  if (isIE10 === undefined) {\n    isIE10 = navigator.appVersion.indexOf('MSIE 10') !== -1;\n  }\n  return isIE10;\n}\n", "import isIE10 from './isIE10';\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(\n    body[`offset${axis}`],\n    body[`scroll${axis}`],\n    html[`client${axis}`],\n    html[`offset${axis}`],\n    html[`scroll${axis}`],\n    isIE10()\n      ? html[`offset${axis}`] +\n        computedStyle[`margin${axis === 'Height' ? 'Top' : 'Left'}`] +\n        computedStyle[`margin${axis === 'Height' ? 'Bottom' : 'Right'}`]\n      : 0\n  );\n}\n\nexport default function getWindowSizes() {\n  const body = window.document.body;\n  const html = window.document.documentElement;\n  const computedStyle = isIE10() && window.getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle),\n  };\n}\n", "/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nexport default function getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height,\n  };\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getBordersSize from './getBordersSize';\nimport getWindowSizes from './getWindowSizes';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\nimport isIE10 from './isIE10';\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nexport default function getBoundingClientRect(element) {\n  let rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  if (isIE10()) {\n    try {\n      rect = element.getBoundingClientRect();\n      const scrollTop = getScroll(element, 'top');\n      const scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } catch (err) {}\n  } else {\n    rect = element.getBoundingClientRect();\n  }\n\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes() : {};\n  const width =\n    sizes.width || element.clientWidth || result.right - result.left;\n  const height =\n    sizes.height || element.clientHeight || result.bottom - result.top;\n\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport includeScroll from './includeScroll';\nimport getScrollParent from './getScrollParent';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport runIsIE10 from './isIE10';\nimport getClientRect from './getClientRect';\n\nexport default function getOffsetRectRelativeToArbitraryNode(children, parent) {\n  const isIE10 = runIsIE10();\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const scrollParent = getScrollParent(children);\n\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = +styles.borderTopWidth.split('px')[0];\n  const borderLeftWidth = +styles.borderLeftWidth.split('px')[0];\n\n  let offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height,\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    const marginTop = +styles.marginTop.split('px')[0];\n    const marginLeft = +styles.marginLeft.split('px')[0];\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (\n    isIE10\n      ? parent.contains(scrollParent)\n      : parent === scrollParent && scrollParent.nodeName !== 'BODY'\n  ) {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n", "import getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\n\nexport default function getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  const html = window.document.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  const scrollTop = getScroll(html);\n  const scrollLeft = getScroll(html, 'left');\n\n  const offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width,\n    height,\n  };\n\n  return getClientRect(offset);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {<PERSON><PERSON><PERSON>} answer to \"isFixed?\"\n */\nexport default function isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  return isFixed(getParentNode(element));\n}\n", "import getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getViewportOffsetRectRelativeToArtbitraryNode from './getViewportOffsetRectRelativeToArtbitraryNode';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @returns {Object} Coordinates of the boundaries\n */\nexport default function getBoundaries(\n  popper,\n  reference,\n  padding,\n  boundariesElement\n) {\n  // NOTE: 1 DOM access here\n  let boundaries = { top: 0, left: 0 };\n  const offsetParent = findCommonOffsetParent(popper, reference);\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(popper));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = window.document.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = window.document.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    const offsets = getOffsetRectRelativeToArbitraryNode(\n      boundariesNode,\n      offsetParent\n    );\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const { height, width } = getWindowSizes();\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  boundaries.left += padding;\n  boundaries.top += padding;\n  boundaries.right -= padding;\n  boundaries.bottom -= padding;\n\n  return boundaries;\n}\n", "import getBoundaries from '../utils/getBoundaries';\n\nfunction getArea({ width, height }) {\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeAutoPlacement(\n  placement,\n  refRect,\n  popper,\n  reference,\n  boundariesElement,\n  padding = 0\n) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  const boundaries = getBoundaries(\n    popper,\n    reference,\n    padding,\n    boundariesElement\n  );\n\n  const rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top,\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height,\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom,\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height,\n    },\n  };\n\n  const sortedAreas = Object.keys(rects)\n    .map(key => ({\n      key,\n      ...rects[key],\n      area: getArea(rects[key]),\n    }))\n    .sort((a, b) => b.area - a.area);\n\n  const filteredAreas = sortedAreas.filter(\n    ({ width, height }) =>\n      width >= popper.clientWidth && height >= popper.clientHeight\n  );\n\n  const computedPlacement = filteredAreas.length > 0\n    ? filteredAreas[0].key\n    : sortedAreas[0].key;\n\n  const variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\n", "import findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nexport default function getReferenceOffsets(state, popper, reference) {\n  const commonOffsetParent = findCommonOffsetParent(popper, reference);\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent);\n}\n", "/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nexport default function getOuterSizes(element) {\n  const styles = window.getComputedStyle(element);\n  const x = parseFloat(styles.marginTop) + parseFloat(styles.marginBottom);\n  const y = parseFloat(styles.marginLeft) + parseFloat(styles.marginRight);\n  const result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x,\n  };\n  return result;\n}\n", "/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nexport default function getOppositePlacement(placement) {\n  const hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n", "import getOuterSizes from './getOuterSizes';\nimport getOppositePlacement from './getOppositePlacement';\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nexport default function getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  const popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  const popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height,\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] =\n    referenceOffsets[mainSide] +\n    referenceOffsets[measurement] / 2 -\n    popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] =\n      referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] =\n      referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n", "/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n", "import find from './find';\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(cur => cur[prop] === value);\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  const match = find(arr, obj => obj[prop] === value);\n  return arr.indexOf(match);\n}\n", "import isFunction from './isFunction';\nimport findIndex from './findIndex';\nimport getClientRect from '../utils/getClientRect';\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nexport default function runModifiers(modifiers, data, ends) {\n  const modifiersToRun = ends === undefined\n    ? modifiers\n    : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(modifier => {\n    if (modifier.function) {\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    const fn = modifier.function || modifier.fn;\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n", "import computeAutoPlacement from '../utils/computeAutoPlacement';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nexport default function update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  let data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {},\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(\n    this.state,\n    this.popper,\n    this.reference\n  );\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(\n    this.options.placement,\n    data.offsets.reference,\n    this.popper,\n    this.reference,\n    this.options.modifiers.flip.boundariesElement,\n    this.options.modifiers.flip.padding\n  );\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(\n    this.popper,\n    data.offsets.reference,\n    data.placement\n  );\n  data.offsets.popper.position = 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n", "/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nexport default function isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(\n    ({ name, enabled }) => enabled && name === modifierName\n  );\n}\n", "/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nexport default function getSupportedPropertyName(property) {\n  const prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  const upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (let i = 0; i < prefixes.length - 1; i++) {\n    const prefix = prefixes[i];\n    const toCheck = prefix ? `${prefix}${upperProp}` : property;\n    if (typeof window.document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n", "import isModifierEnabled from '../utils/isModifierEnabled';\nimport getSupportedPropertyName from '../utils/getSupportedPropertyName';\n\n/**\n * Destroy the popper\n * @method\n * @memberof Popper\n */\nexport default function destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.left = '';\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicity asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n", "import getScrollParent from './getScrollParent';\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  const isBody = scrollParent.nodeName === 'BODY';\n  const target = isBody ? window : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(\n      getScrollParent(target.parentNode),\n      event,\n      callback,\n      scrollParents\n    );\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function setupEventListeners(\n  reference,\n  options,\n  state,\n  updateBound\n) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  window.addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  const scrollElement = getScrollParent(reference);\n  attachToScrollParents(\n    scrollElement,\n    'scroll',\n    state.updateBound,\n    state.scrollParents\n  );\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n", "import setupEventListeners from '../utils/setupEventListeners';\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nexport default function enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(\n      this.reference,\n      this.options,\n      this.state,\n      this.scheduleUpdate\n    );\n  }\n}\n", "/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  window.removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(target => {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n", "import removeEventListeners from '../utils/removeEventListeners';\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger onUpdate callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nexport default function disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    window.cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n", "/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nexport default function isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n", "import isNumeric from './isNumeric';\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setStyles(element, styles) {\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (\n      ['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !==\n        -1 &&\n      isNumeric(styles[prop])\n    ) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n", "/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function(prop) {\n    const value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n", "import setStyles from '../utils/setStyles';\nimport setAttributes from '../utils/setAttributes';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport computeAutoPlacement from '../utils/computeAutoPlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nexport default function applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper.\n * @param {Object} options - Popper.js options\n */\nexport function applyStyleOnLoad(\n  reference,\n  popper,\n  options,\n  modifierOptions,\n  state\n) {\n  // compute reference element offsets\n  const referenceOffsets = getReferenceOffsets(state, popper, reference);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  const placement = computeAutoPlacement(\n    options.placement,\n    referenceOffsets,\n    popper,\n    reference,\n    options.modifiers.flip.boundariesElement,\n    options.modifiers.flip.padding\n  );\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: 'absolute' });\n\n  return options;\n}\n", "import getSupportedPropertyName from '../utils/getSupportedPropertyName';\nimport find from '../utils/find';\nimport getOffsetParent from '../utils/getOffsetParent';\nimport getBoundingClientRect from '../utils/getBoundingClientRect';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeStyle(data, options) {\n  const { x, y } = options;\n  const { popper } = data.offsets;\n\n  // Remove this legacy support in Popper.js v2\n  const legacyGpuAccelerationOption = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'applyStyle'\n  ).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn(\n      'WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!'\n    );\n  }\n  const gpuAcceleration =\n    legacyGpuAccelerationOption !== undefined\n      ? legacyGpuAccelerationOption\n      : options.gpuAcceleration;\n\n  const offsetParent = getOffsetParent(data.instance.popper);\n  const offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  const styles = {\n    position: popper.position,\n  };\n\n  // floor sides to avoid blurry text\n  const offsets = {\n    left: Math.floor(popper.left),\n    top: Math.floor(popper.top),\n    bottom: Math.floor(popper.bottom),\n    right: Math.floor(popper.right),\n  };\n\n  const sideA = x === 'bottom' ? 'top' : 'bottom';\n  const sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  const prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  let left, top;\n  if (sideA === 'bottom') {\n    top = -offsetParentRect.height + offsets.bottom;\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    left = -offsetParentRect.width + offsets.right;\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = `translate3d(${left}px, ${top}px, 0)`;\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    const invertTop = sideA === 'bottom' ? -1 : 1;\n    const invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = `${sideA}, ${sideB}`;\n  }\n\n  // Attributes\n  const attributes = {\n    'x-placement': data.placement,\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = { ...attributes, ...data.attributes };\n  data.styles = { ...styles, ...data.styles };\n  data.arrowStyles = { ...data.offsets.arrow, ...data.arrowStyles };\n\n  return data;\n}\n", "import find from './find';\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nexport default function isModifierRequired(\n  modifiers,\n  requestingName,\n  requestedName\n) {\n  const requesting = find(modifiers, ({ name }) => name === requestingName);\n\n  const isRequired =\n    !!requesting &&\n    modifiers.some(modifier => {\n      return (\n        modifier.name === requestedName &&\n        modifier.enabled &&\n        modifier.order < requesting.order\n      );\n    });\n\n  if (!isRequired) {\n    const requesting = `\\`${requestingName}\\``;\n    const requested = `\\`${requestedName}\\``;\n    console.warn(\n      `${requested} modifier is required by ${requesting} modifier in order to work, be sure to include it before ${requesting}!`\n    );\n  }\n  return isRequired;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOuterSizes from '../utils/getOuterSizes';\nimport isModifierRequired from '../utils/isModifierRequired';\nimport getStyleComputedProperty from '../utils/getStyleComputedProperty';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function arrow(data, options) {\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  let arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn(\n        'WARNING: `arrow.element` must be child of its popper element!'\n      );\n      return data;\n    }\n  }\n\n  const placement = data.placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  const len = isVertical ? 'height' : 'width';\n  const sideCapitalized = isVertical ? 'Top' : 'Left';\n  const side = sideCapitalized.toLowerCase();\n  const altSide = isVertical ? 'left' : 'top';\n  const opSide = isVertical ? 'bottom' : 'right';\n  const arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjuction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -=\n      popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] +=\n      reference[side] + arrowElementSize - popper[opSide];\n  }\n\n  // compute center of the popper\n  const center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  const popperMarginSide = getStyleComputedProperty(\n    data.instance.popper,\n    `margin${sideCapitalized}`\n  ).replace('px', '');\n  let sideValue =\n    center - getClientRect(data.offsets.popper)[side] - popperMarginSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = {};\n  data.offsets.arrow[side] = Math.round(sideValue);\n  data.offsets.arrow[altSide] = ''; // make sure to unset any eventual altSide value from the DOM node\n\n  return data;\n}\n", "/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nexport default function getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n", "/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-right` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nexport default [\n  'auto-start',\n  'auto',\n  'auto-end',\n  'top-start',\n  'top',\n  'top-end',\n  'right-start',\n  'right',\n  'right-end',\n  'bottom-end',\n  'bottom',\n  'bottom-start',\n  'left-end',\n  'left',\n  'left-start',\n];\n", "import placements from '../methods/placements';\n\n// Get rid of `auto` `auto-start` and `auto-end`\nconst validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nexport default function clockwise(placement, counter = false) {\n  const index = validPlacements.indexOf(placement);\n  const arr = validPlacements\n    .slice(index + 1)\n    .concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n", "import getOppositePlacement from '../utils/getOppositePlacement';\nimport getOppositeVariation from '../utils/getOppositeVariation';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\nimport getBoundaries from '../utils/getBoundaries';\nimport isModifierEnabled from '../utils/isModifierEnabled';\nimport clockwise from '../utils/clockwise';\n\nconst BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise',\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    options.boundariesElement\n  );\n\n  let placement = data.placement.split('-')[0];\n  let placementOpposite = getOppositePlacement(placement);\n  let variation = data.placement.split('-')[1] || '';\n\n  let flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach((step, index) => {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    const popperOffsets = data.offsets.popper;\n    const refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    const floor = Math.floor;\n    const overlapsRef =\n      (placement === 'left' &&\n        floor(popperOffsets.right) > floor(refOffsets.left)) ||\n      (placement === 'right' &&\n        floor(popperOffsets.left) < floor(refOffsets.right)) ||\n      (placement === 'top' &&\n        floor(popperOffsets.bottom) > floor(refOffsets.top)) ||\n      (placement === 'bottom' &&\n        floor(popperOffsets.top) < floor(refOffsets.bottom));\n\n    const overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    const overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    const overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    const overflowsBottom =\n      floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    const overflowsBoundaries =\n      (placement === 'left' && overflowsLeft) ||\n      (placement === 'right' && overflowsRight) ||\n      (placement === 'top' && overflowsTop) ||\n      (placement === 'bottom' && overflowsBottom);\n\n    // flip the variation if required\n    const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n    const flippedVariation =\n      !!options.flipVariations &&\n      ((isVertical && variation === 'start' && overflowsLeft) ||\n        (isVertical && variation === 'end' && overflowsRight) ||\n        (!isVertical && variation === 'start' && overflowsTop) ||\n        (!isVertical && variation === 'end' && overflowsBottom));\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = {\n        ...data.offsets.popper,\n        ...getPopperOffsets(\n          data.instance.popper,\n          data.offsets.reference,\n          data.placement\n        ),\n      };\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function keepTogether(data) {\n  const { popper, reference } = data.offsets;\n  const placement = data.placement.split('-')[0];\n  const floor = Math.floor;\n  const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  const side = isVertical ? 'right' : 'bottom';\n  const opSide = isVertical ? 'left' : 'top';\n  const measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] =\n      floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n", "import isNumeric from '../utils/isNumeric';\nimport getClientRect from '../utils/getClientRect';\nimport find from '../utils/find';\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nexport function toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  const split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  const value = +split[1];\n  const unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    let element;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    const rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    let size;\n    if (unit === 'vh') {\n      size = Math.max(\n        document.documentElement.clientHeight,\n        window.innerHeight || 0\n      );\n    } else {\n      size = Math.max(\n        document.documentElement.clientWidth,\n        window.innerWidth || 0\n      );\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nexport function parseOffset(\n  offset,\n  popperOffsets,\n  referenceOffsets,\n  basePlacement\n) {\n  const offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  const useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  const fragments = offset.split(/(\\+|\\-)/).map(frag => frag.trim());\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  const divider = fragments.indexOf(\n    find(fragments, frag => frag.search(/,|\\s/) !== -1)\n  );\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn(\n      'Offsets separated by white space(s) are deprecated, use a comma (,) instead.'\n    );\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  const splitRegex = /\\s*,\\s*|\\s+/;\n  let ops = divider !== -1\n    ? [\n        fragments\n          .slice(0, divider)\n          .concat([fragments[divider].split(splitRegex)[0]]),\n        [fragments[divider].split(splitRegex)[1]].concat(\n          fragments.slice(divider + 1)\n        ),\n      ]\n    : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map((op, index) => {\n    // Most of the units rely on the orientation of the popper\n    const measurement = (index === 1 ? !useHeight : useHeight)\n      ? 'height'\n      : 'width';\n    let mergeWithPrevious = false;\n    return (\n      op\n        // This aggregates any `+` or `-` sign that aren't considered operators\n        // e.g.: 10 + +5 => [10, +, +5]\n        .reduce((a, b) => {\n          if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n            a[a.length - 1] = b;\n            mergeWithPrevious = true;\n            return a;\n          } else if (mergeWithPrevious) {\n            a[a.length - 1] += b;\n            mergeWithPrevious = false;\n            return a;\n          } else {\n            return a.concat(b);\n          }\n        }, [])\n        // Here we convert the string values into number values (in px)\n        .map(str => toValue(str, measurement, popperOffsets, referenceOffsets))\n    );\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach((op, index) => {\n    op.forEach((frag, index2) => {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nexport default function offset(data, { offset }) {\n  const { placement, offsets: { popper, reference } } = data;\n  const basePlacement = placement.split('-')[0];\n\n  let offsets;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n", "import getOffsetParent from '../utils/getOffsetParent';\nimport getBoundaries from '../utils/getBoundaries';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function preventOverflow(data, options) {\n  let boundariesElement =\n    options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    boundariesElement\n  );\n  options.boundaries = boundaries;\n\n  const order = options.priority;\n  let popper = data.offsets.popper;\n\n  const check = {\n    primary(placement) {\n      let value = popper[placement];\n      if (\n        popper[placement] < boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return { [placement]: value };\n    },\n    secondary(placement) {\n      const mainSide = placement === 'right' ? 'left' : 'top';\n      let value = popper[mainSide];\n      if (\n        popper[placement] > boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.min(\n          popper[mainSide],\n          boundaries[placement] -\n            (placement === 'right' ? popper.width : popper.height)\n        );\n      }\n      return { [mainSide]: value };\n    },\n  };\n\n  order.forEach(placement => {\n    const side = ['left', 'top'].indexOf(placement) !== -1\n      ? 'primary'\n      : 'secondary';\n    popper = { ...popper, ...check[side](placement) };\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function shift(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    const { reference, popper } = data.offsets;\n    const isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    const side = isVertical ? 'left' : 'top';\n    const measurement = isVertical ? 'width' : 'height';\n\n    const shiftOffsets = {\n      start: { [side]: reference[side] },\n      end: {\n        [side]: reference[side] + reference[measurement] - popper[measurement],\n      },\n    };\n\n    data.offsets.popper = { ...popper, ...shiftOffsets[shiftvariation] };\n  }\n\n  return data;\n}\n", "import isModifierRequired from '../utils/isModifierRequired';\nimport find from '../utils/find';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  const refRect = data.offsets.reference;\n  const bound = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'preventOverflow'\n  ).boundaries;\n\n  if (\n    refRect.bottom < bound.top ||\n    refRect.left > bound.right ||\n    refRect.top > bound.bottom ||\n    refRect.right < bound.left\n  ) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOppositePlacement from '../utils/getOppositePlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function inner(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  const subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] =\n    reference[basePlacement] -\n    (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n", "import applyStyle, { applyStyleOnLoad } from './applyStyle';\nimport computeStyle from './computeStyle';\nimport arrow from './arrow';\nimport flip from './flip';\nimport keepTogether from './keepTogether';\nimport offset from './offset';\nimport preventOverflow from './preventOverflow';\nimport shift from './shift';\nimport hide from './hide';\nimport inner from './inner';\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nexport default {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift,\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unitless, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the height.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > More on this [reading this issue](https://github.com/FezVrasta/popper.js/issues/373)\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0,\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * An scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper this makes sure the popper has always a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier, can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent',\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near eachothers\n   * without leaving any gap between the two. Expecially useful when the arrow is\n   * enabled and you want to assure it to point to its reference element.\n   * It cares only about the first axis, you can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether,\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjuction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]',\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations).\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position,\n     * the popper will never be placed outside of the defined boundaries\n     * (except if keepTogether is enabled)\n     */\n    boundariesElement: 'viewport',\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner,\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide,\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right',\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define you own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: undefined,\n  },\n};\n\n/**\n * The `dataObject` is an object containing all the informations used by Popper.js\n * this object get passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper.\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements.\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n", "import modifiers from '../modifiers/index';\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overriden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass as 3rd argument an object with the same\n * structure of this object, example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nexport default {\n  /**\n   * Popper's placement\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Whether events (resize, scroll) are initially enabled\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: () => {},\n\n  /**\n   * Callback called when the popper is updated, this callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: () => {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js\n   * @prop {modifiers}\n   */\n  modifiers,\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n", "// Utils\nimport debounce from './utils/debounce';\nimport isFunction from './utils/isFunction';\n\n// Methods\nimport update from './methods/update';\nimport destroy from './methods/destroy';\nimport enableEventListeners from './methods/enableEventListeners';\nimport disableEventListeners from './methods/disableEventListeners';\nimport Defaults from './methods/defaults';\nimport placements from './methods/placements';\n\nexport default class Popper {\n  /**\n   * Create a new Popper.js instance\n   * @class Popper\n   * @param {HTMLElement|referenceObject} reference - The reference element used to position the popper\n   * @param {HTMLElement} popper - The HTML element used as popper.\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  constructor(reference, popper, options = {}) {\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = { ...Popper.Defaults, ...options };\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: [],\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference.jquery ? reference[0] : reference;\n    this.popper = popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys({\n      ...Popper.Defaults.modifiers,\n      ...options.modifiers,\n    }).forEach(name => {\n      this.options.modifiers[name] = {\n        // If it's a built-in modifier, use it as base\n        ...(Popper.Defaults.modifiers[name] || {}),\n        // If there are custom options, override and merge with default ones\n        ...(options.modifiers ? options.modifiers[name] : {}),\n      };\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers)\n      .map(name => ({\n        name,\n        ...this.options.modifiers[name],\n      }))\n      // sort the modifiers by order\n      .sort((a, b) => a.order - b.order);\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(modifierOptions => {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(\n          this.reference,\n          this.popper,\n          this.options,\n          modifierOptions,\n          this.state\n        );\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    const eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n  update() {\n    return update.call(this);\n  }\n  destroy() {\n    return destroy.call(this);\n  }\n  enableEventListeners() {\n    return enableEventListeners.call(this);\n  }\n  disableEventListeners() {\n    return disableEventListeners.call(this);\n  }\n\n  /**\n   * Schedule an update, it will run on the next UI update available\n   * @method scheduleUpdate\n   * @memberof Popper\n   */\n  scheduleUpdate = () => requestAnimationFrame(this.update);\n\n  /**\n   * Collection of utilities useful when writing custom modifiers.\n   * Starting from version 1.7, this method is available only if you\n   * include `popper-utils.js` before `popper.js`.\n   *\n   * **DEPRECATION**: This way to access PopperUtils is deprecated\n   * and will be removed in v2! Use the PopperUtils module directly instead.\n   * Due to the high instability of the methods contained in Utils, we can't\n   * guarantee them to follow semver. Use them at your own risk!\n   * @static\n   * @private\n   * @type {Object}\n   * @deprecated since version 1.8\n   * @member Utils\n   * @memberof Popper\n   */\n  static Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\n\n  static placements = placements;\n\n  static Defaults = Defaults;\n}\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n"], "names": ["nativeHints", "some", "fn", "toString", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "window", "longerTimeoutBrowsers", "timeoutDuration", "i", "length", "navigator", "userAgent", "microtaskDebounce", "scheduled", "elem", "document", "createElement", "observer", "MutationObserver", "observe", "attributes", "setAttribute", "taskDebounce", "supportsNativeMutationObserver", "isNative", "isFunction", "functionToCheck", "getType", "call", "getStyleComputedProperty", "element", "nodeType", "css", "getComputedStyle", "property", "getParentNode", "nodeName", "parentNode", "host", "getScrollParent", "body", "overflow", "overflowX", "overflowY", "test", "getOffsetParent", "offsetParent", "documentElement", "isOffsetContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "contains", "element1root", "getScroll", "side", "upperSide", "html", "scrollingElement", "includeScroll", "subtract", "scrollTop", "scrollLeft", "modifier", "top", "bottom", "left", "right", "getBordersSize", "sideA", "axis", "sideB", "styles", "split", "isIE10", "appVersion", "getSize", "Math", "max", "computedStyle", "getWindowSizes", "getClientRect", "offsets", "width", "height", "getBoundingClientRect", "rect", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "offsetHeight", "getOffsetRectRelativeToArbitraryNode", "runIsIE10", "isHTML", "parent", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "getViewportOffsetRectRelativeToArtbitraryNode", "relativeOffset", "innerWidth", "innerHeight", "offset", "isFixed", "getBoundaries", "boundaries", "boundariesElement", "boundariesNode", "getArea", "computeAutoPlacement", "padding", "placement", "rects", "refRect", "sorted<PERSON>reas", "Object", "keys", "map", "sort", "b", "area", "a", "filtered<PERSON><PERSON>s", "filter", "popper", "computedPlacement", "key", "variation", "getReferenceOffsets", "commonOffsetParent", "getOuterSizes", "x", "parseFloat", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "replace", "getPopperOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "referenceOffsets", "find", "Array", "prototype", "arr", "findIndex", "cur", "match", "obj", "runModifiers", "modifiersToRun", "ends", "modifiers", "slice", "for<PERSON>ach", "function", "warn", "enabled", "data", "reference", "update", "state", "isDestroyed", "options", "flip", "originalPlacement", "position", "isCreated", "onUpdate", "onCreate", "isModifierEnabled", "name", "getSupportedPropertyName", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "prefix", "to<PERSON><PERSON><PERSON>", "style", "destroy", "removeAttribute", "disableEventListeners", "removeOnDestroy", "<PERSON><PERSON><PERSON><PERSON>", "attachToScrollParents", "isBody", "target", "addEventListener", "passive", "push", "setupEventListeners", "updateBound", "scrollElement", "scrollParents", "eventsEnabled", "enableEventListeners", "scheduleUpdate", "removeEventListeners", "removeEventListener", "cancelAnimationFrame", "isNumeric", "n", "isNaN", "isFinite", "setStyles", "unit", "setAttributes", "value", "applyStyle", "instance", "arrowElement", "arrowStyles", "applyStyleOnLoad", "computeStyle", "floor", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "prefixedProperty", "<PERSON><PERSON><PERSON><PERSON>", "invertTop", "invertLeft", "arrow", "isModifierRequired", "requesting", "isRequired", "requested", "querySelector", "isVertical", "len", "sideCapitalized", "toLowerCase", "altSide", "opSide", "arrowElementSize", "center", "popperMarginSide", "sideValue", "min", "round", "getOppositeVariation", "validPlacements", "placements", "clockwise", "counter", "index", "concat", "reverse", "BEHAVIORS", "flipped", "placementOpposite", "flipOrder", "behavior", "FLIP", "CLOCKWISE", "COUNTERCLOCKWISE", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariation", "flipVariations", "keepTogether", "toValue", "str", "size", "parseOffset", "useHeight", "fragments", "frag", "trim", "divider", "search", "splitRegex", "ops", "mergeWithPrevious", "op", "reduce", "index2", "basePlacement", "preventOverflow", "priority", "check", "escapeWithReference", "shift", "shiftvariation", "shiftOffsets", "hide", "bound", "inner", "subtractLength", "<PERSON><PERSON>", "requestAnimationFrame", "debounce", "bind", "De<PERSON>ults", "j<PERSON>y", "modifierOptions", "onLoad", "Utils", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;GAKA,IAAK,GCLCA,mEDKD,UCOU,kBACbA,aAAYC,IAAZD,CAAiB,kBAA8C,CAAC,CAAvC,EAACE,GAAM,EAAP,EAAWC,QAAX,GAAsBC,OAAtB,GAAzB,CAAAJ,CADF,CDPK,CAHCK,UAA8B,WAAlB,QAAOC,OAGpB,CAFCC,kDAED,CADDC,gBAAkB,CACjB,CAAIC,EAAI,CAAb,CAAgBA,EAAIF,sBAAsBG,MAA1C,CAAkDD,GAAK,CAAvD,IACMJ,WAAsE,CAAzDM,YAAUC,SAAVD,CAAoBP,OAApBO,CAA4BJ,sBAAsBE,CAAtBF,CAA5BI,EAA4D,iBACzD,CADyD,OAM/E,QAAgBE,kBAAhB,GAAsC,IAChCC,MACAL,EAAI,EACFM,EAAOC,SAASC,aAATD,CAAuB,MAAvBA,EAKPE,EAAW,GAAIC,iBAAJ,CAAqB,UAAM,IAAA,KAA3B,CAAA,WAKRC,UAAc,CAAEC,aAAF,GAEhB,UAAM,SAAA,GAGJC,aAAa,YAHT,KAAb,EASF,QAAgBC,aAAhB,GAAiC,IAC3BT,YACG,WAAM,SAAA,YAGE,UAAM,KAAA,IAAjB,EAGGN,gBANM,CAAb,EAeF,GAAMgB,gCACJnB,WAAaoB,SAASnB,OAAOa,gBAAhBM,CADf,UAYgBD,+BACZX,iBADYW,CAEZD,YAdJ,CE9CA,QAAwBG,WAAxB,GAAoD,OAGhDC,IAC2C,mBAA3CC,MAAQzB,QAARyB,CAAiBC,IAAjBD,ICJJ,QAAwBE,yBAAxB,KAAoE,IACzC,CAArBC,KAAQC,qBAINC,GAAM3B,OAAO4B,gBAAP5B,GAAiC,IAAjCA,QACL6B,GAAWF,IAAXE,GCNT,QAAwBC,cAAxB,GAA+C,OACpB,MAArBL,KAAQM,QADiC,GAItCN,EAAQO,UAARP,EAAsBA,EAAQQ,KCDvC,QAAwBC,gBAAxB,GAAiD,IAG7C,IAC4D,CAAC,CAA7D,+BAA8BpC,OAA9B,CAAsC2B,EAAQM,QAA9C,QAEO/B,QAAOU,QAAPV,CAAgBmC,WAIkBX,4BAAnCY,IAAAA,SAAUC,IAAAA,UAAWC,IAAAA,UAVkB,MAW3C,iBAAgBC,IAAhB,CAAqBH,KAArB,CAX2C,GAexCF,gBAAgBJ,gBAAhBI,ECjBT,QAAwBM,gBAAxB,GAAiD,IAEzCC,GAAehB,GAAWA,EAAQgB,aAClCV,EAAWU,GAAgBA,EAAaV,SAHC,MAK3C,IAA0B,MAAbA,IAAb,EAAiD,MAAbA,IALO,CAYM,CAAC,CAApD,kBAAgBjC,OAAhB,CAAwB2C,EAAaV,QAArC,GACuD,QAAvDP,8BAAuC,UAAvCA,CAb6C,CAetCgB,kBAfsC,GAMtCxC,OAAOU,QAAPV,CAAgB0C,wBCZHC,qBAA2B,IACzCZ,GAAaN,EAAbM,SADyC,MAEhC,MAAbA,IAF6C,GAMlC,MAAbA,MAAuBS,gBAAgBf,EAAQmB,iBAAxBJ,KANwB,ECKnD,QAAwBK,QAAxB,GAAsC,OACZ,KAApBC,KAAKd,UAD2B,GAE3Ba,QAAQC,EAAKd,UAAba,ECGX,QAAwBE,uBAAxB,KAAmE,IAE7D,IAAa,CAACC,EAAStB,QAAvB,EAAmC,EAAnC,EAAgD,CAACuB,EAASvB,eACrD1B,QAAOU,QAAPV,CAAgB0C,mBAInBQ,GACJF,EAASG,uBAATH,IACAI,KAAKC,4BACDC,EAAQJ,MACRK,EAAML,MAGNM,EAAQ9C,SAAS+C,WAAT/C,KACRgD,WAAgB,EAf2C,GAgB3DC,SAAY,EAhB+C,IAiBzDC,GAA4BJ,EAA5BI,2BAILZ,OACCC,KADDD,EAEDM,EAAMO,QAANP,UAEIX,wBAIGH,sBAIHsB,GAAejB,WAjC4C,MAkC7DiB,GAAa7B,IAlCgD,CAmCxDc,uBAAuBe,EAAa7B,IAApCc,GAnCwD,CAqCxDA,yBAAiCF,WAAkBZ,IAAnDc,ECzCX,QAAwBgB,UAAxB,GAAyD,IAAdC,0DAAO,MAC1CC,EAAqB,KAATD,KAAiB,WAAjBA,CAA+B,aAC3CjC,EAAWN,EAAQM,YAER,MAAbA,MAAoC,MAAbA,KAAqB,IACxCmC,GAAOlE,OAAOU,QAAPV,CAAgB0C,gBACvByB,EAAmBnE,OAAOU,QAAPV,CAAgBmE,gBAAhBnE,UAClBmE,YAGF1C,MCPT,QAAwB2C,cAAxB,KAAuE,IAAlBC,4CAAAA,eAC7CC,EAAYP,YAAmB,KAAnBA,EACZQ,EAAaR,YAAmB,MAAnBA,EACbS,EAAWH,EAAW,CAAC,CAAZA,CAAgB,WAC5BI,KAAOH,MACPI,QAAUJ,MACVK,MAAQJ,MACRK,OAASL,MCRhB,QAAwBM,eAAxB,KAAqD,IAC7CC,GAAiB,GAATC,KAAe,MAAfA,CAAwB,MAChCC,EAAkB,MAAVF,IAAmB,OAAnBA,CAA6B,eAGzC,CAACG,oBAAAA,EAA8BC,KAA9BD,CAAoC,IAApCA,EAA0C,CAA1CA,CAAD,CACA,EAACA,oBAAAA,EAA8BC,KAA9BD,CAAoC,IAApCA,EAA0C,CAA1CA,ECVL,GAAIE,OAAJ,UAEe,UAAW,OACpBA,yBACmD,CAAC,CAA7C9E,aAAU+E,UAAV/E,CAAqBP,OAArBO,CAA6B,SAA7BA,GAEJ8E,OANT,SCJSE,iBAAyC,OACzCC,MAAKC,GAALD,CACLnD,YAAAA,CADKmD,CAELnD,YAAAA,CAFKmD,CAGLpB,YAAAA,CAHKoB,CAILpB,YAAAA,CAJKoB,CAKLpB,YAAAA,CALKoB,CAMLH,WACIjB,YAAAA,EACAsB,YAAgC,QAATT,KAAoB,KAApBA,CAA4B,OAAnDS,CADAtB,CAEAsB,YAAgC,QAATT,KAAoB,QAApBA,CAA+B,QAAtDS,CAHJL,CAII,CAVCG,EAcT,QAAwBG,eAAxB,EAAyC,IACjCtD,GAAOnC,OAAOU,QAAPV,CAAgBmC,KACvB+B,EAAOlE,OAAOU,QAAPV,CAAgB0C,gBACvB8C,EAAgBL,YAAYnF,OAAO4B,gBAAP5B,UAE3B,QACGqF,QAAQ,QAARA,OADH,OAEEA,QAAQ,OAARA,OAFF,2pBCfT,QAAwBK,cAAxB,GAA+C,6BAGpCC,EAAQhB,IAARgB,CAAeA,EAAQC,aACtBD,EAAQlB,GAARkB,CAAcA,EAAQE,SCGlC,QAAwBC,sBAAxB,GAAuD,IACjDC,SAKAZ,cACE,GACK1D,EAAQqE,qBAARrE,EADL,IAEI6C,GAAYP,YAAmB,KAAnBA,EACZQ,EAAaR,YAAmB,MAAnBA,IACdU,MAJH,GAKGE,OALH,GAMGD,SANH,GAOGE,QAPP,CAQE,QAAY,SAEPnD,EAAQqE,qBAARrE,MAGHuE,GAAS,MACPD,EAAKpB,IADE,KAERoB,EAAKtB,GAFG,OAGNsB,EAAKnB,KAALmB,CAAaA,EAAKpB,IAHZ,QAILoB,EAAKrB,MAALqB,CAAcA,EAAKtB,GAJd,EAQTwB,EAA6B,MAArBxE,KAAQM,QAARN,CAA8BgE,gBAA9BhE,IACRmE,EACJK,EAAML,KAANK,EAAexE,EAAQyE,WAAvBD,EAAsCD,EAAOpB,KAAPoB,CAAeA,EAAOrB,KACxDkB,EACJI,EAAMJ,MAANI,EAAgBxE,EAAQ0E,YAAxBF,EAAwCD,EAAOtB,MAAPsB,CAAgBA,EAAOvB,IAE7D2B,EAAiB3E,EAAQ4E,WAAR5E,GACjB6E,EAAgB7E,EAAQ8E,YAAR9E,MAIhB2E,KAAiC,IAC7BnB,GAASzD,+BACGqD,iBAAuB,GAAvBA,CAFiB,IAGlBA,iBAAuB,GAAvBA,CAHkB,GAK5Be,QAL4B,GAM5BC,gBAGFH,0BCvDec,0CAAuD,IACvErB,GAASsB,WACTC,EAA6B,MAApBC,KAAO5E,SAChB6E,EAAed,yBACfe,EAAaf,yBACbgB,EAAe5E,mBAEf+C,EAASzD,4BACTuF,EAAiB,CAAC9B,EAAO8B,cAAP9B,CAAsBC,KAAtBD,CAA4B,IAA5BA,EAAkC,CAAlCA,EAClB+B,EAAkB,CAAC/B,EAAO+B,eAAP/B,CAAuBC,KAAvBD,CAA6B,IAA7BA,EAAmC,CAAnCA,EAErBU,EAAUD,cAAc,KACrBkB,EAAanC,GAAbmC,CAAmBC,EAAWpC,GAA9BmC,EADqB,MAEpBA,EAAajC,IAAbiC,CAAoBC,EAAWlC,IAA/BiC,EAFoB,OAGnBA,EAAahB,KAHM,QAIlBgB,EAAaf,MAJK,CAAdH,OAMNuB,UAAY,IACZC,WAAa,EAMjB,MAAmB,IACfD,GAAY,CAAChC,EAAOgC,SAAPhC,CAAiBC,KAAjBD,CAAuB,IAAvBA,EAA6B,CAA7BA,EACbiC,EAAa,CAACjC,EAAOiC,UAAPjC,CAAkBC,KAAlBD,CAAwB,IAAxBA,EAA8B,CAA9BA,IAEZR,KAAOsC,GAJM,GAKbrC,QAAUqC,GALG,GAMbpC,MAAQqC,GANK,GAObpC,OAASoC,GAPI,GAUbC,WAVa,GAWbC,oBAIR/B,EACIwB,EAAO9C,QAAP8C,GADJxB,CAEIwB,OAAqD,MAA1BG,KAAa/E,cAElCqC,8BC9CU+C,iDAAuD,OAG/D7B,KAAKC,GAH0D,CACvErB,EAAOlE,OAAOU,QAAPV,CAAgB0C,eADgD,CAEvE0E,EAAiBZ,yCAFsD,CAGvEZ,EAAQN,EAASpB,EAAKgC,WAAdZ,CAA2BtF,OAAOqH,UAAPrH,EAAqB,CAAhDsF,CAH+D,CAIvEO,EAASP,EAASpB,EAAKiC,YAAdb,CAA4BtF,OAAOsH,WAAPtH,EAAsB,CAAlDsF,CAJ8D,CAMvEhB,EAAYP,YAN2D,CAOvEQ,EAAaR,YAAgB,MAAhBA,CAP0D,CASvEwD,EAAS,KACRjD,EAAY8C,EAAe3C,GAA3BH,CAAiC8C,EAAeH,SADxC,MAEP1C,EAAa6C,EAAezC,IAA5BJ,CAAmC6C,EAAeF,UAF3C,QAAA,SAAA,CAT8D,OAgBtExB,kBCTT,QAAwB8B,QAAxB,GAAyC,IACjCzF,GAAWN,EAAQM,SADc,MAEtB,MAAbA,MAAoC,MAAbA,IAFY,MAKe,OAAlDP,8BAAkC,UAAlCA,CALmC,GAQhCgG,QAAQ1F,gBAAR0F,ECDT,QAAwBC,cAAxB,SAKE,IAEIC,GAAa,CAAEjD,IAAK,CAAP,CAAUE,KAAM,CAAhB,EACXlC,EAAeM,+BAGK,UAAtB4E,OACWR,qDACR,IAEDS,GACsB,cAAtBD,IAHC,IAIczF,gBAAgBJ,gBAAhBI,CAJd,CAK6B,MAA5B0F,KAAe7F,QALhB,KAMgB/B,OAAOU,QAAPV,CAAgB0C,eANhC,GAQ4B,QAAtBiF,IARN,GASc3H,OAAOU,QAAPV,CAAgB0C,eAT9B,IAAA,IAcCiD,GAAUa,6CAMgB,MAA5BoB,KAAe7F,QAAf6F,EAAsC,CAACJ,WAAuB,OACtC/B,iBAAlBI,IAAAA,OAAQD,IAAAA,QACLnB,KAAOkB,EAAQlB,GAARkB,CAAcA,EAAQsB,SAFwB,GAGrDvC,OAASmB,EAASF,EAAQlB,GAH2B,GAIrDE,MAAQgB,EAAQhB,IAARgB,CAAeA,EAAQuB,UAJsB,GAKrDtC,MAAQgB,EAAQD,EAAQhB,IALrC,mBAaSA,UACAF,SACAG,WACAF,oBCjEJmD,WAA2B,IAAjBjC,KAAAA,MAAOC,IAAAA,aACjBD,KAYT,QAAwBkC,qBAAxB,WAOE,IADAC,0DAAU,KAEwB,CAAC,CAA/BC,KAAUlI,OAAVkI,CAAkB,MAAlBA,cAIEN,GAAaD,uBAObQ,EAAQ,KACP,OACIP,EAAW9B,KADf,QAEKsC,EAAQzD,GAARyD,CAAcR,EAAWjD,GAF9B,CADO,OAKL,OACEiD,EAAW9C,KAAX8C,CAAmBQ,EAAQtD,KAD7B,QAEG8C,EAAW7B,MAFd,CALK,QASJ,OACC6B,EAAW9B,KADZ,QAEE8B,EAAWhD,MAAXgD,CAAoBQ,EAAQxD,MAF9B,CATI,MAaN,OACGwD,EAAQvD,IAARuD,CAAeR,EAAW/C,IAD7B,QAEI+C,EAAW7B,MAFf,CAbM,EAmBRsC,EAAcC,OAAOC,IAAPD,IACjBE,GADiBF,CACb,oCAEAH,WACGJ,QAAQI,IAARJ,GAJU,CAAAO,EAMjBG,IANiBH,CAMZ,oBAAUI,GAAEC,IAAFD,CAASE,EAAED,IANT,CAAAL,EAQdO,EAAgBR,EAAYS,MAAZT,CACpB,eAAGvC,KAAAA,MAAOC,IAAAA,aACRD,IAASiD,EAAO3C,WAAhBN,EAA+BC,GAAUgD,EAAO1C,YAF9B,CAAAgC,EAKhBW,EAA2C,CAAvBH,GAAcvI,MAAduI,CACtBA,EAAc,CAAdA,EAAiBI,GADKJ,CAEtBR,EAAY,CAAZA,EAAeY,IAEbC,EAAYhB,EAAU9C,KAAV8C,CAAgB,GAAhBA,EAAqB,CAArBA,QAEXc,IAAqBE,OAAAA,CAA8B,EAAnDF,EC5DT,QAAwBG,oBAAxB,OAAsE,IAC9DC,GAAqBnG,kCACpByD,2CCPT,QAAwB2C,cAAxB,GAA+C,IACvClE,GAASjF,OAAO4B,gBAAP5B,IACToJ,EAAIC,WAAWpE,EAAOgC,SAAlBoC,EAA+BA,WAAWpE,EAAOqE,YAAlBD,EACnCE,EAAIF,WAAWpE,EAAOiC,UAAlBmC,EAAgCA,WAAWpE,EAAOuE,WAAlBH,EACpCrD,EAAS,OACNvE,EAAQ4E,WAAR5E,EADM,QAELA,EAAQ8E,YAAR9E,EAFK,WCJjB,QAAwBgI,qBAAxB,GAAwD,IAChDC,GAAO,CAAE/E,KAAM,OAAR,CAAiBC,MAAO,MAAxB,CAAgCF,OAAQ,KAAxC,CAA+CD,IAAK,QAApD,QACNuD,GAAU2B,OAAV3B,CAAkB,wBAAlBA,CAA4C,kBAAW0B,KAAvD,CAAA1B,ECIT,QAAwB4B,iBAAxB,OAA8E,GAChE5B,EAAU9C,KAAV8C,CAAgB,GAAhBA,EAAqB,CAArBA,CADgE,IAItE6B,GAAaV,iBAGbW,EAAgB,OACbD,EAAWjE,KADE,QAEZiE,EAAWhE,MAFC,EAMhBkE,EAAmD,CAAC,CAA1C,oBAAkBjK,OAAlB,IACVkK,EAAWD,EAAU,KAAVA,CAAkB,OAC7BE,EAAgBF,EAAU,MAAVA,CAAmB,MACnCG,EAAcH,EAAU,QAAVA,CAAqB,QACnCI,EAAuB,EAAsB,OAAtB,CAAW,qBAGtCC,KACAA,KAAgC,CADhCA,CAEAP,KAA0B,OACxB7B,MAEAoC,KAAkCP,KAGlCO,EAAiBX,uBAAjBW,IChCN,QAAwBC,KAAxB,KAAyC,OAEnCC,OAAMC,SAAND,CAAgBD,IAFmB,CAG9BG,EAAIH,IAAJG,GAH8B,CAOhCA,EAAI5B,MAAJ4B,IAAkB,CAAlBA,ECLT,QAAwBC,UAAxB,OAAoD,IAE9CH,MAAMC,SAAND,CAAgBG,gBACXD,GAAIC,SAAJD,CAAc,kBAAOE,SAArB,CAAAF,KAIHG,GAAQN,OAAU,kBAAOO,SAAjB,CAAAP,QACPG,GAAI1K,OAAJ0K,ICLT,QAAwBK,aAAxB,OAA4D,IACpDC,GAAiBC,aAEnBC,EAAUC,KAAVD,CAAgB,CAAhBA,CAAmBP,YAAqB,MAArBA,GAAnBO,WAEWE,QAAQ,WAAY,CAC7B1G,EAAS2G,QADoB,UAEvBC,KAAK,wDAFkB,IAI3BxL,GAAK4E,EAAS2G,QAAT3G,EAAqBA,EAAS5E,GACrC4E,EAAS6G,OAAT7G,EAAoBpD,aALS,KAS1BuE,QAAQkD,OAASnD,cAAc4F,EAAK3F,OAAL2F,CAAazC,MAA3BnD,CATS,GAU1BC,QAAQ4F,UAAY7F,cAAc4F,EAAK3F,OAAL2F,CAAaC,SAA3B7F,CAVM,GAYxB9F,MAZwB,CAAnC,KCPF,QAAwB4L,OAAxB,EAAiC,KAE3B,KAAKC,KAAL,CAAWC,gBAIXJ,GAAO,UACC,IADD,UAAA,eAAA,cAAA,WAAA,WAAA,IAUN3F,QAAQ4F,UAAYtC,oBACvB,KAAKwC,KADkBxC,CAEvB,KAAKJ,MAFkBI,CAGvB,KAAKsC,SAHkBtC,IASpBjB,UAAYF,qBACf,KAAK6D,OAAL,CAAa3D,SADEF,CAEfwD,EAAK3F,OAAL2F,CAAaC,SAFEzD,CAGf,KAAKe,MAHUf,CAIf,KAAKyD,SAJUzD,CAKf,KAAK6D,OAAL,CAAaX,SAAb,CAAuBY,IAAvB,CAA4BjE,iBALbG,CAMf,KAAK6D,OAAL,CAAaX,SAAb,CAAuBY,IAAvB,CAA4B7D,OANbD,IAUZ+D,kBAAoBP,EAAKtD,YAGzBrC,QAAQkD,OAASe,iBACpB,KAAKf,MADee,CAEpB0B,EAAK3F,OAAL2F,CAAaC,SAFO3B,CAGpB0B,EAAKtD,SAHe4B,IAKjBjE,QAAQkD,OAAOiD,SAAW,aAGxBjB,aAAa,KAAKG,SAAlBH,IAIF,KAAKY,KAAL,CAAWM,eAITJ,QAAQK,kBAHRP,MAAMM,kBACNJ,QAAQM,cC1DjB,QAAwBC,kBAAxB,KAAmE,OAC1DlB,GAAUrL,IAAVqL,CACL,eAAGmB,KAAAA,KAAMd,IAAAA,cAAcA,IAAWc,KAD7B,CAAAnB,ECAT,QAAwBoB,yBAAxB,GAA2D,KAIpD,GAHCC,+BAGD,CAFCC,EAAYzK,EAAS0K,MAAT1K,CAAgB,CAAhBA,EAAmB2K,WAAnB3K,GAAmCA,EAASoJ,KAATpJ,CAAe,CAAfA,CAEhD,CAAI1B,EAAI,EAAGA,EAAIkM,EAASjM,MAATiM,CAAkB,EAAGlM,IAAK,IACtCsM,GAASJ,KACTK,EAAUD,QAAAA,MACmC,WAA/C,QAAOzM,QAAOU,QAAPV,CAAgBmC,IAAhBnC,CAAqB2M,KAArB3M,mBAIN,MCVT,QAAwB4M,QAAxB,EAAkC,aAC3BnB,MAAMC,eAGPQ,kBAAkB,KAAKlB,SAAvBkB,CAAkC,YAAlCA,SACGrD,OAAOgE,gBAAgB,oBACvBhE,OAAO8D,MAAMhI,KAAO,QACpBkE,OAAO8D,MAAMb,SAAW,QACxBjD,OAAO8D,MAAMlI,IAAM,QACnBoE,OAAO8D,MAAMP,yBAAyB,WAAzBA,GAAyC,SAGxDU,wBAID,KAAKnB,OAAL,CAAaoB,sBACVlE,OAAO7G,WAAWgL,YAAY,KAAKnE,QAEnC,aCzBAoE,+BAAoE,IACrEC,GAAmC,MAA1BpG,KAAa/E,SACtBoL,EAASD,EAASlN,MAATkN,KACRE,qBAAkC,CAAEC,UAAF,EAHkC,0BAOvEnL,gBAAgBiL,EAAOnL,UAAvBE,QAPuE,GAa7DoL,QAShB,QAAwBC,oBAAxB,SAKE,GAEMC,aAFN,QAGOJ,iBAAiB,SAAU3B,EAAM+B,YAAa,CAAEH,UAAF,EAHrD,IAMMI,GAAgBvL,kDAGpB,SACAuJ,EAAM+B,YACN/B,EAAMiC,iBAEFD,kBACAE,mBCnCR,QAAwBC,qBAAxB,EAA+C,CACxC,KAAKnC,KAAL,CAAWkC,aAD6B,QAEtClC,MAAQ8B,oBACX,KAAKhC,SADMgC,CAEX,KAAK5B,OAFM4B,CAGX,KAAK9B,KAHM8B,CAIX,KAAKM,cAJMN,CAF8B,ECF/C,QAAwBO,qBAAxB,KAA+D,eAEtDC,oBAAoB,SAAUtC,EAAM+B,eAGrCE,cAAcxC,QAAQ,WAAU,GAC7B6C,oBAAoB,SAAUtC,EAAM+B,YAD7C,KAKMA,YAAc,OACdE,mBACAD,cAAgB,OAChBE,mBCVR,QAAwBb,sBAAxB,EAAgD,CAC1C,KAAKrB,KAAL,CAAWkC,aAD+B,UAErCK,qBAAqB,KAAKH,eAFW,MAGvCpC,MAAQqC,qBAAqB,KAAKvC,SAA1BuC,CAAqC,KAAKrC,KAA1CqC,CAH+B,ECFhD,QAAwBG,UAAxB,GAAqC,OACtB,EAANC,MAAY,CAACC,MAAM9E,aAAN8E,CAAbD,EAAqCE,YCE9C,QAAwBC,UAAxB,KAAmD,QAC1ChG,QAAa6C,QAAQ,WAAQ,IAC9BoD,GAAO,GAIP,CAAC,CADH,oDAAsDxO,OAAtD,KAEAmO,UAAUhJ,IAAVgJ,CANgC,KAQzB,IARyB,IAU1BtB,SAAc1H,MAVxB,GCHF,QAAwBsJ,cAAxB,KAA2D,QAClDlG,QAAiB6C,QAAQ,WAAe,IACvCsD,GAAQzN,KACVyN,MAFyC,GAKnC3B,kBALmC,GAGnC7L,eAAmBD,KAH/B,GCKF,QAAwB0N,WAAxB,GAAyC,kBAK7BnD,EAAKoD,QAALpD,CAAczC,OAAQyC,EAAKrG,sBAIvBqG,EAAKoD,QAALpD,CAAczC,OAAQyC,EAAKvK,YAGrCuK,EAAKqD,YAALrD,EAAqBlD,OAAOC,IAAPD,CAAYkD,EAAKsD,WAAjBxG,EAA8BhI,kBAC3CkL,EAAKqD,aAAcrD,EAAKsD,eAgBtC,QAAgBC,iBAAhB,WAME,IAEMzE,GAAmBnB,2BAKnBjB,EAAYF,qBAChB6D,EAAQ3D,SADQF,OAKhB6D,EAAQX,SAARW,CAAkBC,IAAlBD,CAAuBhE,iBALPG,CAMhB6D,EAAQX,SAARW,CAAkBC,IAAlBD,CAAuB5D,OANPD,WASX9G,aAAa,6BAIF,CAAE8K,SAAU,UAAZ,KCzDpB,QAAwBgD,aAAxB,KAAoD,OA6B1CxJ,KAAKyJ,KA7BqC,CAC1C3F,EAASuC,EAATvC,CAD0C,CACvCG,EAAMoC,EAANpC,CADuC,CAE1CV,EAAWyC,EAAK3F,OAAL2F,CAAXzC,MAF0C,CAK5CmG,EAA8B3E,KAClCiB,EAAKoD,QAALpD,CAAcN,SADoBX,CAElC,kBAA8B,YAAlB7F,KAAS2H,IAFa,CAAA9B,EAGlC4E,eARgD,CAS9CD,UAT8C,UAUxC5D,KACN,gIAX8C,IAoD9CzG,GAAMF,EAtCJwK,EACJD,WAEIrD,EAAQsD,eAFZD,GAIIvM,EAAeD,gBAAgB8I,EAAKoD,QAALpD,CAAczC,MAA9BrG,EACf0M,EAAmBpJ,yBAGnBb,EAAS,UACH4D,EAAOiD,QADJ,EAKTnG,EAAU,MACRL,EAAWuD,EAAOlE,IAAlBW,CADQ,KAETA,EAAWuD,EAAOpE,GAAlBa,CAFS,QAGNA,EAAWuD,EAAOnE,MAAlBY,CAHM,OAIPA,EAAWuD,EAAOjE,KAAlBU,CAJO,EAOVR,EAAc,QAANsE,KAAiB,KAAjBA,CAAyB,SACjCpE,EAAc,OAANuE,KAAgB,MAAhBA,CAAyB,QAKjC4F,EAAmB/C,yBAAyB,WAAzBA,OAYX,QAAVtH,IACI,CAACoK,EAAiBrJ,MAAlB,CAA2BF,EAAQjB,OAEnCiB,EAAQlB,MAEF,OAAVO,IACK,CAACkK,EAAiBtJ,KAAlB,CAA0BD,EAAQf,MAElCe,EAAQhB,KAEbsK,kDAEc,OACA,IACTG,WAAa,gBACf,IAECC,GAAsB,QAAVvK,IAAqB,CAAC,CAAtBA,CAA0B,EACtCwK,EAAuB,OAAVtK,IAAoB,CAAC,CAArBA,CAAyB,OAC5BP,GAJX,MAKWE,GALX,GAMEyK,WAAgBtK,MAAAA,MAInB/D,GAAa,eACFuK,EAAKtD,SADH,WAKdjH,yBAAiCuK,EAAKvK,cACtCkE,qBAAyBqG,EAAKrG,UAC9B2J,wBAAmBtD,EAAK3F,OAAL2F,CAAaiE,MAAUjE,EAAKsD,eCrFtD,QAAwBY,mBAAxB,OAIE,IACMC,GAAapF,OAAgB,eAAG8B,KAAAA,WAAWA,MAA9B,CAAA9B,EAEbqF,EACJ,CAAC,EAAD,EACA1E,EAAUrL,IAAVqL,CAAe,WAAY,OAEvBxG,GAAS2H,IAAT3H,MACAA,EAAS6G,OADT7G,EAEAA,EAAStB,KAATsB,CAAiBiL,EAAWvM,KAJhC,CAAA8H,KAQE,GAAa,IACTyE,qBAEErE,cACHuE,4BAAAA,8DAAAA,iBCrBT,QAAwBJ,MAAxB,KAA6C,IAEvC,CAACC,mBAAmBlE,EAAKoD,QAALpD,CAAcN,SAAjCwE,CAA4C,OAA5CA,CAAqD,cAArDA,cAIDb,GAAehD,EAAQlK,WAGC,QAAxB,iBACa6J,EAAKoD,QAALpD,CAAczC,MAAdyC,CAAqBsE,aAArBtE,IAGX,qBAMA,CAACA,EAAKoD,QAALpD,CAAczC,MAAdyC,CAAqBzH,QAArByH,mBACKF,KACN,sEAMApD,GAAYsD,EAAKtD,SAALsD,CAAepG,KAAfoG,CAAqB,GAArBA,EAA0B,CAA1BA,IACYA,EAAK3F,QAA3BkD,IAAAA,OAAQ0C,IAAAA,UACVsE,EAAsD,CAAC,CAA1C,oBAAkB/P,OAAlB,IAEbgQ,EAAMD,EAAa,QAAbA,CAAwB,QAC9BE,EAAkBF,EAAa,KAAbA,CAAqB,OACvC7L,EAAO+L,EAAgBC,WAAhBD,GACPE,EAAUJ,EAAa,MAAbA,CAAsB,MAChCK,EAASL,EAAa,QAAbA,CAAwB,QACjCM,EAAmBhH,oBAQrBoC,OAAuC1C,IA5CA,KA6CpClD,QAAQkD,WACXA,MAAgB0C,MAAhB1C,CA9CuC,EAiDvC0C,OAAqC1C,IAjDE,KAkDpClD,QAAQkD,WACX0C,OAAqC1C,IAnDE,KAuDrCuH,GAAS7E,KAAkBA,KAAiB,CAAnCA,CAAuC4E,EAAmB,EAInEE,EAAmB7O,yBACvB8J,EAAKoD,QAALpD,CAAczC,MADSrH,WAAAA,EAGvBmI,OAHuBnI,CAGf,IAHeA,CAGT,EAHSA,EAIrB8O,EACFF,EAAS1K,cAAc4F,EAAK3F,OAAL2F,CAAazC,MAA3BnD,IAAT0K,YAGU9K,KAAKC,GAALD,CAASA,KAAKiL,GAALjL,CAASuD,MAATvD,GAATA,CAA8D,CAA9DA,IAEPqJ,iBACAhJ,QAAQ4J,WACR5J,QAAQ4J,SAAcjK,KAAKkL,KAALlL,MACtBK,QAAQ4J,SAAiB,KC7EhC,QAAwBkB,qBAAxB,GAAwD,IACpC,KAAdzH,WACK,QAF6C,MAG7B,OAAdA,IAH2C,CAI7C,KAJ6C,GCwBxD,iLAAA,CC5BM0H,gBAAkBC,WAAW1F,KAAX0F,CAAiB,CAAjBA,CD4BxB,CChBA,QAAwBC,UAAxB,GAA8D,IAAjBC,4CAAAA,eACrCC,EAAQJ,gBAAgB5Q,OAAhB4Q,IACRlG,EAAMkG,gBACTzF,KADSyF,CACHI,EAAQ,CADLJ,EAETK,MAFSL,CAEFA,gBAAgBzF,KAAhByF,CAAsB,CAAtBA,GAFEA,QAGLG,GAAUrG,EAAIwG,OAAJxG,EAAVqG,MCZHI,WAAY,MACV,MADU,WAEL,WAFK,kBAGE,kBAHF,EAalB,QAAwBrF,KAAxB,KAA4C,IAEtCM,kBAAkBZ,EAAKoD,QAALpD,CAAcN,SAAhCkB,CAA2C,OAA3CA,cAIAZ,EAAK4F,OAAL5F,EAAgBA,EAAKtD,SAALsD,GAAmBA,EAAKO,8BAKtCnE,GAAaD,cACjB6D,EAAKoD,QAALpD,CAAczC,MADGpB,CAEjB6D,EAAKoD,QAALpD,CAAcC,SAFG9D,CAGjBkE,EAAQ5D,OAHSN,CAIjBkE,EAAQhE,iBAJSF,EAOfO,EAAYsD,EAAKtD,SAALsD,CAAepG,KAAfoG,CAAqB,GAArBA,EAA0B,CAA1BA,EACZ6F,EAAoB1H,wBACpBT,EAAYsC,EAAKtD,SAALsD,CAAepG,KAAfoG,CAAqB,GAArBA,EAA0B,CAA1BA,GAAgC,GAE5C8F,YAEIzF,EAAQ0F,cACTJ,WAAUK,OACD,gBAETL,WAAUM,YACDX,uBAETK,WAAUO,mBACDZ,gCAGAjF,EAAQ0F,mBAGdnG,QAAQ,aAAiB,IAC7BlD,OAAsBoJ,EAAUhR,MAAVgR,GAAqBN,EAAQ,aAI3CxF,EAAKtD,SAALsD,CAAepG,KAAfoG,CAAqB,GAArBA,EAA0B,CAA1BA,CALqB,GAMb7B,uBANa,IAQ3BK,GAAgBwB,EAAK3F,OAAL2F,CAAazC,OAC7B4I,EAAanG,EAAK3F,OAAL2F,CAAaC,UAG1BwD,EAAQzJ,KAAKyJ,MACb2C,EACW,MAAd1J,MACC+G,EAAMjF,EAAclF,KAApBmK,EAA6BA,EAAM0C,EAAW9M,IAAjBoK,CAD9B/G,EAEc,OAAdA,MACC+G,EAAMjF,EAAcnF,IAApBoK,EAA4BA,EAAM0C,EAAW7M,KAAjBmK,CAH7B/G,EAIc,KAAdA,MACC+G,EAAMjF,EAAcpF,MAApBqK,EAA8BA,EAAM0C,EAAWhN,GAAjBsK,CAL/B/G,EAMc,QAAdA,MACC+G,EAAMjF,EAAcrF,GAApBsK,EAA2BA,EAAM0C,EAAW/M,MAAjBqK,EAEzB4C,EAAgB5C,EAAMjF,EAAcnF,IAApBoK,EAA4BA,EAAMrH,EAAW/C,IAAjBoK,EAC5C6C,EAAiB7C,EAAMjF,EAAclF,KAApBmK,EAA6BA,EAAMrH,EAAW9C,KAAjBmK,EAC9C8C,EAAe9C,EAAMjF,EAAcrF,GAApBsK,EAA2BA,EAAMrH,EAAWjD,GAAjBsK,EAC1C+C,EACJ/C,EAAMjF,EAAcpF,MAApBqK,EAA8BA,EAAMrH,EAAWhD,MAAjBqK,EAE1BgD,EACW,MAAd/J,SACc,OAAdA,OADAA,EAEc,KAAdA,OAFAA,EAGc,QAAdA,QAGG6H,EAAsD,CAAC,CAA1C,oBAAkB/P,OAAlB,IACbkS,EACJ,CAAC,CAACrG,EAAQsG,cAAV,GACEpC,GAA4B,OAAd7G,IAAd6G,KACCA,GAA4B,KAAd7G,IAAd6G,GADDA,EAEC,IAA6B,OAAd7G,IAAf,GAFD6G,EAGC,IAA6B,KAAd7G,IAAf,GAJH,EAtC+B,CA4C7B0I,OA5C6B,MA8C1BR,UA9C0B,EAgD3BQ,IAhD2B,MAiDjBN,EAAUN,EAAQ,CAAlBM,CAjDiB,QAqDjBX,uBArDiB,IAwD1BzI,UAAYA,GAAagB,EAAY,KAAZA,CAA8B,EAA3ChB,CAxDc,GA4D1BrC,QAAQkD,mBACRyC,EAAK3F,OAAL2F,CAAazC,OACbe,iBACD0B,EAAKoD,QAALpD,CAAczC,MADbe,CAED0B,EAAK3F,OAAL2F,CAAaC,SAFZ3B,CAGD0B,EAAKtD,SAHJ4B,EA9D0B,GAqExBiB,aAAaS,EAAKoD,QAALpD,CAAcN,SAA3BH,GAA4C,MAA5CA,CArEwB,CAAnC,KCpDF,QAAwBqH,aAAxB,GAA2C,OACX5G,EAAK3F,QAA3BkD,IAAAA,OAAQ0C,IAAAA,UACVvD,EAAYsD,EAAKtD,SAALsD,CAAepG,KAAfoG,CAAqB,GAArBA,EAA0B,CAA1BA,EACZyD,EAAQzJ,KAAKyJ,MACbc,EAAsD,CAAC,CAA1C,oBAAkB/P,OAAlB,IACbkE,EAAO6L,EAAa,OAAbA,CAAuB,SAC9BK,EAASL,EAAa,MAAbA,CAAsB,MAC/B3F,EAAc2F,EAAa,OAAbA,CAAuB,eAEvChH,MAAekG,EAAMxD,IAANwD,MACZpJ,QAAQkD,UACXkG,EAAMxD,IAANwD,EAA2BlG,MAE3BA,KAAiBkG,EAAMxD,IAANwD,MACdpJ,QAAQkD,UAAiBkG,EAAMxD,IAANwD,KCLlC,QAAgBoD,QAAhB,SAA2E,OA6B9D7M,KAAKC,GA7ByD,CAEnEL,EAAQkN,EAAIzH,KAAJyH,CAAU,2BAAVA,CAF2D,CAGnE5D,EAAQ,CAACtJ,EAAM,CAANA,CAH0D,CAInEoJ,EAAOpJ,EAAM,CAANA,CAJ4D,IAOrE,eAIsB,CAAtBoJ,KAAKxO,OAALwO,CAAa,GAAbA,EAAyB,IACvB7M,iBAEG,mBAGA,QACA,qBAKDsE,GAAOL,uBACNK,MAAoB,GAApBA,EAbT,CAcO,GAAa,IAATuI,MAA0B,IAATA,IAArB,CAAoC,IAErC+D,YACS,IAAT/D,KACKhJ,EACL5E,SAASgC,eAAThC,CAAyByF,YADpBb,CAELtF,OAAOsH,WAAPtH,EAAsB,CAFjBsF,EAKAA,EACL5E,SAASgC,eAAThC,CAAyBwF,WADpBZ,CAELtF,OAAOqH,UAAPrH,EAAqB,CAFhBsF,EAKF+M,EAAO,GAAPA,EAdF,UAiCT,QAAgBC,YAAhB,SAKE,IACM3M,SAKA4M,EAAyD,CAAC,CAA9C,oBAAkBzS,OAAlB,IAIZ0S,EAAYjL,EAAOrC,KAAPqC,CAAa,SAAbA,EAAwBe,GAAxBf,CAA4B,kBAAQkL,GAAKC,IAALD,EAApC,CAAAlL,EAIZoL,EAAUH,EAAU1S,OAAV0S,CACdnI,OAAgB,kBAAgC,CAAC,CAAzBoI,KAAKG,MAALH,CAAY,MAAZA,CAAxB,CAAApI,CADcmI,EAIZA,MAA0D,CAAC,CAArCA,QAAmB1S,OAAnB0S,CAA2B,GAA3BA,CAlB1B,UAmBUpH,KACN,+EApBJ,IA0BMyH,GAAa,cACfC,EAAkB,CAAC,CAAbH,KASN,GATMA,CACN,CACEH,EACGvH,KADHuH,CACS,CADTA,IAEGzB,MAFHyB,CAEU,CAACA,KAAmBtN,KAAnBsN,IAAqC,CAArCA,CAAD,CAFVA,CADF,CAIE,CAACA,KAAmBtN,KAAnBsN,IAAqC,CAArCA,CAAD,EAA0CzB,MAA1C,CACEyB,EAAUvH,KAAVuH,CAAgBG,EAAU,CAA1BH,CADF,CAJF,WAWEM,EAAIxK,GAAJwK,CAAQ,aAAe,IAErB5I,GAAc,CAAW,CAAV4G,KAAc,EAAdA,EAAD,EAChB,QADgB,CAEhB,QACAiC,WAEFC,GAGGC,MAHHD,CAGU,aAAU,OACQ,EAApBtK,KAAEA,EAAEtI,MAAFsI,CAAW,CAAbA,GAAoD,CAAC,CAA3B,aAAW5I,OAAX,GADd,IAEZ4I,EAAEtI,MAAFsI,CAAW,IAFC,KAAA,SAMZA,EAAEtI,MAAFsI,CAAW,KANC,KAAA,IAUPA,EAAEqI,MAAFrI,GAbb,CAAAsK,KAiBG1K,GAjBH0K,CAiBO,kBAAOb,iBAjBd,CAAAa,CAPE,CAAAF,IA6BF5H,QAAQ,aAAe,GACtBA,QAAQ,aAAkB,CACvB+C,YADuB,SAEPwE,GAA2B,GAAnBO,KAAGE,EAAS,CAAZF,EAAyB,CAAC,CAA1BA,CAA8B,CAAtCP,CAFO,CAA7B,EADF,KAmBF,QAAwBlL,OAAxB,KAAiD,IAI3C5B,GAJiC4B,IAAAA,OAC7BS,EAA8CsD,EAA9CtD,YAA8CsD,EAAnC3F,QAAWkD,IAAAA,OAAQ0C,IAAAA,UAChC4H,EAAgBnL,EAAU9C,KAAV8C,CAAgB,GAAhBA,EAAqB,CAArBA,WAGlBiG,UAAU,EAAVA,EACQ,CAAC,EAAD,CAAU,CAAV,EAEAqE,qBAGU,MAAlBa,QACK1O,KAAOkB,EAAQ,CAARA,IACPhB,MAAQgB,EAAQ,CAARA,GACY,OAAlBwN,QACF1O,KAAOkB,EAAQ,CAARA,IACPhB,MAAQgB,EAAQ,CAARA,GACY,KAAlBwN,QACFxO,MAAQgB,EAAQ,CAARA,IACRlB,KAAOkB,EAAQ,CAARA,GACa,QAAlBwN,SACFxO,MAAQgB,EAAQ,CAARA,IACRlB,KAAOkB,EAAQ,CAARA,KAGXkD,WCrLP,QAAwBuK,gBAAxB,KAAuD,IACjDzL,GACFgE,EAAQhE,iBAARgE,EAA6BnJ,gBAAgB8I,EAAKoD,QAALpD,CAAczC,MAA9BrG,EAK3B8I,EAAKoD,QAALpD,CAAcC,SAAdD,IAPiD,KAQ/B9I,kBAR+B,KAW/CkF,GAAaD,cACjB6D,EAAKoD,QAALpD,CAAczC,MADGpB,CAEjB6D,EAAKoD,QAALpD,CAAcC,SAFG9D,CAGjBkE,EAAQ5D,OAHSN,MAMXC,YAjB6C,IAmB/CxE,GAAQyI,EAAQ0H,SAClBxK,EAASyC,EAAK3F,OAAL2F,CAAazC,OAEpByK,EAAQ,oBACO,IACb9E,GAAQ3F,WAEVA,MAAoBnB,IAApBmB,EACA,CAAC8C,EAAQ4H,wBAEDjO,KAAKC,GAALD,CAASuD,IAATvD,CAA4BoC,IAA5BpC,yBAPA,CAAA,sBAWS,IACb0E,GAAyB,OAAdhC,KAAwB,MAAxBA,CAAiC,MAC9CwG,EAAQ3F,WAEVA,MAAoBnB,IAApBmB,EACA,CAAC8C,EAAQ4H,wBAEDjO,KAAKiL,GAALjL,CACNuD,IADMvD,CAENoC,MACiB,OAAdM,KAAwBa,EAAOjD,KAA/BoC,CAAuCa,EAAOhD,MADjD6B,CAFMpC,0BAlBA,WA4BR4F,QAAQ,WAAa,IACnBlH,GAA8C,CAAC,CAAxC,kBAAgBlE,OAAhB,IAET,WAFS,CACT,0BAEqBwT,QAJ3B,KAOK3N,QAAQkD,WC5Df,QAAwB2K,MAAxB,GAAoC,IAC5BxL,GAAYsD,EAAKtD,UACjBmL,EAAgBnL,EAAU9C,KAAV8C,CAAgB,GAAhBA,EAAqB,CAArBA,EAChByL,EAAiBzL,EAAU9C,KAAV8C,CAAgB,GAAhBA,EAAqB,CAArBA,OAGH,OACYsD,EAAK3F,QAA3B4F,IAAAA,UAAW1C,IAAAA,OACbgH,EAA0D,CAAC,CAA9C,oBAAkB/P,OAAlB,IACbkE,EAAO6L,EAAa,MAAbA,CAAsB,MAC7B3F,EAAc2F,EAAa,OAAbA,CAAuB,SAErC6D,EAAe,2BACFnI,KADE,yBAGTA,KAAkBA,IAAlBA,CAA2C1C,KAHlC,IAOhBlD,QAAQkD,qBAAyB6K,eChB1C,QAAwBC,KAAxB,GAAmC,IAC7B,CAACnE,mBAAmBlE,EAAKoD,QAALpD,CAAcN,SAAjCwE,CAA4C,MAA5CA,CAAoD,iBAApDA,cAICtH,GAAUoD,EAAK3F,OAAL2F,CAAaC,UACvBqI,EAAQvJ,KACZiB,EAAKoD,QAALpD,CAAcN,SADFX,CAEZ,kBAA8B,iBAAlB7F,KAAS2H,IAFT,CAAA9B,EAGZ3C,cAGAQ,EAAQxD,MAARwD,CAAiB0L,EAAMnP,GAAvByD,EACAA,EAAQvD,IAARuD,CAAe0L,EAAMhP,KADrBsD,EAEAA,EAAQzD,GAARyD,CAAc0L,EAAMlP,MAFpBwD,EAGAA,EAAQtD,KAARsD,CAAgB0L,EAAMjP,KACtB,IAEI2G,OAAKqI,gBAIJA,OANL,GAOK5S,WAAW,uBAAyB,EAZ3C,KAaO,IAEDuK,OAAKqI,gBAIJA,OANA,GAOA5S,WAAW,mCC/BpB,QAAwB8S,MAAxB,GAAoC,IAC5B7L,GAAYsD,EAAKtD,UACjBmL,EAAgBnL,EAAU9C,KAAV8C,CAAgB,GAAhBA,EAAqB,CAArBA,IACQsD,EAAK3F,QAA3BkD,IAAAA,OAAQ0C,IAAAA,UACVxB,EAAuD,CAAC,CAA9C,oBAAkBjK,OAAlB,IAEVgU,EAA4D,CAAC,CAA5C,kBAAgBhU,OAAhB,aAEhBiK,EAAU,MAAVA,CAAmB,OACxBwB,MACCuI,EAAiBjL,EAAOkB,EAAU,OAAVA,CAAoB,QAA3BlB,CAAjBiL,CAAwD,CADzDvI,IAGGvD,UAAYyB,0BACZ9D,QAAQkD,OAASnD,mBCSxB,cAAe,OASN,OAEE,GAFF,WAAA,IAMD8N,KANC,CATM,QAwDL,OAEC,GAFD,WAAA,IAMFjM,MANE,QAUE,CAVF,CAxDK,iBAsFI,OAER,GAFQ,WAAA,IAMX6L,eANW,yCAAA,SAmBN,CAnBM,mBAyBI,cAzBJ,CAtFJ,cA2HC,OAEL,GAFK,WAAA,IAMRlB,YANQ,CA3HD,OA8IN,OAEE,GAFF,WAAA,IAMD3C,KANC,SAQI,WARJ,CA9IM,MAoKP,OAEG,GAFH,WAAA,IAMA3D,IANA,UAaM,MAbN,SAkBK,CAlBL,mBAyBe,UAzBf,CApKO,OAuMN,OAEE,GAFF,WAAA,IAMDiI,KANC,CAvMM,MA0NP,OAEG,GAFH,WAAA,IAMAF,IANA,CA1NO,cAkPC,OAEL,GAFK,WAAA,IAMR7E,YANQ,mBAAA,GAkBT,QAlBS,GAwBT,OAxBS,CAlPD,YA4RD,OAEH,GAFG,WAAA,IAMNL,UANM,QAQFI,gBARE,uBAAA,CA5RC,CAAf,UCde,WAKF,QALE,iBAAA,mBAAA,UA0BH,UAAM,CA1BH,CAAA,UAoCH,UAAM,CApCH,CAAA,oBAAA,CDcf,CEpBqBkF,iCAS0B,YAAdpI,kFAAc,MAyF7CkC,eAAiB,iBAAMmG,uBAAsB,EAAKxI,MAA3BwI,CAzFsB,CAAA,MAEtCxI,OAASyI,SAAS,KAAKzI,MAAL,CAAY0I,IAAZ,CAAiB,IAAjB,CAATD,CAF6B,MAKtCtI,oBAAeoI,EAAOI,WALgB,MAQtC1I,MAAQ,eAAA,aAAA,iBAAA,CAR8B,MAetCF,UAAYA,EAAU6I,MAAV7I,CAAmBA,EAAU,CAAVA,CAAnBA,EAf0B,MAgBtC1C,OAASA,EAAOuL,MAAPvL,CAAgBA,EAAO,CAAPA,CAAhBA,EAhB6B,MAmBtC8C,QAAQX,YAnB8B,QAoBpC3C,iBACF0L,EAAOI,QAAPJ,CAAgB/I,UAChBW,EAAQX,YACVE,QAAQ,WAAQ,GACZS,QAAQX,yBAEP+I,EAAOI,QAAPJ,CAAgB/I,SAAhB+I,QAEApI,EAAQX,SAARW,CAAoBA,EAAQX,SAARW,GAApBA,IARR,EApB2C,MAiCtCX,UAAY5C,OAAOC,IAAPD,CAAY,KAAKuD,OAAL,CAAaX,SAAzB5C,EACdE,GADcF,CACV,qCAEA,EAAKuD,OAAL,CAAaX,SAAb,IAHU,CAAA5C,EAMdG,IANcH,CAMT,oBAAUM,GAAExF,KAAFwF,CAAUF,EAAEtF,KANb,CAAAkF,CAjC0B,MA6CtC4C,UAAUE,QAAQ,WAAmB,CACpCmJ,EAAgBhJ,OAAhBgJ,EAA2BjT,WAAWiT,EAAgBC,MAA3BlT,CADS,IAEtBkT,OACd,EAAK/I,UACL,EAAK1C,OACL,EAAK8C,UAEL,EAAKF,MAPX,EA7C2C,MA0DtCD,QA1DsC,IA4DrCmC,GAAgB,KAAKhC,OAAL,CAAagC,cA5DQ,QA+DpCC,sBA/DoC,MAkEtCnC,MAAMkC,oEAKJ,OACAnC,QAAOjK,IAAPiK,CAAY,IAAZA,mCAEC,OACDoB,SAAQrL,IAARqL,CAAa,IAAbA,gDAEc,OACdgB,sBAAqBrM,IAArBqM,CAA0B,IAA1BA,iDAEe,OACfd,uBAAsBvL,IAAtBuL,CAA2B,IAA3BA,UFtEX,CEpBqBiH,OAoHZQ,KApHYR,CAoHJ,CAAmB,WAAlB,QAAO/T,OAAP,CAAyCwU,MAAzC,CAAgCxU,MAAjC,EAAkDyU,YApH9CV,OAsHZpD,UAtHYoD,CAsHCpD,WAtHDoD,OAwHZI,QAxHYJ,CAwHDI"}