/*
 Copyright (C) <PERSON> 2017
 Distributed under the MIT License (license terms are at http://opensource.org/licenses/MIT).
 */const nativeHints=['native code','[object MutationObserverConstructor]'];var isNative=(e)=>nativeHints.some((t)=>-1<(e||'').toString().indexOf(t));const isBrowser='undefined'!=typeof window,longerTimeoutBrowsers=['Edge','Trident','Firefox'];let timeoutDuration=0;for(let e=0;e<longerTimeoutBrowsers.length;e+=1)if(isBrowser&&0<=navigator.userAgent.indexOf(longerTimeoutBrowsers[e])){timeoutDuration=1;break}function microtaskDebounce(e){let t=!1,o=0;const i=document.createElement('span'),n=new MutationObserver(()=>{e(),t=!1});return n.observe(i,{attributes:!0}),()=>{t||(t=!0,i.setAttribute('x-index',o),++o)}}function taskDebounce(e){let t=!1;return()=>{t||(t=!0,setTimeout(()=>{t=!1,e()},timeoutDuration))}}const supportsNativeMutationObserver=isBrowser&&isNative(window.MutationObserver);var debounce=supportsNativeMutationObserver?microtaskDebounce:taskDebounce;function isFunction(e){return e&&'[object Function]'==={}.toString.call(e)}function getStyleComputedProperty(e,t){if(1!==e.nodeType)return[];const o=window.getComputedStyle(e,null);return t?o[t]:o}function getParentNode(e){return'HTML'===e.nodeName?e:e.parentNode||e.host}function getScrollParent(e){if(!e||-1!==['HTML','BODY','#document'].indexOf(e.nodeName))return window.document.body;const{overflow:t,overflowX:o,overflowY:i}=getStyleComputedProperty(e);return /(auto|scroll)/.test(t+i+o)?e:getScrollParent(getParentNode(e))}function getOffsetParent(e){const t=e&&e.offsetParent,o=t&&t.nodeName;return o&&'BODY'!==o&&'HTML'!==o?-1!==['TD','TABLE'].indexOf(t.nodeName)&&'static'===getStyleComputedProperty(t,'position')?getOffsetParent(t):t:window.document.documentElement}function isOffsetContainer(e){const{nodeName:t}=e;return'BODY'!==t&&('HTML'===t||getOffsetParent(e.firstElementChild)===e)}function getRoot(e){return null===e.parentNode?e:getRoot(e.parentNode)}function findCommonOffsetParent(e,t){if(!e||!e.nodeType||!t||!t.nodeType)return window.document.documentElement;const o=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,i=o?e:t,n=o?t:e,r=document.createRange();r.setStart(i,0),r.setEnd(n,0);const{commonAncestorContainer:p}=r;if(e!==p&&t!==p||i.contains(n))return isOffsetContainer(p)?p:getOffsetParent(p);const d=getRoot(e);return d.host?findCommonOffsetParent(d.host,t):findCommonOffsetParent(e,getRoot(t).host)}function getScroll(e,t='top'){const o='top'===t?'scrollTop':'scrollLeft',i=e.nodeName;if('BODY'===i||'HTML'===i){const e=window.document.documentElement,t=window.document.scrollingElement||e;return t[o]}return e[o]}function includeScroll(e,t,o=!1){const i=getScroll(t,'top'),n=getScroll(t,'left'),r=o?-1:1;return e.top+=i*r,e.bottom+=i*r,e.left+=n*r,e.right+=n*r,e}function getBordersSize(e,t){const o='x'===t?'Left':'Top',i='Left'==o?'Right':'Bottom';return+e[`border${o}Width`].split('px')[0]+ +e[`border${i}Width`].split('px')[0]}let isIE10;var isIE10$1=function(){return void 0==isIE10&&(isIE10=-1!==navigator.appVersion.indexOf('MSIE 10')),isIE10};function getSize(e,t,o,i){return Math.max(t[`offset${e}`],t[`scroll${e}`],o[`client${e}`],o[`offset${e}`],o[`scroll${e}`],isIE10$1()?o[`offset${e}`]+i[`margin${'Height'===e?'Top':'Left'}`]+i[`margin${'Height'===e?'Bottom':'Right'}`]:0)}function getWindowSizes(){const e=window.document.body,t=window.document.documentElement,o=isIE10$1()&&window.getComputedStyle(t);return{height:getSize('Height',e,t,o),width:getSize('Width',e,t,o)}}var _extends=Object.assign||function(e){for(var t,o=1;o<arguments.length;o++)for(var i in t=arguments[o],t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e};function getClientRect(e){return _extends({},e,{right:e.left+e.width,bottom:e.top+e.height})}function getBoundingClientRect(e){let t={};if(isIE10$1())try{t=e.getBoundingClientRect();const o=getScroll(e,'top'),i=getScroll(e,'left');t.top+=o,t.left+=i,t.bottom+=o,t.right+=i}catch(e){}else t=e.getBoundingClientRect();const o={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},i='HTML'===e.nodeName?getWindowSizes():{},n=i.width||e.clientWidth||o.right-o.left,r=i.height||e.clientHeight||o.bottom-o.top;let p=e.offsetWidth-n,d=e.offsetHeight-r;if(p||d){const t=getStyleComputedProperty(e);p-=getBordersSize(t,'x'),d-=getBordersSize(t,'y'),o.width-=p,o.height-=d}return getClientRect(o)}function getOffsetRectRelativeToArbitraryNode(e,t){const o=isIE10$1(),i='HTML'===t.nodeName,n=getBoundingClientRect(e),r=getBoundingClientRect(t),p=getScrollParent(e),d=getStyleComputedProperty(t),s=+d.borderTopWidth.split('px')[0],a=+d.borderLeftWidth.split('px')[0];let f=getClientRect({top:n.top-r.top-s,left:n.left-r.left-a,width:n.width,height:n.height});if(f.marginTop=0,f.marginLeft=0,!o&&i){const e=+d.marginTop.split('px')[0],t=+d.marginLeft.split('px')[0];f.top-=s-e,f.bottom-=s-e,f.left-=a-t,f.right-=a-t,f.marginTop=e,f.marginLeft=t}return(o?t.contains(p):t===p&&'BODY'!==p.nodeName)&&(f=includeScroll(f,t)),f}function getViewportOffsetRectRelativeToArtbitraryNode(e){var t=Math.max;const o=window.document.documentElement,i=getOffsetRectRelativeToArbitraryNode(e,o),n=t(o.clientWidth,window.innerWidth||0),r=t(o.clientHeight,window.innerHeight||0),p=getScroll(o),d=getScroll(o,'left'),s={top:p-i.top+i.marginTop,left:d-i.left+i.marginLeft,width:n,height:r};return getClientRect(s)}function isFixed(e){const t=e.nodeName;return'BODY'===t||'HTML'===t?!1:!('fixed'!==getStyleComputedProperty(e,'position'))||isFixed(getParentNode(e))}function getBoundaries(e,t,o,i){let n={top:0,left:0};const r=findCommonOffsetParent(e,t);if('viewport'===i)n=getViewportOffsetRectRelativeToArtbitraryNode(r);else{let t;'scrollParent'===i?(t=getScrollParent(getParentNode(e)),'BODY'===t.nodeName&&(t=window.document.documentElement)):'window'===i?t=window.document.documentElement:t=i;const o=getOffsetRectRelativeToArbitraryNode(t,r);if('HTML'===t.nodeName&&!isFixed(r)){const{height:e,width:t}=getWindowSizes();n.top+=o.top-o.marginTop,n.bottom=e+o.top,n.left+=o.left-o.marginLeft,n.right=t+o.left}else n=o}return n.left+=o,n.top+=o,n.right-=o,n.bottom-=o,n}function getArea({width:e,height:t}){return e*t}function computeAutoPlacement(e,t,o,i,n,r=0){if(-1===e.indexOf('auto'))return e;const p=getBoundaries(o,i,r,n),d={top:{width:p.width,height:t.top-p.top},right:{width:p.right-t.right,height:p.height},bottom:{width:p.width,height:p.bottom-t.bottom},left:{width:t.left-p.left,height:p.height}},s=Object.keys(d).map((e)=>_extends({key:e},d[e],{area:getArea(d[e])})).sort((e,t)=>t.area-e.area),a=s.filter(({width:e,height:t})=>e>=o.clientWidth&&t>=o.clientHeight),f=0<a.length?a[0].key:s[0].key,l=e.split('-')[1];return f+(l?`-${l}`:'')}function getReferenceOffsets(e,t,o){const i=findCommonOffsetParent(t,o);return getOffsetRectRelativeToArbitraryNode(o,i)}function getOuterSizes(e){const t=window.getComputedStyle(e),o=parseFloat(t.marginTop)+parseFloat(t.marginBottom),i=parseFloat(t.marginLeft)+parseFloat(t.marginRight),n={width:e.offsetWidth+i,height:e.offsetHeight+o};return n}function getOppositePlacement(e){const t={left:'right',right:'left',bottom:'top',top:'bottom'};return e.replace(/left|right|bottom|top/g,(e)=>t[e])}function getPopperOffsets(e,t,o){o=o.split('-')[0];const i=getOuterSizes(e),n={width:i.width,height:i.height},r=-1!==['right','left'].indexOf(o),p=r?'top':'left',d=r?'left':'top',s=r?'height':'width',a=r?'width':'height';return n[p]=t[p]+t[s]/2-i[s]/2,n[d]=o===d?t[d]-i[a]:t[getOppositePlacement(d)],n}function find(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function findIndex(e,t,o){if(Array.prototype.findIndex)return e.findIndex((e)=>e[t]===o);const i=find(e,(e)=>e[t]===o);return e.indexOf(i)}function runModifiers(e,t,o){const i=void 0===o?e:e.slice(0,findIndex(e,'name',o));return i.forEach((e)=>{e.function&&console.warn('`modifier.function` is deprecated, use `modifier.fn`!');const o=e.function||e.fn;e.enabled&&isFunction(o)&&(t.offsets.popper=getClientRect(t.offsets.popper),t.offsets.reference=getClientRect(t.offsets.reference),t=o(t,e))}),t}function update(){if(this.state.isDestroyed)return;let e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=getReferenceOffsets(this.state,this.popper,this.reference),e.placement=computeAutoPlacement(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.offsets.popper=getPopperOffsets(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position='absolute',e=runModifiers(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}function isModifierEnabled(e,t){return e.some(({name:e,enabled:o})=>o&&e===t)}function getSupportedPropertyName(e){const t=[!1,'ms','Webkit','Moz','O'],o=e.charAt(0).toUpperCase()+e.slice(1);for(let n=0;n<t.length-1;n++){const i=t[n],r=i?`${i}${o}`:e;if('undefined'!=typeof window.document.body.style[r])return r}return null}function destroy(){return this.state.isDestroyed=!0,isModifierEnabled(this.modifiers,'applyStyle')&&(this.popper.removeAttribute('x-placement'),this.popper.style.left='',this.popper.style.position='',this.popper.style.top='',this.popper.style[getSupportedPropertyName('transform')]=''),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function attachToScrollParents(e,t,o,i){const n='BODY'===e.nodeName,r=n?window:e;r.addEventListener(t,o,{passive:!0}),n||attachToScrollParents(getScrollParent(r.parentNode),t,o,i),i.push(r)}function setupEventListeners(e,t,o,i){o.updateBound=i,window.addEventListener('resize',o.updateBound,{passive:!0});const n=getScrollParent(e);return attachToScrollParents(n,'scroll',o.updateBound,o.scrollParents),o.scrollElement=n,o.eventsEnabled=!0,o}function enableEventListeners(){this.state.eventsEnabled||(this.state=setupEventListeners(this.reference,this.options,this.state,this.scheduleUpdate))}function removeEventListeners(e,t){return window.removeEventListener('resize',t.updateBound),t.scrollParents.forEach((e)=>{e.removeEventListener('scroll',t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t}function disableEventListeners(){this.state.eventsEnabled&&(window.cancelAnimationFrame(this.scheduleUpdate),this.state=removeEventListeners(this.reference,this.state))}function isNumeric(e){return''!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function setStyles(e,t){Object.keys(t).forEach((o)=>{let i='';-1!==['width','height','top','right','bottom','left'].indexOf(o)&&isNumeric(t[o])&&(i='px'),e.style[o]=t[o]+i})}function setAttributes(e,t){Object.keys(t).forEach(function(o){const i=t[o];!1===i?e.removeAttribute(o):e.setAttribute(o,t[o])})}function applyStyle(e){return setStyles(e.instance.popper,e.styles),setAttributes(e.instance.popper,e.attributes),e.arrowElement&&Object.keys(e.arrowStyles).length&&setStyles(e.arrowElement,e.arrowStyles),e}function applyStyleOnLoad(e,t,o,i,n){const r=getReferenceOffsets(n,t,e),p=computeAutoPlacement(o.placement,r,t,e,o.modifiers.flip.boundariesElement,o.modifiers.flip.padding);return t.setAttribute('x-placement',p),setStyles(t,{position:'absolute'}),o}function computeStyle(e,t){var o=Math.floor;const{x:i,y:n}=t,{popper:r}=e.offsets,p=find(e.instance.modifiers,(e)=>'applyStyle'===e.name).gpuAcceleration;void 0!==p&&console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');const d=void 0===p?t.gpuAcceleration:p,s=getOffsetParent(e.instance.popper),a=getBoundingClientRect(s),f={position:r.position},l={left:o(r.left),top:o(r.top),bottom:o(r.bottom),right:o(r.right)},m='bottom'===i?'top':'bottom',c='right'===n?'left':'right',h=getSupportedPropertyName('transform');let u,g;if(g='bottom'==m?-a.height+l.bottom:l.top,u='right'==c?-a.width+l.right:l.left,d&&h)f[h]=`translate3d(${u}px, ${g}px, 0)`,f[m]=0,f[c]=0,f.willChange='transform';else{const e='bottom'==m?-1:1,t='right'==c?-1:1;f[m]=g*e,f[c]=u*t,f.willChange=`${m}, ${c}`}const b={"x-placement":e.placement};return e.attributes=_extends({},b,e.attributes),e.styles=_extends({},f,e.styles),e.arrowStyles=_extends({},e.offsets.arrow,e.arrowStyles),e}function isModifierRequired(e,t,o){const i=find(e,({name:e})=>e===t),n=!!i&&e.some((e)=>e.name===o&&e.enabled&&e.order<i.order);if(!n){const e=`\`${t}\``,i=`\`${o}\``;console.warn(`${i} modifier is required by ${e} modifier in order to work, be sure to include it before ${e}!`)}return n}function arrow(e,t){if(!isModifierRequired(e.instance.modifiers,'arrow','keepTogether'))return e;let o=t.element;if('string'==typeof o){if(o=e.instance.popper.querySelector(o),!o)return e;}else if(!e.instance.popper.contains(o))return console.warn('WARNING: `arrow.element` must be child of its popper element!'),e;const i=e.placement.split('-')[0],{popper:n,reference:r}=e.offsets,p=-1!==['left','right'].indexOf(i),d=p?'height':'width',s=p?'Top':'Left',a=s.toLowerCase(),f=p?'left':'top',l=p?'bottom':'right',m=getOuterSizes(o)[d];r[l]-m<n[a]&&(e.offsets.popper[a]-=n[a]-(r[l]-m)),r[a]+m>n[l]&&(e.offsets.popper[a]+=r[a]+m-n[l]);const c=r[a]+r[d]/2-m/2,h=getStyleComputedProperty(e.instance.popper,`margin${s}`).replace('px','');let u=c-getClientRect(e.offsets.popper)[a]-h;return u=Math.max(Math.min(n[d]-m,u),0),e.arrowElement=o,e.offsets.arrow={},e.offsets.arrow[a]=Math.round(u),e.offsets.arrow[f]='',e}function getOppositeVariation(e){if('end'===e)return'start';return'start'===e?'end':e}var placements=['auto-start','auto','auto-end','top-start','top','top-end','right-start','right','right-end','bottom-end','bottom','bottom-start','left-end','left','left-start'];const validPlacements=placements.slice(3);function clockwise(e,t=!1){const o=validPlacements.indexOf(e),i=validPlacements.slice(o+1).concat(validPlacements.slice(0,o));return t?i.reverse():i}const BEHAVIORS={FLIP:'flip',CLOCKWISE:'clockwise',COUNTERCLOCKWISE:'counterclockwise'};function flip(e,t){if(isModifierEnabled(e.instance.modifiers,'inner'))return e;if(e.flipped&&e.placement===e.originalPlacement)return e;const o=getBoundaries(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement);let i=e.placement.split('-')[0],n=getOppositePlacement(i),r=e.placement.split('-')[1]||'',p=[];switch(t.behavior){case BEHAVIORS.FLIP:p=[i,n];break;case BEHAVIORS.CLOCKWISE:p=clockwise(i);break;case BEHAVIORS.COUNTERCLOCKWISE:p=clockwise(i,!0);break;default:p=t.behavior;}return p.forEach((d,s)=>{if(i!==d||p.length===s+1)return e;i=e.placement.split('-')[0],n=getOppositePlacement(i);const a=e.offsets.popper,f=e.offsets.reference,l=Math.floor,m='left'===i&&l(a.right)>l(f.left)||'right'===i&&l(a.left)<l(f.right)||'top'===i&&l(a.bottom)>l(f.top)||'bottom'===i&&l(a.top)<l(f.bottom),c=l(a.left)<l(o.left),h=l(a.right)>l(o.right),u=l(a.top)<l(o.top),g=l(a.bottom)>l(o.bottom),b='left'===i&&c||'right'===i&&h||'top'===i&&u||'bottom'===i&&g,y=-1!==['top','bottom'].indexOf(i),w=!!t.flipVariations&&(y&&'start'===r&&c||y&&'end'===r&&h||!y&&'start'===r&&u||!y&&'end'===r&&g);(m||b||w)&&(e.flipped=!0,(m||b)&&(i=p[s+1]),w&&(r=getOppositeVariation(r)),e.placement=i+(r?'-'+r:''),e.offsets.popper=_extends({},e.offsets.popper,getPopperOffsets(e.instance.popper,e.offsets.reference,e.placement)),e=runModifiers(e.instance.modifiers,e,'flip'))}),e}function keepTogether(e){const{popper:t,reference:o}=e.offsets,i=e.placement.split('-')[0],n=Math.floor,r=-1!==['top','bottom'].indexOf(i),p=r?'right':'bottom',d=r?'left':'top',s=r?'width':'height';return t[p]<n(o[d])&&(e.offsets.popper[d]=n(o[d])-t[s]),t[d]>n(o[p])&&(e.offsets.popper[d]=n(o[p])),e}function toValue(e,t,o,i){var n=Math.max;const r=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),p=+r[1],d=r[2];if(!p)return e;if(0===d.indexOf('%')){let e;switch(d){case'%p':e=o;break;case'%':case'%r':default:e=i;}const n=getClientRect(e);return n[t]/100*p}if('vh'===d||'vw'===d){let e;return e='vh'===d?n(document.documentElement.clientHeight,window.innerHeight||0):n(document.documentElement.clientWidth,window.innerWidth||0),e/100*p}return p}function parseOffset(e,t,o,i){const n=[0,0],r=-1!==['right','left'].indexOf(i),p=e.split(/(\+|\-)/).map((e)=>e.trim()),d=p.indexOf(find(p,(e)=>-1!==e.search(/,|\s/)));p[d]&&-1===p[d].indexOf(',')&&console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');const s=/\s*,\s*|\s+/;let a=-1===d?[p]:[p.slice(0,d).concat([p[d].split(s)[0]]),[p[d].split(s)[1]].concat(p.slice(d+1))];return a=a.map((e,i)=>{const n=(1===i?!r:r)?'height':'width';let p=!1;return e.reduce((e,t)=>''===e[e.length-1]&&-1!==['+','-'].indexOf(t)?(e[e.length-1]=t,p=!0,e):p?(e[e.length-1]+=t,p=!1,e):e.concat(t),[]).map((e)=>toValue(e,n,t,o))}),a.forEach((e,t)=>{e.forEach((o,i)=>{isNumeric(o)&&(n[t]+=o*('-'===e[i-1]?-1:1))})}),n}function offset(e,{offset:t}){const{placement:o,offsets:{popper:i,reference:n}}=e,r=o.split('-')[0];let p;return p=isNumeric(+t)?[+t,0]:parseOffset(t,i,n,r),'left'===r?(i.top+=p[0],i.left-=p[1]):'right'===r?(i.top+=p[0],i.left+=p[1]):'top'===r?(i.left+=p[0],i.top-=p[1]):'bottom'===r&&(i.left+=p[0],i.top+=p[1]),e.popper=i,e}function preventOverflow(e,t){let o=t.boundariesElement||getOffsetParent(e.instance.popper);e.instance.reference===o&&(o=getOffsetParent(o));const i=getBoundaries(e.instance.popper,e.instance.reference,t.padding,o);t.boundaries=i;const n=t.priority;let r=e.offsets.popper;const p={primary(e){let o=r[e];return r[e]<i[e]&&!t.escapeWithReference&&(o=Math.max(r[e],i[e])),{[e]:o}},secondary(e){const o='right'===e?'left':'top';let n=r[o];return r[e]>i[e]&&!t.escapeWithReference&&(n=Math.min(r[o],i[e]-('right'===e?r.width:r.height))),{[o]:n}}};return n.forEach((e)=>{const t=-1===['left','top'].indexOf(e)?'secondary':'primary';r=_extends({},r,p[t](e))}),e.offsets.popper=r,e}function shift(e){const t=e.placement,o=t.split('-')[0],i=t.split('-')[1];if(i){const{reference:t,popper:n}=e.offsets,r=-1!==['bottom','top'].indexOf(o),p=r?'left':'top',d=r?'width':'height',s={start:{[p]:t[p]},end:{[p]:t[p]+t[d]-n[d]}};e.offsets.popper=_extends({},n,s[i])}return e}function hide(e){if(!isModifierRequired(e.instance.modifiers,'hide','preventOverflow'))return e;const t=e.offsets.reference,o=find(e.instance.modifiers,(e)=>'preventOverflow'===e.name).boundaries;if(t.bottom<o.top||t.left>o.right||t.top>o.bottom||t.right<o.left){if(!0===e.hide)return e;e.hide=!0,e.attributes['x-out-of-boundaries']=''}else{if(!1===e.hide)return e;e.hide=!1,e.attributes['x-out-of-boundaries']=!1}return e}function inner(e){const t=e.placement,o=t.split('-')[0],{popper:i,reference:n}=e.offsets,r=-1!==['left','right'].indexOf(o),p=-1===['top','left'].indexOf(o);return i[r?'left':'top']=n[o]-(p?i[r?'width':'height']:0),e.placement=getOppositePlacement(t),e.offsets.popper=getClientRect(i),e}var modifiers={shift:{order:100,enabled:!0,fn:shift},offset:{order:200,enabled:!0,fn:offset,offset:0},preventOverflow:{order:300,enabled:!0,fn:preventOverflow,priority:['left','right','top','bottom'],padding:5,boundariesElement:'scrollParent'},keepTogether:{order:400,enabled:!0,fn:keepTogether},arrow:{order:500,enabled:!0,fn:arrow,element:'[x-arrow]'},flip:{order:600,enabled:!0,fn:flip,behavior:'flip',padding:5,boundariesElement:'viewport'},inner:{order:700,enabled:!1,fn:inner},hide:{order:800,enabled:!0,fn:hide},computeStyle:{order:850,enabled:!0,fn:computeStyle,gpuAcceleration:!0,x:'bottom',y:'right'},applyStyle:{order:900,enabled:!0,fn:applyStyle,onLoad:applyStyleOnLoad,gpuAcceleration:void 0}},Defaults={placement:'bottom',eventsEnabled:!0,removeOnDestroy:!1,onCreate:()=>{},onUpdate:()=>{},modifiers};class Popper{constructor(e,t,o={}){this.scheduleUpdate=()=>requestAnimationFrame(this.update),this.update=debounce(this.update.bind(this)),this.options=_extends({},Popper.Defaults,o),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=e.jquery?e[0]:e,this.popper=t.jquery?t[0]:t,this.options.modifiers={},Object.keys(_extends({},Popper.Defaults.modifiers,o.modifiers)).forEach((e)=>{this.options.modifiers[e]=_extends({},Popper.Defaults.modifiers[e]||{},o.modifiers?o.modifiers[e]:{})}),this.modifiers=Object.keys(this.options.modifiers).map((e)=>_extends({name:e},this.options.modifiers[e])).sort((e,t)=>e.order-t.order),this.modifiers.forEach((e)=>{e.enabled&&isFunction(e.onLoad)&&e.onLoad(this.reference,this.popper,this.options,e,this.state)}),this.update();const i=this.options.eventsEnabled;i&&this.enableEventListeners(),this.state.eventsEnabled=i}update(){return update.call(this)}destroy(){return destroy.call(this)}enableEventListeners(){return enableEventListeners.call(this)}disableEventListeners(){return disableEventListeners.call(this)}}Popper.Utils=('undefined'==typeof window?global:window).PopperUtils,Popper.placements=placements,Popper.Defaults=Defaults;export default Popper;
//# sourceMappingURL=popper.min.js.map
