<?php
/**
 * Copyright © 2018 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>


<?php
/** @var \Magento\Catalog\Pricing\Render\FinalPriceBox $block */
// Daily deal Helper 
$helper=$this->helper('Smartwave\Dailydeals\Helper\Data');

$productId = $block->getSaleableItem()->getId();



/** ex: \Magento\Catalog\Pricing\Price\RegularPrice */
/** @var \Magento\Framework\Pricing\Price\PriceInterface $priceModel */
$priceModel = $block->getPriceType('regular_price');

/** ex: \Magento\Catalog\Pricing\Price\FinalPrice */
/** @var \Magento\Framework\Pricing\Price\PriceInterface $finalPriceModel */
$finalPriceModel = $block->getPriceType('final_price');
$idSuffix = $block->getIdSuffix() ? $block->getIdSuffix() : '';
$schema = ($block->getZone() == 'item_view') ? true : false;
?>
<?php if($helper->isDealProduct($productId)) : ?>
    <?php
        $deal_final_price_amount = number_format($helper->getDealproductbyId($productId),2);
    ?>
    <span class="old-price">
        <?php /* @escapeNotVerified */ echo $block->renderAmount($finalPriceModel->getAmount(), [
            'display_label'     => __('Regular Price'),
            'price_id'          => $block->getPriceId('old-price-' . $idSuffix),
            'price_type'        => 'oldPrice',
            'include_container' => true,
            'skip_adjustments'  => true
        ]); ?>
    </span>
    <span class="special-price">
        <span class="price-container price-final_price tax weee">
            <span class="price-label">Special Price</span>
            <span id="product-price-<?php echo $idSuffix; ?>" data-price-amount="<?php echo $deal_final_price_amount; ?>" data-price-type="finalPrice" class="price-wrapper ">
                <span class="price"><?php echo $helper->getcurrencySymbol().''.$deal_final_price_amount; ?></span>
            </span>
        </span>
    </span>
<?php elseif ($block->hasSpecialPrice()): ?>
    <span class="old-price">
        <?php /* @escapeNotVerified */ echo $block->renderAmount($priceModel->getAmount(), [
            'display_label'     => __('Regular Price'),
            'price_id'          => $block->getPriceId('old-price-' . $idSuffix),
            'price_type'        => 'oldPrice',
            'include_container' => true,
            'skip_adjustments'  => true
        ]); ?>
    </span>
    <span class="special-price">
        <?php /* @escapeNotVerified */ echo $block->renderAmount($finalPriceModel->getAmount(), [
            'display_label'     => __('Special Price'),
            'price_id'          => $block->getPriceId('product-price-' . $idSuffix),
            'price_type'        => 'finalPrice',
            'include_container' => true,
            'schema' => $schema
        ]); ?>
    </span>
<?php else : ?>
    <?php /* @escapeNotVerified */ echo $block->renderAmount($finalPriceModel->getAmount(), [
        'price_id'          => $block->getPriceId('product-price-' . $idSuffix),
        'price_type'        => 'finalPrice',
        'include_container' => true,
        'schema' => $schema
    ]); ?>
<?php endif; ?>

<?php if ($block->showMinimalPrice()): ?>
    <?php if ($block->getUseLinkForAsLowAs()):?>
        <a href="<?php /* @escapeNotVerified */ echo $block->getSaleableItem()->getProductUrl(); ?>" class="minimal-price-link">
            <?php /* @escapeNotVerified */ echo $block->renderAmountMinimal(); ?>
        </a>
    <?php else:?>
        <span class="minimal-price-link">
            <?php /* @escapeNotVerified */ echo $block->renderAmountMinimal(); ?>
        </span>
    <?php endif?>
<?php endif; ?>
