<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Update to your %store_name shipment" store_name=$store.getFrontendName()}} @-->
<!--@vars {
"var this.getUrl($store, 'customer/account/')":"Customer Account URL",
"var order.getCustomerName()":"Customer Name",
"var comment":"Order Comment",
"var order.increment_id":"Order Id",
"var order.getStatusLabel()":"Order Status",
"var shipment.increment_id":"Shipment Id"
} @-->
{{template config_path="design/email/header_template"}}

<table>
    <tr class="email-intro">
        <td>
            <p class="greeting">{{trans "%name," name=$order.getCustomerName()}}</p>
            <p>
                {{trans
                    "Your order #%increment_id has been updated with a status of <strong>%order_status</strong>."

                    increment_id=$order.increment_id
                    order_status=$order.getStatusLabel()
                |raw}}
                {{trans 'You can check the status of your order by <a href="%account_url">logging into your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
            </p>
            <p>
                {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>.' store_email=$store_email |raw}}
            </p>
        </td>
    </tr>
    <tr class="email-information">
        <td>
            {{depend comment}}
            <table class="message-info">
                <tr>
                    <td>
                        {{var comment|escape|nl2br}}
                    </td>
                </tr>
            </table>
            {{/depend}}
        </td>
    </tr>
</table>

{{template config_path="design/email/footer_template"}}
