<?xml version="1.0"?>
<!--
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="header.links">
            <block class="Magento\Customer\Block\Account\Customer" name="customer" template="account/customer.phtml" before="-"/>
            <block class="Magento\Customer\Block\Account\AuthorizationLink" name="authorization-link-login" template="account/link/authorization.phtml"/>
        </referenceBlock>
        <block class="Magento\Theme\Block\Html\Header" name="header" as="header">
            <arguments>
                <argument name="show_part" xsi:type="string">welcome</argument>
            </arguments>
        </block>
        <move element="header" destination="header.links" before="-"/>
        <move element="register-link" destination="header.links"/>
        <move element="top.links" destination="customer"/>
        <move element="authorization-link" destination="top.links" after="-"/>
    </body>
</page>
