<?php
/**
 * Copyright © 2015 <PERSON><PERSON> (<EMAIL>). All rights reserved.
 * See LICENSE.txt for license details (http://opensource.org/licenses/osl-3.0.php).
 *
 * Glory to Ukraine! Glory to the heroes!
 */
?>
<?php
/**
 * Blog post comments template
 *
 * @var $block \Magefan\Blog\Block\Post\View\Comments
 */
?>
<?php if ($block->getCommentsType()) { ?>
<div class="block comments">
  <?php if ($block->getCommentsType() == 'facebook') { ?>
      <div id="fb-root"></div>
      <script>(function(d, s, id) {
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s); js.id = id;
        js.src = "//connect.facebook.net/<?php echo $block->getLocaleCode() ?>/sdk.js#xfbml=1&version=v2.5&appId=<?php echo $block->getFacebookAppId() ?>";
        fjs.parentNode.insertBefore(js, fjs);
      }(document, 'script', 'facebook-jssdk'));</script>
      <div class="post-comments clear">
          <div class="fb-comments" data-href="<?php echo $block->getPost()->getPostUrl() ?>" data-width="100%" data-numposts="<?php echo $block->getNumberOfComments() ?>"></div>
      </div>
  <?php } elseif ($block->getCommentsType() == 'disqus') { ?>
        <div id="disqus_thread"></div>
        <script>
            var disqus_config = function () {
                this.page.url = "<?php echo $block->getPost()->getPostUrl() ?>";
                this.page.identifier = "<?php echo $block->getPost()->getUrl() ?>"
            };

            (function() {
                var d = document, s = d.createElement('script');
                s.src = '//<?php echo $block->getDisqusShortname() ?>.disqus.com/embed.js';
                s.setAttribute('data-timestamp', +new Date());
                (d.head || d.body).appendChild(s);
            })();
        </script>
  <?php } ?>
</div>
<?php } ?>