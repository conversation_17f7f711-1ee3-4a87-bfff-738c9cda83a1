// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .pages {
        .lib-pager();
        .page {
            width: 26px;
            border-radius: 3px;
        }
        .action {
            border-radius: 3px;
            padding: 0 8px;
            width: 26px;
        }
        .item:last-child {
            margin-left: 0;
        }
    }
}
