// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//    Common
//--------------------------------------

& when (@media-common = true) {

.block-balance {
    .balance-price-label {
        &:extend(.abs-visually-hidden all);
    }
    .price {
        .lib-font-size(32);
        line-height: 32px;
        display: inline-block;
        .lib-css(margin-top, -@indent__s);
    }
}

}

//
//    Desktop
//--------------------------------------
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .storecredit {
        .block {
            &:extend(.abs-blocks-2columns all);
        }
        .block-balance-history {
            width: 100%;
        }
    }
    .table-balance-history {
        .col {
            width: 25%;
        }
    }
    .table-wrapper.balance-history {
        &:extend(.abs-account-table-margin-desktop all);
    }
}

//
//    Mobile
//--------------------------------------
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .table-wrapper.balance-history {
        &:extend(.abs-account-table-margin-mobile all);
        &:extend(.abs-no-border-top all);
    }
}
