// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

@desktop-popup-width: 380px;
@desktop-popup-margin-left: -((@desktop-popup-width+(@popup__padding*2))/2);
@desktop-popup-position-left: 50%;
@desktop-popup-position-top: 20%;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .wishlist {
        //  Wish list split button
        &.split.button {
            .lib-dropdown-split(
            @_options-selector: ~".items",
            @_dropdown-split-button-border-radius-fix: false,
            @_dropdown-split-list-item-padding: 5px 5px 5px 23px,
            @_dropdown-split-toggle-icon-content: @icon-down,
            @_dropdown-split-toggle-active-icon-content: @icon-up,
            @_icon-font-size: 12px,
            @_icon-font-color: @addto-color,
            @_icon-font-color-hover: @addto-hover-color,
            @_icon-font-vertical-align: middle,
            @_dropdown-split-list-pointer-position: left,
            @_dropdown-split-list-min-width: 200px
            );
            margin-left: 5%;
            vertical-align: middle;
            .items {
                padding: 6px 0;
            }
            &:extend(.abs-dropdown-items-new all);

            > .action {
                &:extend(.abs-action-button-as-link all);
                &.split {
                    .lib-icon-font-symbol(
                    @_icon-font-content: @icon-wishlist-full
                    );
                    &:extend(.abs-actions-addto all);
                    margin-left: 5px;
                    &:before {
                        margin: 0;
                    }
                }
                &:active,
                &:focus,
                &:hover {
                    .lib-css(color, @addto-hover-color);
                }
            }
            > .action.toggle {
                &:before {
                    border-right: 1px solid @addto-color;
                    content: '';
                    float: right;
                    height: 15px;
                    margin: 3px 0 0 4px;
                }
            }
            .table-comparison &,
            .product-items & {
                > .action {
                    &.split {
                        span {
                            &:extend(.abs-visually-hidden all);
                        }
                    }
                }
            }
            .product-info-main &,
            .block-bundle-summary & {
                > .action {
                    &.split {
                    }
                }
            }
            .cart.table-wrapper & {
                vertical-align: baseline;
                > .action {
                    &.split {
                        font-weight: normal;
                        letter-spacing: normal;
                        text-transform: none;
                    }
                    &.toggle {
                        &:after {
                            .lib-css(color, @link__color);
                        }
                    }
                    &.split,
                    &.toggle {
                        .lib-css(color, @link__color);
                        &:before {
                            display: none;
                        }
                    }
                }
            }
        }
        //  Wish list popup
        &.window.popup {
            .lib-popup();
            &.active {
                display: block;
            }
        }
        &.overlay {
            .lib-window-overlay();
            &.active {
                display: block;
            }
        }
        //  Wish list dropdown
        &-dropdown {
            &:extend(.abs-dropdown-simple all);
            &:extend(.abs-dropdown-items-new all);
        }
        &-dropdown.move,
        &-dropdown.copy {
            .action.toggle {
                .lib-css(color, @link__color);
            }
        }
    }

    .page-multiple-wishlist {
        .page-title-wrapper {
            .page-title {
                &:extend(.abs-visually-hidden all);
            }
        }
        .actions-toolbar {
            > .primary {
                display: block;
                float: none;
                text-align: right;
                .action.remove {
                    float: left;
                }
            }
        }
    }

    .block-wishlist-management {
        position: relative;
        .wishlist {
            //  Select wish list
            &-select {
                .wishlist-name {
                    &-label {
                        font-weight: @font-weight__semibold;
                        &:extend(.abs-colon all);
                    }
                    &-current {
                    }
                }
                &-items {
                    &:extend(.abs-reset-list all);
                    &.active {
                    }
                }
            }
            &-add.item {
                .action {
                }
            }
            //  Wish list name
            &-title {
                margin-bottom: @indent__s;
                strong {
                    font-weight: @font-weight__light;
                    margin-left: @indent__s;
                }
                .action.edit {
                }
            }
            //  Wish list info
            &-info {
            }
            &-notice {
                .lib-css(color, @text__color__muted);
                display: inline-block;
                margin-left: @indent__base;
                &.private {
                    .lib-icon-font(
                    @_icon-font-content: @icon-private,
                    @_icon-font-color: lighten(@text__color__muted, 15%),
                    @_icon-font-vertical-align: text-bottom,
                    @_icon-font-margin: 0 0 0 @indent__xs
                    );
                }
            }
            //  Wish list toolbar
            &-toolbar {
                &-select,
                &-actions {
                    display: inline-block;
                }
                &-select {
                    margin-left: @indent__base;
                }
                &-actions {
                }
            }
            //  Wish list toolbar actions
            &-dropdown.move {
                margin-left: @indent__base;
            }
        }
    }

    .block-wishlist-search-form {
        .block-title {
            &:extend(.abs-account-title all);
        }
        .form-wishlist-search {
            .lib-css(margin-bottom, @indent__l*2);
            max-width: 500px;
            .fieldset {
                > .legend {
                    &:extend(.abs-visually-hidden);
                }
            }
        }
    }

    .block-wishlist-search-results {
        .block-title {
            &:extend(.abs-account-title all);
        }
        .data.table {
            &:extend(.abs-table-striped all);
        }
        .table-wrapper {
            border-top: none;
        }
    }

    .block-wishlist-info-items {
        .block-title {
            .lib-css(margin-bottom, @indent__base);
            .lib-font-size(22px);
            > strong {
                font-weight: @font-weight__light ;
            }
        }
        .product-item {
            width: 100%;
        }
        .product-item-photo {
            margin-right: @indent__base;
        }
        .product-item-info {
            width: auto;
            &:hover {
                .split {
                    visibility: visible;
                }
            }
        }
        .price-wrapper {
            display: block;
            margin: 0 0 @indent__s;
        }
        .split {
            clear: both;
            padding: @indent__s 0;
            visibility: hidden;
        }
        .wishlist-dropdown {
            .change {
                .lib-css(color, @link__color);
            }
        }
        .action {
            &.towishlist {
                padding: @indent__s 0;
            }
        }
        .input-text {
            &.qty {
                &:extend(.abs-input-qty all);
                margin-left: 10px;
            }
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .block-wishlist-management {
        clear: both;
        margin-top: -21px;
        .wishlist {
            //  Select wish list
            &-select {
                margin: 0 -@layout__width-xs-indent 20px;
                .wishlist-name {
                    .lib-font-size(16);
                    &:extend(.abs-toggling-title-mobile all);
                    &-label {
                    }
                    &-current {
                    }
                }
                &-items {
                    border-bottom: @border-width__base solid @border-color__base;
                    .lib-css(background, @collapsible-nav-background);
                    display: none;
                    padding: 15px 0;
                    &.active {
                        display: block;
                    }
                    .item {
                        margin: 3px 0 0;
                        &:first-child {
                            margin-top: 0;
                        }
                        &.current {
                            display: none;
                        }
                        a {
                            .lib-css(color, @text__color);
                            display: block;
                            padding: @indent__xs 18px;
                            text-decoration: none;
                            &:hover {
                                .lib-css(background, @collapsible-nav-item-hover);
                            }
                        }
                    }
                }
            }
            &-add.item {
                .add {
                    &:extend(.abs-icon-add-mobile all);
                }
            }
            //  Wish list name
            &-title {
                strong {
                    .lib-font-size(26);
                }
                .action.edit {
                }
            }
            //  Wish list info
            &-info {
                margin-bottom: 15px;
            }
            &-notice {
                &.private {
                }
            }
            //  Wish list toolbar
            &-toolbar {
                &-select,
                &-actions {
                    margin-bottom: 15px;
                }
                &-select {
                }
                &-actions {
                }
            }
            //  Wish list toolbar actions
            &-dropdown.move {
            }
        }
    }
    .products-grid.wishlist {
        .product {
            &-item {
                &-checkbox {
                    right: 20px;
                    position: absolute;
                    top: 20px;
                }
                &-photo {
                    .page-multiple-wishlist & {
                        margin-right: @indent__m;
                    }
                }
                &-name,
                &-description,
                .price-box,
                &-tooltip {
                    .page-multiple-wishlist & {
                        margin-right: 115px;
                    }
                }
            }
        }
        .wishlist-dropdown {
            display: none;
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .wishlist {
        &.window.popup {
            bottom: auto;
            .lib-css(top, @desktop-popup-position-top);
            .lib-css(right, @desktop-popup-position-left);
            .lib-css(margin-right, @desktop-popup-margin-left);
            .lib-css(width, @desktop-popup-width);
            right: auto;
        }
    }

    .block-wishlist-management {
        &:extend(.abs-add-clearfix-desktop all);
        margin-bottom: @indent__base;
        .wishlist {
            //  Select wish list
            &-select {
                border-bottom: 1px solid @secondary__color;
                display: table;
                margin-bottom: 15px;
                width: 100%;
                .wishlist-name {
                    display: table-cell;
                    margin-left: @indent__s;
                    padding: @indent__xs @indent__s @indent__s;
                    vertical-align: top;
                    white-space: nowrap;
                    width: 5%;
                    span {
                    }
                    &.active {
                    }
                    &-label {
                    }
                    &-current {
                    }
                }
                &-items {
                    display: table-cell;
                    padding-left: 160px;
                    vertical-align: top;
                    &.active {
                    }
                    .item {
                        display: inline-block;
                        margin-left: @indent__s;
                        padding: @indent__xs @indent__s @indent__s;
                        &:last-child {
                            margin-left: 0;
                        }
                    }
                    .current {
                        border-bottom: 3px solid @active__color;
                        font-weight: @font-weight__semibold;
                    }
                }
                .wishlist-name-current {
                    display: none;
                }
            }
            &-add.item {
                position: absolute;
                left: 0;
                top: 0;
            }
            //  Wish list name
            &-title {
                strong {
                    .lib-font-size(40);
                }
                .action.edit {
                }
            }
            //  Wish list info
            &-info {
                float: right;
            }
            &-notice {
                &.private {
                }
            }
            //  Wish list toolbar
            &-toolbar {
                float: left;
                &-select,
                &-actions {
                }
                &-select {
                }
                &-actions {
                }
            }
            //  Wish list toolbar actions
            &-dropdown.move {
            }
        }
    }

    .block-wishlist-info-items {
        .product-item-photo {
            margin-right: 0;
        }
    }

    .products-grid.wishlist {
        .product {
            &-item {
                &-checkbox {
                    float: right;
                    + .product-item-name {
                        margin-right: @indent__m;
                    }
                }
            }
        }
    }

    .toolbar-wishlist-results {
        &:extend(.abs-pager-toolbar all);
    }
}
