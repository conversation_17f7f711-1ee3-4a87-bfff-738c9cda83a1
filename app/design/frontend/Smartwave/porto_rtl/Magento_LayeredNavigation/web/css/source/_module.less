// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//    Common
//  _____________________________________________

& when (@media-common = true) {
.filter {
    &.block {
        margin-bottom: 0;
    }
    &-title {
        strong {
            position: absolute;
            z-index: 2;
            line-height: 16px;
            border: 1px solid @border-color__base;
            text-align: center;
            padding: 7px 10px;
            @_shadow: inset 0 1px 0 0 @color-white, inset 0 -1px 0 0 fade(@border-color__base, 30);
            .lib-css(background-color, @toolbar-element-background);
            .lib-css(box-shadow, @_shadow);
            border-radius: 3px;
            font-weight: 400;
            top: 0;
            right: 0;
            &[data-count]:after {
                content: attr(data-count);
                display: inline-block;
                border-radius: 2px;
                padding: 2px;
                font-size: .8em;
                margin: 0 5px;
                .lib-css(color, @color-white);
                min-width: 1em;
                line-height: 1;
                background: @color-orange-red4;
            }
            &.disabled {
                opacity: .5;
            }
        }
    }
    .block-subtitle {
        font-weight: @font-weight__semibold;
        padding: @indent__s @indent__s @indent__base;
        font-size: @font-size__base;
        line-height: 1em;
        border-bottom: @border-width__base solid @border-color__base;
        display: none;
    }
    &-subtitle {
            display: none;
    }
    &-current {
        margin: 0;
        .items {
            border: 1px solid #ddd;
            border-width: 0 1px;
            background-color: #fbfbfb;
            padding: 10px 0;
            margin: 0
        }
        .item {
            position: relative;
            z-index: 1;
            padding-right: 17px;
        }
        .filter &-subtitle {
            display: block;
            padding: 10px 15px;
            font-size: 13px;
            font-weight: bold;
            text-transform: uppercase;
            border: 1px solid #ddd;
            background-color: #f5f5f5;
            color: #777;
            position: relative;
            cursor: pointer;
            border-radius: 7px 7px 0 0;
        }
        .action.remove {
            &:extend(.abs-remove-button-for-blocks all);
            position: absolute;
            left: 15px;
            top: -2px;
        }
    }
    &-actions {
        font-size: 11px;
        padding: 10px 15px;
        background: #f7f7f7;
        border: 1px solid #ddd;
        border-radius: 0 0 7px 7px;
        text-align: right;
        margin-bottom: 14px;
    }
    &-label {
        text-transform: uppercase;
        
        &:after {
            content: ": ";
        }
    }
    &-value {
        .lib-css(color, @filter-quantity);
    }
    &-options {
        display: none;
        margin: 0;
        &-item {
            margin-bottom: 14px;
            
            &.active {
                .filter-options-title {
                    border-radius: 7px 7px 0 0;
                }
                .filter-options-content {
                    display: block !important;
                }
            }
        }
        &-title {
            padding: 10px 15px;
            font-size: 13px;
            font-weight: bold;
            text-transform: uppercase;
            border: 1px solid #ddd;
            background-color: #f5f5f5;
            position: relative;
            cursor: pointer;
            border-radius: 7px;

            .lib-icon-font(
                @_icon-font-content: @icon-down-dir,
                @_icon-font-size: 13px,
                @_icon-font-position: after,
                @_icon-font-display: block
            );
            &:after {
                width: 26px;
                height: 26px;
                display: block;
                border: 1px solid #ddd;
                position: absolute;
                left: 15px;
                top: 50%;
                margin-top: -13px;
                border-radius: 3px;
                color: #ccc;
                text-align: center;
                line-height: 24px;
                background-color: #fff;
                font-size: 10px;
            }
            &:hover {
                &:after {
                    background-color: @theme-color;
                    color: #fff;
                }
            }
            .active > & {
                .lib-icon-font-symbol(
                    @_icon-font-content: @icon-up-dir,
                    @_icon-font-position: after
                );
            }
        }
        &-content {
            margin: 0;
            padding: 10px 15px;
            font-size: 13px;
            border: 1px solid #ddd;
            border-top: 0;
            border-radius: 0 0 7px 7px;
            background-color: #fbfbfb;
            
            .item {
                margin: 3px 0;
                line-height: 1.5em;
            }
            a {
                color: @base-font-color !important;
                &:hover {
                    .count {
                        text-decoration: none;
                    }
                }
            }
            .count {
                .lib-css(color, @filter-quantity);
                padding-left: 5px;
                &:before {
                    content: "(";
                }
                &:after {
                    content: ")";
                }
            }
            .filter-count-label {
                &:extend(.abs-visually-hidden all);
            }
        }
    }
}

}

//
//    Mobile
//--------------------------------------

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    body.filter-active {
        .page-wrapper {
            visibility: hidden;
            height: 0;
            margin-top: -999999em;
        }
        .columns {
            z-index: 999;
        }
    }

    .filter {
        &.active {
            position: relative;
            z-index: 99;
            visibility: visible;
            .filter-options-item:last-child {
                margin-bottom: @indent__xl;
            }
            .filter-title {
                position: fixed;
                top: 0;
                right: 0;
                left: 0;
                height: 48px;
                z-index: 2;
                border-bottom: 1px solid @border-color__base;
                strong {
                        left: 3px;
                        right: auto;
                        top: 10px;
                        border: 0;
                        background: none;
                        .lib-css(box-shadow, none);
                        color: transparent;
                        .lib-icon-font(
                            @icon-remove,
                            @_icon-font-color: @text__color__muted,
                            @_icon-font-size: 16px,
                            @_icon-font-position: after
                            );
                }
            }
            .filter-subtitle {
                display: block;
                position: fixed;
                top: 0;
                right: 0;
                left: 0;
                height: 50px;
                .lib-css(background, @toolbar-background);
                z-index: 1;
                line-height: 32px;
            }
            .filter-options {
                display: block;
                position: fixed;
                top: 50px;
                left: 0;
                right: 0;
                bottom: 0;
                overflow: scroll;
                .lib-css(background, @color-white);
                z-index: 10;
            }
        }
    }

    .filter {
        & &-subtitle {
            font-size: 20px;
            font-weight: @font-weight__light;
        }
        &-actions {
            margin: -35px -@indent__s @indent__m;
        }
        &-options-content {
            padding: @indent__xs @indent__s;
        }
    }
    .filter-no-options {
        .filter-title:before {
            content: "";
            width: 100px;
            height: 40px;
            background: rgba(255,255,255,.5);
            display: block;
            position: relative;
            z-index: 99;
            margin-top: -40px;
            right: -15px;
        }
    }
    .filter .filter-current {
        margin: @indent__xs -@indent__s 0;
        border: solid @border-color__base;
        border-width: 1px 0;

        .items {
            display: none;
        }
        &-subtitle {
            text-transform: uppercase;
            position: relative;
            z-index: 1;
            .lib-icon-font(
                @_icon-font-content: @icon-down,
                @_icon-font-size: 13px,
                @_icon-font-position: before,
                @_icon-font-display: block
            );
            &:before {
                position: absolute;
                left: 10px;
                top: 10px;
            }

            &:after {
                content:' (' attr(data-count) ')';
                font-size:.9em;
                .lib-css(color, @text__color__muted);
            }
        }
        &.active {
            padding-bottom: 30px;
            .block-subtitle {
                .lib-icon-font-symbol(
                    @_icon-font-content: @icon-up,
                    @_icon-font-position: before
                );
            }
            .items {
                display: block;
            }
            & + .block-actions {
                display: block;
            }
        }
        & + .block-actions {
            display: none;
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {

    .filter {
        &.block {
            margin-bottom: @indent__xl;
        }
        &-title {
            display: none;
        }
        &-content {
            .item {
                margin: @indent__s 0;
            }
        }
        &.active &-options,
        &-options {
            clear: both;
            position: static;
            overflow: initial;
            display: block;
            background: transparent;
        }
        &-subtitle {
            position: static;
            display: block;
        }
    }
    .page-layout-1column {
        .toolbar-products {
            position: absolute;
            top: 0;
            width: 100%;
        }
        .products ~ .toolbar-products {
            position: static;
        }
        &.page-with-filter .column.main {
            position: relative;
            z-index: 1;
            padding-top: 45px;
        }
        .filter {
            &.block {
                border-top: 1px solid @border-color__base;
            }
            &-content {
                margin-top: @indent__s;
            }
            &-subtitle {
                display: none;
            }
            &-options {
                &-item {
                    display: inline-block;
                    border: 0;
                    margin-left: @indent__m;
                    &.active {
                        position: relative;
                        z-index: 2;
                        &:hover {
                            z-index: 3;
                        }
                        &:after,
                        &:before {
                            content: "";
                            display: block;
                            .lib-arrow(up, 8px, @color-black);
                            position: absolute;
                            right: 50%;
                            bottom: -1px;
                            z-index: 3;
                            margin-right: -20px;
                        }
                        &:after {
                            .lib-css(border-bottom-color, @color-white);
                            margin-top:2px;
                            z-index: 4;
                        }
                    }
                }
                &-title {
                    padding: 0 0 0 20px;
                    &:after {
                        left: 2px;
                        top: 3px;
                        z-index: 3;
                    }
                }
                &-content {
                    position: absolute;
                    z-index: 2;
                    .lib-css(background, @color-white);
                    width: 180px;
                    top: 100%;
                    border: 1px solid @border-color__base;
                    padding: @indent__xs 0;
                    @_shadow:  0 3px 5px 0 rgba(50, 50, 50, .75);
                    .lib-css(box-shadow, @_shadow, 1);
                    .item {
                        padding: @indent__xs;
                        margin:0;
                        a {
                            margin-right: 0;
                        }
                        &:hover {
                            background-color: @color-gray91;
                        }
                    }
                }
            }
            &-current {
                display: inline;
                line-height: 35px;
                &-subtitle {
                    display: inline;
                    padding: 0;
                    .lib-css(color, @text__color__muted);
                    font-size: @font-size__base;
                    font-weight: normal;
                    &:after {
                        content: ":";
                    }
                }
                .item,
                .items {
                    display: inline;
                }
                .item {
                    margin-left: @indent__m;
                }
                .action.remove {
                    line-height: normal;
                }
            }
            &-actions {
                display: inline;
                & ~ .filter-options {
                    margin-top: @indent__m;
                }
            }
        }
    }
}
