// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

@mobile-cart-padding: 15px;
@cart-price-color: @color-gray40;
@cart-item-cell-padding-top: 20px;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //
    //  Shopping cart
    //  ---------------------------------------------

    .checkout-cart-index {
        .page-main {
            padding-left: 0;
            padding-right: 0;
        }
        .page-title-wrapper {
            padding-left: @layout__width-xs-indent;
            padding-right: @layout__width-xs-indent;
        }
    }

    //  Cart container
    .cart-container {
        .form-cart {
            &:extend(.abs-shopping-cart-items all);
        }
    }

    //  Summary block
    .cart-summary {
        &:extend(.abs-add-box-sizing all);
        .lib-css(background, @sidebar__background-color);
        margin-bottom: @indent__m;
        padding: 1px 0 @indent__m;
        background: #fbfbfb;
        border: 1px solid #ddd;
        border-radius: 8px;
        > .title {
            .lib-font-size(24);
            display: none;
            font-weight: @font-weight__light;
            margin: 12px 0;
        }

        .block {
            form:not(:last-of-type) {
                .fieldset {
                    margin: 0 0 @indent__m;
                }
            }

            .price {
                font-weight: @font-weight__bold;
            }

            .field {
                margin: 0 0 16px;
                &.note {
                    display: none;
                }
            }

            .actions-toolbar {
                > .primary {
                    text-align: right;
                    .action.primary {
                        &:extend(.abs-revert-to-action-secondary all);
                        width: auto;
                    }
                }
            }

            .fieldset.estimate {
                > .legend,
                > .legend + br {
                    &:extend(.abs-no-display all);
                }
            }
            &:extend(.abs-cart-block all);
            .title {
                strong {
                    .lib-font-size(14);
                    font-weight: @font-weight__semibold;
                }
            }
            .item-options {
                margin: 0 0 16px;
                .field {
                    .radio {
                        float: right;
                        margin-left: 8px;
                    }
                    .radio {
                        + .label {
                            display: block;
                            margin: 0;
                            overflow: hidden;
                            font-weight: 400;
                        }
                    }
                }
            }
        }

        .page-main & {
            .block {
                margin-bottom: 0;
            }
        }

        .checkout-methods-items {
            &:extend(.abs-reset-list all);
            margin: @indent__base 0 0;
            padding: 0 @mobile-cart-padding;
            text-align: center;
            .action.primary.checkout {
                &:extend(.abs-button-l all);
                width: 100%;
            }
            .item {
                margin-bottom: @indent__m;
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        .message {
            padding-right: @indent__base;
            > *:first-child:before {
                display: none;
            }
        }
        &:extend(.abs-adjustment-incl-excl-tax all);
    }

    //  Totals block
    .cart-totals {
        &:extend(.abs-sidebar-totals all);
        tbody,
        tfoot {
            .mark {
                text-align: right;
            }
        }
    }

    //  Products table
    .cart {
        &.table-wrapper {
            .product-item-name > a {
                font-size: 14px;
                &, &:hover {
                    color: @_link-color;
                }
            }
            .product-image-container {
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 3px;
            }
            .cart {
                thead {
                    tr th.col {
                        border-bottom: @border-width__base solid @border-color__base;
                        padding-bottom: 12px;
                        padding-top: 24px;
                        font-size: 14px;
                        font-weight: 600;
                    }
                }
                tbody {
                    td {
                        border: 0;
                    }
                }
                > .item {
                    border-bottom: @border-width__base solid @border-color__base;
                    position: relative;
                }
            }
            .col {
                vertical-align: middle;
                &.price,
                &.subtotal,
                &.msrp {
                    font-size: 14px;
                    text-align: center;
                    &:extend(.abs-incl-excl-tax all);
                }
                &.subtotal .price {
                    color: @theme-color;
                }
                &.qty {
                    text-align: center;
                    .label {
                        &:extend(.abs-visually-hidden all);
                    }
                    .input-text {
                        text-align: center;
                        width: 45px;
                    }
                    .field.qty {
                        min-width: 67px;
                    }
                }
                > .price {
                    .lib-css(color, @primary__color__lighter);
                    .lib-font-size(18);
                    font-weight: @font-weight__bold;
                }
            }

            .item-actions {
                td {
                    padding-bottom: 0;
                    padding-left: @mobile-cart-padding;
                    padding-right: @mobile-cart-padding;
                    white-space: normal;
                }
            }
            .item {
                .col.item {
                    display: block;
                    min-height: 75px;
                    padding: 15px 90px @indent__s @mobile-cart-padding;
                    position: relative;
                }
            }

            .actions-toolbar {
                &:extend(.abs-add-clearfix all);
                min-height: 20px;
                padding-bottom: 30px;
                position: relative;
                > .action-edit,
                > .action-delete {
                    position: absolute;
                    left: 16px;
                    top: 0;
                    .lib-icon-font(
                    @icon-edit,
                    @_icon-font-size: 18px,
                    @_icon-font-line-height: 20px,
                    @_icon-font-text-hide: true,
                    @_icon-font-color: @minicart-icons-color,
                    @_icon-font-color-hover: @primary__color,
                    @_icon-font-color-active: @minicart-icons-color
                    );
                }
                > .action-delete {
                    &:extend(.abs-action-button-as-link all);
                    left: 0;
                    .lib-icon-font-symbol(
                    @_icon-font-content: @icon-trash
                    );
                }
            }
            .action {
                margin-left: 15px;
                &:last-child {
                    margin-left: 0;
                }
                &.help.map {
                    &:extend(.abs-action-button-as-link all);
                    font-weight: @font-weight__regular;
                }
            }

            .product {
                &-item-photo {
                    display: block;
                    right: @mobile-cart-padding;
                    max-width: 65px;
                    padding: 0;
                    position: absolute;
                    top: 15px;
                    width: 100%;
                    border: 0;
                    border-radius: 0;
                }
                &-item-name {
                    .lib-font-size(18);
                    display: block;
                    margin: 0;
                    margin-top: 10px;
                }
            }
            .gift-registry-name-label {
                &:after {
                    content: ':';
                }
            }

            //  Product options
            .item-options {
                margin-bottom: 0;
                &:extend(.abs-product-options-list all);
                &:extend(.abs-add-clearfix all);
            }

            .product-item-name + .item-options {
                margin-top: @indent__base;
            }

            .cart-tax-total {
                &:extend(.abs-tax-total all);
                &-expanded {
                    &:extend(.abs-tax-total-expanded all);
                }
            }
            .product-image-wrapper {
                &:extend(.abs-reset-image-wrapper all);
            }
            .action.configure {
                display: inline-block;
                margin: @indent__s 0 0;
            }
            .item .message {
                margin-top: @indent__base;
            }
        }
    }

    //  Discount
    .cart-discount {
        border-bottom: @border-width__base solid @border-color__base;
        clear: right;
        &:extend(.abs-discount-block all);
    }

    //  Empty cart
    .cart-empty {
        padding-left: @layout__width-xs-indent;
        padding-right: @layout__width-xs-indent;
    }

    .cart-tax-info + .cart-tax-total {
        display: block;
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .cart {
        .table.items {
            .col.item,
            .item-actions td {
                &:extend(.abs-col-no-prefix all);
            }
            .col.qty {
                text-align: center;
            }
            tbody > tr > td:last-child {
                &:extend(.abs-no-border-bottom-top all);
            }
        }
    }
    .cart-totals {
        .totals {
            &:extend(.abs-sidebar-totals-mobile all);
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    //  Cart container
    .cart-container {
        .form-cart {
            .actions.main {
                text-align: center;
            }
        }
    }

    .cart-discount {
        border-bottom: @border-width__base solid @border-color__base;
    }

    .cart {
        &.table-wrapper {
            border-top: @border-width__base solid @border-color__base;
            thead {
                .col {
                    &.item,
                    &.qty,
                    &.price,
                    &.subtotal,
                    &.msrp {
                        display: none;
                    }
                }
            }
            .col {
                &.qty,
                &.price,
                &.subtotal,
                &.msrp {
                    box-sizing: border-box;
                    display: block;
                    float: right;
                    white-space: nowrap;
                    width: 33%;
                    &:before {
                        content: attr(data-th);
                        display: block;
                        font-weight: @font-weight__semibold;
                        padding-bottom: 10px;
                    }
                }
                &.msrp {
                    white-space: normal;
                }
            }
            .item .col.item {
                padding-bottom: 0;
            }
            tbody > tr > td:last-child {
                border: 0;
            }
        }
    }

    .cart-totals {
        padding-left: @mobile-cart-padding;
        padding-right: @mobile-cart-padding;
        .table-wrapper {
            border-top: 0;
        }
        .totals {
            tbody > tr:not(:last-child) > td:last-child {
                border: 0;
            }
        }
    }
}

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    //  Cross sell
    .block {
        &.crosssell {
            .lib-css(padding, 0 @mobile-cart-padding);
            margin-top: 70px;
            .product-item-info {
                width: 200px;
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .checkout-cart-index {
        .page-main {
            padding-left: @layout__width-xs-indent;
            padding-right: @layout__width-xs-indent;
        }
        .page-title-wrapper {
            &:extend(.abs-revert-side-paddings all);
        }
    }

    //  Cart container
    .cart-container {
        &:extend(.abs-add-clearfix-desktop all);
        .form-cart {
            &:extend(.abs-shopping-cart-items-desktop all);
            .actions.main {
                text-align: left;
            }
        }
        .widget {
            float: right;
        }
    }

    //  Summary block
    .cart-summary {
        .lib-layout-column(2, 2, @layout-column-checkout__width-left);
        float: left;
        padding: 1px @indent__base @indent__m;
        
        > .title {
            display: block;
        }
        .fieldset {
            .actions-toolbar {
                margin-right: 0;
                > .secondary {
                    float: none;
                }
            }
        }
        .block {
            > .title {
                padding-right: 0;
                &:after {
                    left: 3px;
                }
            }
            .content {
                &:extend(.abs-revert-side-paddings all);
            }
            .fieldset {
                .field {
                    .lib-form-field-type-revert(@_type: block);
                    margin: 0 0 @indent__s;
                }
            }
        }
        .checkout-methods-items {
            padding: 0;
        }
    }

    //  Products table
    .cart {
        &.table-wrapper {
            .items { // Google Chrome version 44.0.2403.107 m fix
                min-width: 100%;
                width: auto;
            }
            tbody td {
                padding-top: @cart-item-cell-padding-top;
            }

            .item {
                .col.item {
                    padding: @cart-item-cell-padding-top 8px @indent__base 0;
                }
            }

            .item-actions td {
                padding: 0;
            }

            .product {
                &-item-photo {
                    display: table-cell;
                    max-width: 100%;
                    padding-left: @indent__base;
                    position: static;
                    vertical-align: middle;
                    width: 1%;
                }
                &-item-details {
                    
                }
                &-item-details {
                    display: table-cell;
                    vertical-align: middle;
                    white-space: normal;
                    width: 99%;
                }
            }
        }
    }

    //  Discount
    .cart-discount {
        &:extend(.abs-discount-block-desktop all);
        .lib-layout-column(2, 1, @layout-column-checkout__width-main);
        float: right;
        border: 0;
        box-sizing: border-box;
        padding-left: 4%;
    }

    //  Empty cart
    .cart-empty {
        &:extend(.abs-revert-side-paddings all);
    }

    //  Cross sell
    .block {
        &.crosssell {
            .lib-layout-column(2, 1, @layout-column-checkout__width-main);
            &:extend(.abs-add-box-sizing-desktop all);
            float: right;
            padding: 0 0 0 4%;
            .products-grid .product-item {
                width: 100%/4;
            }
        }
    }
}
