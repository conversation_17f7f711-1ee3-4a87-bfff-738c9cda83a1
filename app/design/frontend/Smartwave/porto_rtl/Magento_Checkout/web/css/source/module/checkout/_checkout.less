// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@checkout-wrapper__margin: @indent__base;
@checkout-wrapper__columns: 16;

@checkout-step-title__border: @border-width__base solid @color-gray80;
@checkout-step-title__font-size: 22px;
@checkout-step-title__font-weight: @font-weight__light;
@checkout-step-title__padding: @indent__s;

@checkout-step-title-mobile__font-size: 18px;

@checkout-step-content-mobile__background: @color-gray-light01;
@checkout-step-content-mobile__margin-s: 15px;
@checkout-step-content-mobile__margin: @indent__base -(@checkout-step-content-mobile__margin-s) @checkout-step-content-mobile__margin-s;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    .checkout-index-index,
    .checkout-onepage-success {
        .page-title-wrapper {
            &:extend(.abs-visually-hidden all);
        }
    }

    .checkout-container {
        &:extend(.abs-add-clearfix all);
        .lib-css(margin, 0 0 @checkout-wrapper__margin);
    }

    .opc-wrapper {
        .lib-css(margin, 0 0 @checkout-wrapper__margin);

        .opc {
            &:extend(.abs-reset-list all);
        }

        .step-title {
            &:extend(.abs-checkout-title all);
            .lib-css(border-bottom, @checkout-step-title__border);
        }

        .step-content {
            margin: 0 0 @indent__xl;
        }
    }

    .checkout-index-index {
        .nav-sections,
        .nav-toggle,
        .header.panel {
            
        }
        .logo {
            margin-right: 0;
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .opc-wrapper {
        .step-title {
            .lib-css(font-size, @checkout-step-title-mobile__font-size);
            border-bottom: 0;
            padding-bottom: 0;
        }
        .step-content {
            .lib-css(margin, 0 0 @checkout-step-content-mobile__margin-s);
        }
    }
}
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .checkout-index-index {
        .page-header .minicart-wrapper {
            display: none;
        }
    }
    .page-header.type5 .block-search .label {
        margin-left: 0;
    }
}
//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .opc-wrapper {
       &:extend(.abs-add-box-sizing-desktop-m all);
        .lib-layout-column(2, 1, @checkout-wrapper__columns);
        float: right;
        width: 66%;
        padding-left: @indent__l;
    }
}
