// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@checkout-modal-popup__width: 800px;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .checkout-index-index {
        .modal-popup {
            .field-tooltip {
                .field-tooltip-content {
                    &:extend(.abs-checkout-tooltip-content-position-top all);
                }
            }
            .fieldset {
                .field {
                    .label {
                        font-weight: @font-weight__regular;
                    }
                }
            }
            .modal-footer {
                .action-hide-popup {
                    &:extend(.abs-action-button-as-link all);
                    margin-top: 3px;
                }
            }
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .checkout-index-index {
        .modal-popup {
            .modal-footer {
                .action-save-address {
                    width: 100%;
                }
                .action-hide-popup {
                    margin-top: @indent__base;
                }
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .checkout-index-index {
        .modal-popup {
            .form-shipping-address {
                .lib-css(max-width, @checkout-shipping-address__max-width);
            }
            .modal-footer {
                .action-save-address {
                    float: left;
                    margin: 0 @indent__base 0 0;
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .checkout-index-index {
        .modal-popup {
            .modal-inner-wrap {
                .lib-css(margin-right, -(@checkout-modal-popup__width/2));
                .lib-css(width, @checkout-modal-popup__width);
                right: 50%;
            }
        }
    }
}
