<?php
/** @var \Magezon\Core\Helper\Data $coreHelper */
$coreHelper       = $this->helper('\Magezon\Core\Helper\Data');
$elements         = $this->getELements();
$count            = count($elements);
$element          = $this->getElement();
$title            = $coreHelper->filter($element->getData('title'));
$titleAlign       = $element->getData('title_align');
$titleTag         = $element->getData('title_tag') ? $element->getData('title_tag') : 'h2';
$description      = $coreHelper->filter($element->getData('description'));
$showLine         = $element->getData('show_line');
$activeSections   = array_filter(array_map( function($item) { return (int)$item;}, explode(',', $element->getData('active_sections') ?? '')));
$hideEmptySection = $element->getData('hide_empty_section');
$collapsibleAll   = $element->getData('collapsible_all') ? 'true' : 'false';
$atLeastOneOpen   = $element->getData('at_least_one_open') ? 'true' : 'false';
$activeSections = count($activeSections) ? $activeSections : ($atLeastOneOpen === "true" ? [1] : []);
$icon             = $element->getData('icon');
$iconPosition     = $element->getData('icon_position') ? $element->getData('icon_position') : 'left';
$activeIcon       = $element->getData('active_icon');
$accordionIcon    = $element->getData('accordion_icon');
if ($accordionIcon!='custom') {
	$icon = $activeIcon = '';
}
$noFillContentArea = $element->getData('no_fill_content_area');
?>
<?php if ($count) { ?>
	<div class="mgz-block">
        <?php if ($title || $description) { ?>
        <div class="mgz-block-heading mgz-block-heading-align-<?= $titleAlign ?><?= $showLine ? ' mgz-block-heading-line' : '' ?>">
            <?php if ($title) { ?>
                <<?= $titleTag ?> class="title"><?= $title ?></<?= $titleTag ?>>
            <?php } ?>
            <?php if ($description) { ?>
                <div class="info"><?= $description ?></div>
            <?php } ?>
        </div>
        <?php } ?>
        <div class="mgz-block-content">
            <?php
            $tabs = [];
            foreach ($elements as $index => $_element) {
                $_html = $_element->toHtml();
                if (!$hideEmptySection || ($hideEmptySection && substr_count($_html, 'mgz-element-inner') > 1)) {
                    $tabs[] = [
                        'element' => $_element,
                        'html'    => $_html
                    ];
                }
            }
            ?>
            <div class="mgz-panels mgz-panels-<?= $element->getId() ?> <?= $noFillContentArea ? 'mgz-panels-no-fill-content' : '' ?>"
                 x-data="{
                    selected: '<?= implode(",",$activeSections) ?>'.split(',') , oneOpen: <?= $atLeastOneOpen ?>, collapseAll: <?= $collapsibleAll ?>,
                    clicked: false,
                    click: function(index){
                        if(!this.includes(index)){ //open
                            if(this.collapseAll){ this.selected = []; this.selected.push(index); }
                            else { this.selected.push(index); }
                        } else { //close
                            if(this.selected.length === 1 && this.oneOpen) return;
                            this.selected.splice(this.selected.indexOf(index),1);
                        }
                        this.clicked = true;
                    },
                    includes: function(index){ return this.selected.includes(index); }
                }"
            >
                <?php foreach ($tabs as $index => $tab) { ?>
                    <?php
                    $_element      = $tab['element'];
                    $id            = $_element->getId();
                    $addIcon       = $_element->getData('add_icon');
                    $_iconPosition = $_element->getData('icon_position');
                    $_icon         = $_element->getData('icon');
                    $isActive = in_array($index+1, $activeSections);
                    ?>
                    <div class="mgz-panel <?=  $isActive ? ' mgz-active block' : '' ?> <?= $index == 0 ? 'mgz-panel-first' : '' ?> <?= ($index == $count-1)  ? 'mgz-panel-last' : '' ?>" :class="{'mgz-active': includes(<?= $index+1 ?>)}">
                        <div class="mgz-panel-heading mgz-text-<?= $element->getData('section_align') ?> mgz-icon-position-<?= $iconPosition ?>">
                            <h4 class="mgz-panel-heading-title" @click="click(<?=$index+1?>)">
                                <a href="#tab-<?= $id ?>" class="<?= $accordionIcon ? 'has-icon' : '' ?>">
                                    <?php if ($accordionIcon) { ?><i class="mgz-accoridon-icon-<?= $accordionIcon ?> <?= $isActive ? $activeIcon : $icon ?>"></i><?php } ?>
                                    <span><?php if ($addIcon && $_icon && $_iconPosition == 'left') { ?><i class="<?= $_icon ?>"></i><?php } ?><?= $_element->getTitle() ?>
                                        <?php if ($addIcon && $_icon && $_iconPosition == 'right') { ?><i class="<?= $_icon ?>"></i><?php } ?>
                                    </span>
                                </a>
                            </h4>
                        </div>
                        <?php if(!$isActive): ?>
                        <template x-if="clicked">
                        <?php endif; ?>
                            <div class="mgz-panel-body block transition-all duration-700 overflow-hidden max-h-0"
                             x-bind:style="includes(<?=$index+1?>) ? 'max-height: ' + $refs.<?= $id . "_container_". ($index+1)?>.scrollHeight + 'px; display: block; transition-property: all !important;' : 'display: block; transition-property: all !important;'"
                             style="max-height:<?= $isActive ? 'none': '0' ?>; display: block;"
                            >
                                <div class="mgz-panel-body-inner" x-ref="<?= $id . "_container_". ($index+1)?>">
                                    <?= $tab['html'] ?>
                                </div>
                            </div>
                        <?php if(!$isActive) : ?>
                        </template>
                        <?php endif; ?>
                    </div>

                <?php } ?>
            </div>
        </div>
	</div>
<?php } ?>
