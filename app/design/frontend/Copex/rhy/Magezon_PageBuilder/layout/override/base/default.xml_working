<?xml version="1.0"?>
<!--
/**
 * Magezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license.
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  Magezon
 * @package   Magezon_PageBuilder
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
<!--        <css src="Magezon_Core::css/magnific.css"/>-->
        <css src="Magezon_PageBuilder::css/styles.css" defer="defer"/>
        <css src="Magezon_PageBuilder::css/pagebuilder.css" defer="defer"/>
        <css src="Magezon_PageBuilder::css/font/replace.css" defer="defer"/>
<!--        <css src="Magezon_PageBuilder::vendor/photoswipe/photoswipe.css"/>-->
<!--        <css src="Magezon_PageBuilder::vendor/photoswipe/default-skin/default-skin.css"/>-->
<!--        <css src="Magezon_PageBuilder::vendor/blueimp/css/blueimp-gallery.min.css"/>-->
    </head>
    <body>
        <referenceContainer name="before.body.end">
            <block class="Magento\Framework\View\Element\Template" name="magezon.pagebuilder.js" template="Magezon_PageBuilder::scripts.phtml"/>
        </referenceContainer>
    </body>
</page>