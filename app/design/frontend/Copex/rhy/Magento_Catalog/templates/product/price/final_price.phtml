<?php
// @codingStandardsIgnoreFile

?>


<?php
/** @var \Magento\Catalog\Pricing\Render\FinalPriceBox $block */
$productId = $block->getSaleableItem()->getId();



/** ex: \Magento\Catalog\Pricing\Price\RegularPrice */
/** @var \Magento\Framework\Pricing\Price\PriceInterface $priceModel */
$priceModel = $block->getPriceType('regular_price');

/** ex: \Magento\Catalog\Pricing\Price\FinalPrice */
/** @var \Magento\Framework\Pricing\Price\PriceInterface $finalPriceModel */
$finalPriceModel = $block->getPriceType('final_price');
$idSuffix = $block->getIdSuffix() ? $block->getIdSuffix() : '';
$schema = ($block->getZone() == 'item_view') ? true : false;
?>

<?php if ($block->hasSpecialPrice()): ?>
    <span class="old-price">
        <?php /* @escapeNotVerified */ echo $block->renderAmount($priceModel->getAmount(), [
            'display_label'     => __('Regular Price'),
            'price_id'          => $block->getPriceId('old-price-' . $idSuffix),
            'price_type'        => 'oldPrice',
            'include_container' => true,
            'skip_adjustments'  => true
        ]); ?>
        <?= $block->getLayout()->getBlock('old.price.after')->toHtml(); ?>
    </span>
    <span class="special-price">
        <?php /* @escapeNotVerified */ echo $block->renderAmount($finalPriceModel->getAmount(), [
            'display_label'     => __('Special Price'),
            'price_id'          => $block->getPriceId('product-price-' . $idSuffix),
            'price_type'        => 'finalPrice',
            'include_container' => true,
            'schema' => $schema
        ]); ?>
    </span>
    <div class="omnibus-price">
            <span class=""><?= __("30-day best price") ?>: </span>
            <span><?php /* @escapeNotVerified */ echo $block->renderAmount($finalPriceModel->getAmount(), [
                'include_container' => false,
                'skip_adjustments'  => true
            ]); ?></span>
    </div>
<?php else : ?>
    <?php /* @escapeNotVerified */ echo $block->renderAmount($finalPriceModel->getAmount(), [
        'price_id'          => $block->getPriceId('product-price-' . $idSuffix),
        'price_type'        => 'finalPrice',
        'include_container' => true,
        'schema' => $schema
    ]); ?>
<?php endif; ?>

<?php if ($block->showMinimalPrice()): ?>
    <?php if ($block->getUseLinkForAsLowAs()):?>
        <a href="<?php /* @escapeNotVerified */ echo $block->getSaleableItem()->getProductUrl(); ?>" class="minimal-price-link">
            <?php /* @escapeNotVerified */ echo $block->renderAmountMinimal(); ?>
        </a>
    <?php else:?>
        <span class="minimal-price-link">
            <?php /* @escapeNotVerified */ echo $block->renderAmountMinimal(); ?>
        </span>
    <?php endif?>
<?php endif; ?>
