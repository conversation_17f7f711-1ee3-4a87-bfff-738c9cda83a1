<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductAttributes;
use Hyva\Theme\ViewModel\ProductCompare;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\Wishlist;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var ProductAttributes $attributesViewModel */
$attributesViewModel = $viewModels->require(ProductAttributes::class);
/** @var Product $product */
$product = $productViewModel->getProduct();
$productType = $product->getTypeId();

?>
<div class="w-full mb-6">
    <div class="my-2 flex">
        <?= $block->getChildHtml('product.info.review') ?>
    </div>

    <?php if ($shortDescription = $productViewModel->getShortDescription(false,false)) { ?>
        <div class="mb-4 leading-relaxed product-description"><?= /* @noEscape */ $shortDescription ?></div>
    <?php } ?>
    <?php $alerts = $block->getChildHtml("alert.urls"); ?>
    <?php if($alerts): ?>
        <div class="flex flex-col justify-between my-4 p-2">
            <?= $alerts ?>
        </div>
    <?php endif; ?>
    <div class="flex flex-col justify-between my-4 bg-container-darker p-2">
        <?php
            $stock = $block->getChildBlock('product.info.stockstatus');
            if($stock) : ?>
            <span class="flex flex-row gap-2">
                <?php
                    $stock->setIsProductPage(true);
                    echo $stock->toHtml();
                ?>
            </span>
            <?php endif; ?>
            <?php if (!$product->isAvailable() && $product->getDeliveryDate()) : ?>
                <?php $dateFormatHelper = $this->helper("CopeX\WorkDays\Helper\Data"); ?>
                <div class="stock unavailable-backorder" title="<?php /* @escapeNotVerified */ echo __('Expected available from') ?>">
                    <span class="label"><?php /* @escapeNotVerified */ echo __('Expected available from') ?>:</span>
                    <span><?= $dateFormatHelper->getLocaleDate(strtotime($product->getDeliveryDate()),'EEEE|dd.MM.YYYY'); ?></span>
                </div>
            <?php endif; ?>
        <?= $block->getChildHtml("product.info.attributes.before") ?>
    </div>

    <dl id="product-details">
        <?php foreach ($block->getAttributes() as $attributeConfig):
            $attribute = $attributesViewModel->getAttributeFromLayoutConfig($attributeConfig); ?>
            <?php if ($value = $attribute['value'] ?? null): ?>
                <div class="flex border-t border-gray-300 py-2 last:mb-6 last:border-b attribute-<?=/* @noEscape */ $attribute['code'] ?: "" ?>">
                    <dt class="w-1/2 text-left text-gray-700 product-detail-label">
                        <?= $escaper->escapeHtml($attribute['label']) ?>
                    </dt>
                    <dd class="w-1/2 ml-2 text-left text-gray-900 product-detail-value">
                        <?= $escaper->escapeHtml($value) ?>
                    </dd>
                </div>
            <?php endif ?>
        <?php endforeach; ?>
    </dl>
    <?= $block->getChildHtml("product.info.form.before") ?>

    <?= $block->getChildHtml('product.info.form') ?>

    <div class="flex flex-col sm:flex-row sm:items-end my-4 sm:justify-between">

        <?php if ($productType === 'bundle'): ?>
            <span class="sr-only">
                <?= $escaper->escapeHtml(__('The price depends on the chosen options')) ?>
            </span>
        <?php endif; ?>

        <div role="group" aria-label="<?= $escaper->escapeHtmlAttr('Price') ?>">
            <?= $block->getChildHtml("product.info.price") ?>
        </div>

        <div class="flex mt-4 justify-between sm:mt-0 sm:ml-auto">
            <?php if ($product->isSaleable()): ?>
                <div class="flex mt-4 justify-end w-full">
                    <?= $block->getChildHtml("product.info.quantity") ?>
                    <?= $block->getChildHtml("product.info.addtocart") ?>
                </div>
                <div class="flex mt-4 justify-end">
                    <?= $block->getChildHtml('addtocart.shortcut.buttons') ?>
                </div>
            <?php endif; ?>
            <div class="flex mt-4 justify-end">
                <?= $block->getChildHtml('product.info.addtowishlist'); ?>
                <?= $block->getChildHtml('product.info.addtocompare'); ?>
                <?= $block->getChildHtml('product.info.emailtofriend'); ?>
                <?= $block->getChildHtml('product.info.additional.actions'); ?>
            </div>
        </div>

    </div>
    <div class="flex flex-col sm:flex-row items-end my-4">

    </div>

    <?php if ($tierPriceBlock = $block->getChildHtml("product.price.tier")): ?>
        <div class="py-4 my-2 tier-price-container">
            <?= /** @noEscape */ $tierPriceBlock ?>
        </div>
    <?php endif; ?>

    <?= $block->getChildHtml("product.info.additional") ?>
</div>
