<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Catalog\Block\Product\View\Details;
use Magento\Framework\Escaper;

/** @var ViewModelRegistry $viewModels */
/** @var Details $block */
/** @var Escaper $escaper */

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

$divider = $block->getDivider() !== NULL ? $block->getDivider() : true;
$sectionIsOpen = '';
?>

<div x-data="initProductSections" class="py-3 px-6 bg-gray-100">
    <nav
        class="hidden lg:flex pb-6 snap overflow-x-auto overscroll-x-contain"
        aria-label="<?= $escaper->escapeHtml(__('Navigate to Product Section')) ?>"
        x-show="items.length"
    >
        <template x-for="tab in items">
            <button
                type="button"
                class="relative flex-auto pt-1 pb-3 px-3 border-b whitespace-nowrap font-bold
                    aria-expanded:text-blue-700 aria-expanded:border-current aria-expanded:after:border-inherit
                    after:absolute after:inset-x-0 after:bottom-0 after:border-b-2 after:border-transparent"
                :aria-expanded="sectionItemIsOpen(tab.id)"
                :aria-controls="tab.id"
                @click="openSectionItem(tab.id)"
                x-text="tab.getAttribute('data-name')"
            ></button>
        </template>
    </nav>

    <div
        x-ref="accordion"
        <?php if ($divider): ?>
        class="divide-y lg:divide-y-0"
        <?php endif; ?>
        style="min-height: 500px"
    >
        <?php foreach ($block->getGroupSortedChildNames('detailed_info', '') as $key => $sectionName): ?>
            <?php
                $sectionBlock  = $block->getLayout()->getBlock($sectionName);
                $sectionHtml   = (string) $sectionBlock->toHtml();
                $sectionTitle = $sectionBlock->getTitle() ?: $sectionName;
                $sectionId = str_replace('.', '-', $sectionBlock->getNameInLayout());
                if (empty(trim($sectionHtml))) {
                    continue;
                }

                if ($key === 0) {
                    $sectionIsOpen = $sectionId;
                }
            ?>
            <details
                id="<?= $escaper->escapeHtmlAttr($sectionId) ?>"
                data-name="<?= $escaper->escapeHtml($sectionTitle) ?>"
                x-data="{ id: $el.id }"
                class="group scroll-mt-8 lg:scroll-mt-24"
                :open="sectionItemIsOpen(id)"
            >
                <summary
                    @click.prevent="openSectionItem(id)"
                    class="flex [&::-webkit-details-marker]:hidden lg:hidden w-full items-center justify-between py-3 text-lg font-bold group-open:text-blue-700"

                >
                    <span><?= $escaper->escapeHtml($sectionTitle) ?></span>
                    <span class="transition-transform group-open:rotate-180">
                        <?= $heroiconsSolid->chevronDownHtml('', 20, 20, ["aria-hidden" => true]); ?>
                    </span>
                </summary>
                <div x-show="sectionItemIsOpen(id)" x-collapse>
                    <div class="pb-3">
                        <?= /** @noEscape  */ $sectionHtml ?>
                    </div>
                </div>
            </details>
        <?php endforeach; ?>
    </div>
    <script>
        function initProductSections() {
            return {
                items: [],
                currentOpen: '<?= /** @noEscape */ $sectionIsOpen ?>',
                init() {
                    this.$refs.accordion.style.minHeight = null;
                    this.items = [...this.$refs.accordion.children];
                },
                openSectionItem(id) {
                    this.currentOpen = id;
                    this.$nextTick(() => {
                        document.getElementById(this.currentOpen).scrollIntoView({
                            behavior: "smooth",
                            block: "nearest",
                            inline: "nearest"
                        });
                    });
                },
                sectionItemIsOpen(id) {
                    return this.currentOpen === id
                },
            }
        }
    </script>
    <?php // To ensure the summary is visible when JavaScript is disabled.  ?>
    <noscript>
        <style>details[data-name] summary { display: flex }</style>
    </noscript>
</div>
