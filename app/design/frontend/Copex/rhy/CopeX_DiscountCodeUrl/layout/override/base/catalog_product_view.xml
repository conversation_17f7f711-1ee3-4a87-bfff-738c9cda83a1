<?xml version="1.0"?>
<!--
/**
 * CopeX
 *
 * @category   CopeX
 * @package    CopeX_CookieNotification
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
<!--        <referenceContainer name="after.body.start">-->
<!--            <block class="Magento\Framework\View\Element\Template" name="copex_couponcodeproduct" template="CopeX_DiscountCodeUrl::product.phtml">-->
<!--                <arguments>-->
<!--                    <argument name="view_model" xsi:type="object" >\CopeX\DiscountCodeUrl\Helper\Config</argument>-->
<!--                    <argument name="product_view_model" xsi:type="object" >\CopeX\DiscountCodeUrl\Helper\Product</argument>-->
<!--                </arguments>-->
<!--            </block>-->
<!--        </referenceContainer>-->
    </body>
</page>
