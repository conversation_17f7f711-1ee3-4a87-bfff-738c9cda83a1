<?xml version="1.0"?>
<!--
/**
 * Magezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license.
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  Magezon
 * @package   Magezon_Builder
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
<!--        <css src="Magezon_Core::css/owlcarousel/owl.carousel.min.css"/>-->
<!--        <css src="Magezon_Core::css/animate.css"  defer="defer"/>-->
<!--        <css src="Magezon_Core::css/fontawesome5.css"  defer="defer"/>-->
<!--        <css src="Magezon_Core::css/mgz_font.css" defer="defer"/>-->
        <css src="Magezon_Core::css/mgz_bootstrap.css" />
<!--        <css src="Magezon_Builder::css/openiconic.min.css"/>-->
        <css src="Magezon_Builder::css/styles.css"  defer="defer"/>
<!--        <css src="Magezon_Builder::css/common.css"  defer="defer"/>-->
    </head>
    <body>
        <referenceBlock name="head.additional">
            <block class="Magento\Framework\View\Element\Template" name="magezon.builder.head" template="Magezon_Builder::head.phtml"/>
            <block class="Magento\Framework\View\Element\Template" name="magezon.builder.fontawesome" template="Magezon_Builder::fontawesome.phtml"  ifconfig="mgzbuilder/customization/enable_fontawesome" />
        </referenceBlock>
        <referenceContainer name="before.body.end">
            <block class="Magento\Framework\View\Element\Template" name="magezon.builder.footer" template="Magezon_Builder::footer.phtml"/>
        </referenceContainer>
    </body>
</page>
