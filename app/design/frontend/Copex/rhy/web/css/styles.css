/* purgecss start ignore */

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(147 197 253 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(147 197 253 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/* ! tailwindcss v3.4.13 | MIT License | https://tailwindcss.com */

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: color-mix(in srgb, var(--color-blue-600, #0C40A5) calc(var(--tw-ring-opacity, 1) * 100%), transparent);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: color-mix(in srgb, var(--color-blue-600, #0C40A5) calc(var(--tw-border-opacity, 1) * 100%), transparent);
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6b7280;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
  text-align: inherit;
}

::-webkit-datetime-edit {
  display: inline-flex;
}

::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[multiple],[size]:where(select:not([size="1"])) {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: color-mix(in srgb, var(--color-blue-600, #0C40A5) calc(var(--tw-text-opacity, 1) * 100%), transparent);
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: color-mix(in srgb, var(--color-blue-600, #0C40A5) calc(var(--tw-ring-opacity, 1) * 100%), transparent);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {
  [type='checkbox']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {
  [type='radio']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media (forced-colors: active)  {
  [type='checkbox']:indeterminate {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}

h1,.h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
  margin-bottom: 2rem;
  margin-top: 2rem;
  overflow-wrap: break-word;
  font-weight: 500;
}

h1, .h1 {
  font-size: 2rem;
  line-height: 2rem;
}

h2, .h2 {
  font-size: 1.8rem;
  line-height: 1.8rem;
  margin-top: 2rem;
}

h3, .h3 {
  font-size: 1.6rem;
  line-height: 1.6rem;
  margin-bottom: 1rem;
}

h4, .h4 {
  font-size: 1.4rem;
}

h5, .h5 {
  font-size: 1.2rem;
}

h6, .h6 {
  font-size: 1rem;
}

h1.page-title{
  padding-bottom: 0.5rem;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1770px) {
  .container {
    max-width: 1770px;
  }
}

.form-input,.form-textarea,.form-select,.form-multiselect {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

.form-input:focus, .form-textarea:focus, .form-select:focus, .form-multiselect:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: color-mix(in srgb, var(--color-blue-600, #0C40A5) calc(var(--tw-ring-opacity, 1) * 100%), transparent);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: color-mix(in srgb, var(--color-blue-600, #0C40A5) calc(var(--tw-border-opacity, 1) * 100%), transparent);
}

.form-input::-moz-placeholder, .form-textarea::-moz-placeholder {
  color: #6b7280;
  opacity: 1;
}

.form-input::placeholder,.form-textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}

.form-input::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

.form-input::-webkit-date-and-time-value {
  min-height: 1.5em;
  text-align: inherit;
}

.form-input::-webkit-datetime-edit {
  display: inline-flex;
}

.form-input::-webkit-datetime-edit,.form-input::-webkit-datetime-edit-year-field,.form-input::-webkit-datetime-edit-month-field,.form-input::-webkit-datetime-edit-day-field,.form-input::-webkit-datetime-edit-hour-field,.form-input::-webkit-datetime-edit-minute-field,.form-input::-webkit-datetime-edit-second-field,.form-input::-webkit-datetime-edit-millisecond-field,.form-input::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

.form-select:where([size]:not([size="1"])) {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

.form-checkbox,.form-radio {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: color-mix(in srgb, var(--color-blue-600, #0C40A5) calc(var(--tw-text-opacity, 1) * 100%), transparent);
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

.form-checkbox {
  border-radius: 0px;
}

.form-radio {
  border-radius: 100%;
}

.form-checkbox:focus,.form-radio:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: color-mix(in srgb, var(--color-blue-600, #0C40A5) calc(var(--tw-ring-opacity, 1) * 100%), transparent);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.form-checkbox:checked,.form-radio:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

.form-checkbox:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {
  .form-checkbox:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

.form-radio:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {
  .form-radio:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

.form-checkbox:checked:hover,.form-checkbox:checked:focus,.form-radio:checked:hover,.form-radio:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

.form-checkbox:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media (forced-colors: active)  {
  .form-checkbox:indeterminate {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

.form-checkbox:indeterminate:hover,.form-checkbox:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

.prose {
  color: var(--tw-prose-body);
  max-width: 65ch;
}

.prose :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-links);
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-weight: 500;
}

.prose :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-bold);
  font-weight: 600;
}

.prose :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}

.prose :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}

.prose :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}

.prose :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}

.prose :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}

.prose :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}

.prose :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}

.prose :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}

.prose :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}

.prose :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
}

.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}

.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters);
}

.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  color: var(--tw-prose-bullets);
}

.prose :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em;
}

.prose :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
}

.prose :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-inline-start-width: 0.25rem;
  border-inline-start-color: var(--tw-prose-quote-borders);
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1em;
}

.prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: open-quote;
}

.prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: close-quote;
}

.prose :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}

.prose :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 900;
  color: inherit;
}

.prose :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}

.prose :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit;
}

.prose :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}

.prose :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}

.prose :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5;
}

.prose :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}

.prose :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  display: block;
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  padding-inline-end: 0.375em;
  padding-bottom: 0.1875em;
  padding-inline-start: 0.375em;
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em;
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: "`";
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: "`";
}

.prose :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.875em;
}

.prose :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.9em;
}

.prose :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-inline-end: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-inline-start: 1.1428571em;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: none;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: none;
}

.prose :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  width: 100%;
  table-layout: auto;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}

.prose :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}

.prose :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}

.prose :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders);
}

.prose :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 0;
}

.prose :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: baseline;
}

.prose :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}

.prose :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: top;
}

.prose :where(th, td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  text-align: start;
}

.prose :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}

.prose {
  --tw-prose-body: #374151;
  --tw-prose-headings: #111827;
  --tw-prose-lead: #4b5563;
  --tw-prose-links: #111827;
  --tw-prose-bold: #111827;
  --tw-prose-counters: #6b7280;
  --tw-prose-bullets: #d1d5db;
  --tw-prose-hr: #e5e7eb;
  --tw-prose-quotes: #111827;
  --tw-prose-quote-borders: #e5e7eb;
  --tw-prose-captions: #6b7280;
  --tw-prose-kbd: #111827;
  --tw-prose-kbd-shadows: 17 24 39;
  --tw-prose-code: #111827;
  --tw-prose-pre-code: #e5e7eb;
  --tw-prose-pre-bg: #1f2937;
  --tw-prose-th-borders: #d1d5db;
  --tw-prose-td-borders: #e5e7eb;
  --tw-prose-invert-body: #d1d5db;
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: #9ca3af;
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: #9ca3af;
  --tw-prose-invert-bullets: #4b5563;
  --tw-prose-invert-hr: #374151;
  --tw-prose-invert-quotes: #f3f4f6;
  --tw-prose-invert-quote-borders: #374151;
  --tw-prose-invert-captions: #9ca3af;
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: #d1d5db;
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: #4b5563;
  --tw-prose-invert-td-borders: #374151;
  font-size: 1rem;
  line-height: 1.75;
}

.prose :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}

.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}

.prose :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  padding-inline-start: 1.625em;
}

.prose :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.5714286em;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}

.prose :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-slate {
  --tw-prose-body: #334155;
  --tw-prose-headings: #0f172a;
  --tw-prose-lead: #475569;
  --tw-prose-links: #0f172a;
  --tw-prose-bold: #0f172a;
  --tw-prose-counters: #64748b;
  --tw-prose-bullets: #cbd5e1;
  --tw-prose-hr: #e2e8f0;
  --tw-prose-quotes: #0f172a;
  --tw-prose-quote-borders: #e2e8f0;
  --tw-prose-captions: #64748b;
  --tw-prose-kbd: #0f172a;
  --tw-prose-kbd-shadows: 15 23 42;
  --tw-prose-code: #0f172a;
  --tw-prose-pre-code: #e2e8f0;
  --tw-prose-pre-bg: #1e293b;
  --tw-prose-th-borders: #cbd5e1;
  --tw-prose-td-borders: #e2e8f0;
  --tw-prose-invert-body: #cbd5e1;
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: #94a3b8;
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: #94a3b8;
  --tw-prose-invert-bullets: #475569;
  --tw-prose-invert-hr: #334155;
  --tw-prose-invert-quotes: #f1f5f9;
  --tw-prose-invert-quote-borders: #334155;
  --tw-prose-invert-captions: #94a3b8;
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: #cbd5e1;
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: #475569;
  --tw-prose-invert-td-borders: #334155;
}

.btn {
  display: flex;
  align-items: center;
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-gray-darker, #2E332C) calc(var(--tw-bg-opacity) * 100%), transparent);
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .btn {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1280px) {
  .btn {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.btn svg {
  display: inline-flex;
}

.btn span {
  vertical-align: middle;
}

.btn:hover {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.btn:focus {
  border-color: transparent;
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-ring-opacity) * 100%), transparent);
  --tw-ring-opacity: 0.5;
}

.btn-primary {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-bg-opacity) * 100%), transparent);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-primary:hover {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-primary-darker, #0D1840) calc(var(--tw-bg-opacity) * 100%), transparent);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-secondary {
  border-width: 2px;
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-gray-lighter, #41473D) calc(var(--tw-border-opacity) * 100%), transparent);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-secondary:hover {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-gray-darker, #2E332C) calc(var(--tw-border-opacity) * 100%), transparent);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-secondary:focus {
  border-width: 2px;
  border-color: transparent;
}

.btn-size-lg {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.actions-toolbar .primary button {
  display: flex;
  align-items: center;
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-gray-darker, #2E332C) calc(var(--tw-bg-opacity) * 100%), transparent);
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .actions-toolbar .primary button {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1280px) {
  .actions-toolbar .primary button {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.actions-toolbar .primary button svg {
  display: inline-flex;
}

.actions-toolbar .primary button span {
  vertical-align: middle;
}

.actions-toolbar .primary button:hover {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.actions-toolbar .primary button:focus {
  border-color: transparent;
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-ring-opacity) * 100%), transparent);
  --tw-ring-opacity: 0.5;
}

.actions-toolbar .primary button {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-bg-opacity) * 100%), transparent);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.actions-toolbar .primary button:hover {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-primary-darker, #0D1840) calc(var(--tw-bg-opacity) * 100%), transparent);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

input[type="range"] {
  --hv-fill-min: var(--tw-range-fill-min, 0);
  --hv-fill: var(--tw-range-fill-max, 0);
  --hv-fill-color: var(--tw-range-fill-color, color-mix(in srgb, var(--color-blue-700, #0C40A5) calc(1 * 100%), transparent));
  --hv-track-size: var(--tw-range-track-size, 0.5rem);
  --hv-thumb-size: var(--tw-range-thumb-size, 1.5rem);
  --hv-thumb-color: var(--tw-range-thumb-color, #fff);
  --hv-track: linear-gradient(
            to right,
            currentcolor 0%,
            currentcolor calc(var(--hv-fill-min) * 1%),
            var(--hv-fill-color) calc(var(--hv-fill-min) * 1%),
            var(--hv-fill-color) calc(var(--hv-fill) * 1%),
            currentcolor calc(var(--hv-fill) * 1%),
            currentcolor 100%
        );
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}

input[type="range"]:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

input[type="range"] {
  height: var(--hv-thumb-size);
}

input[type="range"]:hover {
  --hv-thumb-color: #93c5fd;
}

input[type="range"]:focus {
  --hv-thumb-color: color-mix(in srgb, var(--color-blue-500, #0C40A5) calc(1 * 100%), transparent);
}

input[type="range"]:focus::-webkit-slider-thumb {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

input[type="range"]:focus::-moz-range-thumb {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

input[type="range"]::-webkit-slider-runnable-track {
  width: 100%;
  border-radius: 0.5rem;
  height: var(--hv-track-size);
  background: var(--hv-track);
}

input[type="range"]::-moz-range-track {
  width: 100%;
  border-radius: 0.5rem;
  height: var(--hv-track-size);
  background: var(--hv-track);
}

input[type="range"]::-webkit-slider-thumb {
  cursor: pointer;
  -webkit-appearance: none;
          appearance: none;
  border-radius: 9999px;
  border-width: 3px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  -webkit-transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  width: var(--hv-thumb-size);
  height: var(--hv-thumb-size);
  margin-top: calc(
                0.5 * calc(var(--hv-track-size) - var(--hv-thumb-size))
            );
  background-color: var(--hv-thumb-color);
}

input[type="range"]::-moz-range-thumb {
  box-sizing: border-box;
  cursor: pointer;
  -moz-appearance: none;
       appearance: none;
  border-radius: 9999px;
  border-width: 3px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  -moz-transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  width: var(--hv-thumb-size);
  height: var(--hv-thumb-size);
  background-color: var(--hv-thumb-color);
}

#category-view-container {
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-left: 0px;
  padding-right: 0px;
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.toolbar-products .modes-mode {
  height: 1.5rem;
  width: 1.5rem;
}

.toolbar-products .modes-mode span {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.toolbar-products .modes-mode.mode-grid {
  background-image: url('data:image/svg+xml;utf8,<svg  stroke="currentColor" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.5 3.5H10.5V10.5H3.5V3.5Z"/> <path d="M3.5 13.5H10.5V20.5H3.5V13.5Z"/> <path d="M13.5 3.5H20.5V10.5H13.5V3.5Z"/> <path d="M13.5 13.5H20.5V20.5H13.5V13.5Z"/> </svg>');
}

.toolbar-products .modes-mode.mode-list {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" /></svg>');
}

.column.main.order-4 {
  order: 4 !important;
}

.price-including-tax + .price-excluding-tax {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.price-including-tax + .price-excluding-tax:before {
  content: attr(data-label) ': ';
}

.price-excluding-tax,
.price-including-tax {
  display: block;
  white-space: nowrap;
}

[data-content-type$='block'] .price-excluding-tax p:last-child,[data-content-type$='block'] 
.price-including-tax p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.price-excluding-tax .price, .price-including-tax .price {
  font-weight: 600;
  line-height: 1.625;
}

.checkout-cart-index .page-title, .checkout-cart-index .cart-form .title-font {
  margin: 0px;
  padding: 0px;
}

.checkout-cart-index .cart.item td {
  vertical-align: middle;
}

.checkout-cart-index .cart.item td .product-item-details {
  display: flex;
  align-items: center;
}

#customer-login-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

@media (min-width: 768px) {
  #customer-login-container {
    flex-direction: row;
  }
}

/**
 * different styles can be found at https://tailwindcss-forms.vercel.app/
 **/

.form-input,
.form-email,
.form-select,
.form-multiselect,
.form-textarea,
[type='text'],
[type='email'],
[type='url'],
[type='password'],
[type='number'],
[type='date'],
[type='datetime-local'],
[type='month'],
[type='search'],
[type='tel'],
[type='time'],
[type='week'],
[multiple],
textarea,
select {
  width: 100%;
  border-radius: 0.125rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-gray-lightest, #7B8774) calc(var(--tw-border-opacity) * 100%), transparent);
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.form-input:focus, .form-email:focus, .form-select:focus, .form-multiselect:focus, .form-textarea:focus, [type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-border-opacity) * 100%), transparent);
  box-shadow: none;
}

.page.messages {
  position: sticky;
  top: 0px;
  z-index: 20;
}

.page.messages .messages
    section#messages {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

@media (min-width: 640px) {
  .page.messages .messages
    section#messages {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .page.messages .messages
    section#messages {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .page.messages .messages
    section#messages {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .page.messages .messages
    section#messages {
    max-width: 1280px;
  }
}

@media (min-width: 1770px) {
  .page.messages .messages
    section#messages {
    max-width: 1770px;
  }
}

.page.messages .messages
    section#messages {
  margin-left: auto;
  margin-right: auto;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.message {
  margin-bottom: 0.5rem;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-quaternary, #16A34A) calc(var(--tw-bg-opacity) * 100%), transparent);
  padding: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.message.error {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-secondary, #DC2626) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.message.success {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.message.info,
    .message.warning,
    .message.notice {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-tertiary, #EAB308) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.message a {
  text-decoration-line: underline;
}

.product-item .price-container {
  display: block;
}

[data-content-type$='block'] .product-item .price-container p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.product-item .price-container .price-label {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.product-item .special-price .price-container .price-label {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.product-item .old-price .price-container {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
}

.product-item .old-price .price-container .price {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
}

.page-product-bundle .price-final_price .price-from .price-container, .page-product-bundle .price-final_price .price-to .price-container {
  margin-bottom: 1rem;
  display: block;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

[data-content-type$='block'] .page-product-bundle .price-final_price .price-from .price-container p:last-child,[data-content-type$='block']  .page-product-bundle .price-final_price .price-to .price-container p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.page-product-bundle .price-final_price .price-from .price-container .price-label, .page-product-bundle .price-final_price .price-to .price-container .price-label {
  display: block;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
}

[data-content-type$='block'] .page-product-bundle .price-final_price .price-from .price-container .price-label p:last-child,[data-content-type$='block']  .page-product-bundle .price-final_price .price-to .price-container .price-label p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.page-product-bundle .price-final_price .price-from .price-container .price, .page-product-bundle .price-final_price .price-to .price-container .price {
  display: block;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 600;
  line-height: 1.25;
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

[data-content-type$='block'] .page-product-bundle .price-final_price .price-from .price-container .price p:last-child,[data-content-type$='block']  .page-product-bundle .price-final_price .price-to .price-container .price p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.page-product-bundle .price-final_price .price-from .price-including-tax + .price-excluding-tax, .page-product-bundle .price-final_price .price-to .price-including-tax + .price-excluding-tax {
  margin-top: 0.25rem;
}

.page-product-bundle .price-final_price .price-from .price-including-tax + .price-excluding-tax .price, .page-product-bundle .price-final_price .price-to .price-including-tax + .price-excluding-tax .price {
  font-size: 1rem;
  line-height: 1.5rem;
}

.page-product-bundle .price-final_price .price-from .old-price .price-container .price,
                    .page-product-bundle .price-final_price .price-from .old-price .price-container .price-label,
                    .page-product-bundle .price-final_price .price-to .old-price .price-container .price,
                    .page-product-bundle .price-final_price .price-to .old-price .price-container .price-label {
  display: inline;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
}

.super_attribute .option_label {
  font-weight: 600;
}

.super_attribute .option_price {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-gray-lighter, #41473D) calc(var(--tw-text-opacity) * 100%), transparent);
}

.catalog-product-view h1.page-title {
  margin: 0px;
}

.catalog-product-view .product-name {
  margin-top: 0px;
  margin-bottom: 0px;
  text-align: left;
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 600;
}

@media (min-width: 768px) {
  .catalog-product-view .product-name {
    padding-left: 0px;
    padding-right: 0px;
  }
}

@media (min-width: 1024px) {
  .catalog-product-view .product-name {
    margin-top: 0px;
  }
}

.catalog-product-view.product-type-default .price-label {
  display: none;
}

.catalog-product-view .seo-link a {
  font-weight: 700;
}

#search_autocomplete .product_attribute .catalog-product-view .seo-link a {
  width: 100%;
}

.catalog-product-view .product.attribute.delivery_time, .catalog-product-view .product.attribute.workdays {
  margin-top: 0.5rem;
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .catalog-product-view .product.attribute.delivery_time, .catalog-product-view .product.attribute.workdays {
    margin-top: 0px;
    flex-direction: row;
    gap: 0.5rem;
  }
}

.catalog-product-view .product.attribute.delivery_time .type::after, .catalog-product-view .product.attribute.workdays .type::after {
  content: ':';
}

.catalog-product-view .product-description ul {
  list-style-position: inside;
  list-style-type: disc;
}

.wishlist-widget .price-box .price-label {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.wishlist-widget .price-box .old-price {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.modal {
  /*
   * TODO: add tailwind classes used for the cart and modal styles.
   * This will make the modal and off-canvas styles theme specific and more adjustable.
   */
}

.backdrop {
  position: fixed;
  inset: 0px;
  display: flex;
  background-color: rgb(0 0 0 / 0.25);
}

.snap {
  scroll-snap-type: x mandatory;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
  scrollbar-width: none;
}

.snap::-webkit-scrollbar {
  display: none;
}

.snap > div {
  scroll-snap-align: start;
}

body {
  overflow-y: scroll;
}

.clearfix::after {
  content: "";
  display: block;
  clear: both;
}

.page-main {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

@media (min-width: 1024px) {
  .page-main {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }
}

.columns {
  margin-left: auto;
  margin-right: auto;
  display: grid;
  max-width: 1770px;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  -moz-column-gap: 2rem;
       column-gap: 2rem;
  row-gap: 1rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 1rem;
}

@media (min-width: 768px) {
  .columns {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

.columns {
  grid-template-rows: auto minmax(0, 1fr);
}

.columns .main {
  order: 2;
}

.columns .sidebar {
  order: 3;
}

.product-main-full-width .columns {
  max-width: none;
}

.page-main-full-width .columns {
  max-width: none;
  padding-left: 0px;
  padding-right: 0px;
}

.page-with-filter .columns .sidebar-main {
  order: 1;
}

@media (min-width: 640px) {
  .page-layout-2columns-left .columns, .page-layout-2columns-right .columns, .page-layout-3columns .columns {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .page-layout-2columns-left .columns .main, .page-layout-2columns-right .columns .main, .page-layout-3columns .columns .main {
    grid-column: span 2 / span 2;
  }

  .page-layout-2columns-left .columns .sidebar, .page-layout-2columns-right .columns .sidebar, .page-layout-3columns .columns .sidebar {
    order: 3;
  }

  .page-with-filter .columns .sidebar-main {
    order: 1;
    grid-column: span 2 / span 2;
  }
}

@media (min-width: 768px) {
  .page-layout-2columns-left .columns, .page-layout-2columns-right .columns, .page-layout-3columns .columns {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .page-layout-2columns-left .columns .main, .page-layout-2columns-right .columns .main, .page-layout-3columns .columns .main {
    grid-row: span 2 / span 2;
  }

  .page-layout-2columns-left .columns .sidebar, .page-layout-2columns-right .columns .sidebar, .page-layout-3columns .columns .sidebar {
    grid-column: span 1 / span 1;
  }

  .page-layout-2columns-left .columns .main, .page-layout-3columns .columns .main {
    grid-column-start: 2 !important;
  }

  .page-layout-2columns-left .columns .sidebar, .page-layout-3columns .columns .sidebar {
    order: 1;
  }

  .page-layout-2columns-left .columns .sidebar ~ .sidebar-additional, .page-layout-3columns .columns .sidebar ~ .sidebar-additional {
    order: 3;
  }

  .page-layout-2columns-right .sidebar-main, .page-layout-2columns-right.page-with-filter .sidebar-main {
    order: 3;
  }
}

@media (min-width: 1024px) {
  .page-layout-2columns-left .columns, .page-layout-2columns-right .columns, .page-layout-3columns .columns {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .page-layout-2columns-left .columns .main, .page-layout-2columns-right .columns .main {
    grid-column: span 3 / span 3;
  }

  .page-layout-3columns .columns .sidebar-additional {
    grid-column-start: 4;
  }
}

.product-image-container {
  width: 100% !important;
}

.product-image-container img {
  width: 100%;
}

.swatch-attribute .swatch-attribute-options {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.swatch-attribute .swatch-attribute-options .swatch-option {
  margin: 0.25rem;
  display: flex;
  justify-content: center;
  border-width: 1px;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  min-width: 40px;
}

body {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-container, #fafafa) calc(var(--tw-bg-opacity) * 100%), transparent);
}

#cart-drawer {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

[x-cloak] {
  display: none !important;
}

.input {
  margin-right: 0.5rem;
  width: auto;
  border-radius: 0.25rem;
  border-width: 1px;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

@media (min-width: 768px) {
  .input {
    margin-right: 1rem;
  }
}

@media (min-width: 1024px) {
  .input {
    margin-right: 0px;
  }
}

@media (min-width: 1280px) {
  .input {
    margin-right: 1rem;
  }
}

.input-light {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.input-light:focus {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-border-opacity) * 100%), transparent);
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.card {
  border-radius: 0.125rem;
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-container-lighter, #ffffff) calc(var(--tw-bg-opacity) * 100%), transparent);
  padding: 1rem;
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.card-interactive:hover {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-container, #fafafa) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.duration-200 {
  transition-duration: 200ms;
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.transition {
  transition: transform 250ms ease, color 250ms ease;
}

.transform-180 {
  transform: rotate(-180deg);
}

.poppins {
  font: 400 1.2rem/2.2rem 'Poppins';
}

html {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  line-height: 1.5rem;
  line-height: 1.5;
  letter-spacing: 0em;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
  font-family: "Poppins", "Helvetica Neue", Arial, sans-serif;
  scroll-behavior: smooth;
  font-size: 1rem;
}

/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

/**
 * Layout
 */

/* Rows */

[data-content-type='row'] {
  box-sizing: border-box;
}

[data-content-type='row'] > div {
  margin-bottom: 0.625rem;
  padding: 0.625rem;
}

[data-content-type='row'][data-appearance='contained'] {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

@media (min-width: 640px) {
  [data-content-type='row'][data-appearance='contained'] {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  [data-content-type='row'][data-appearance='contained'] {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  [data-content-type='row'][data-appearance='contained'] {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  [data-content-type='row'][data-appearance='contained'] {
    max-width: 1280px;
  }
}

@media (min-width: 1770px) {
  [data-content-type='row'][data-appearance='contained'] {
    max-width: 1770px;
  }
}

[data-content-type='row'][data-appearance='contained'] {
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
}

[data-content-type='row'][data-appearance='contained'] [data-element='inner'] {
  box-sizing: border-box;
  background-attachment: scroll !important;
}

[data-content-type='row'][data-appearance='full-bleed'] {
  background-attachment: scroll !important;
}

[data-content-type='row'][data-appearance='full-width'] {
  background-attachment: scroll !important;
}

[data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

@media (min-width: 640px) {
  [data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  [data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  [data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  [data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
    max-width: 1280px;
  }
}

@media (min-width: 1770px) {
  [data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
    max-width: 1770px;
  }
}

/* Column Groups (both needed for pagebuider backwards compatibiliy) */

[data-content-type='column-group'], [data-content-type='column-line'] {
  flex-wrap: wrap;
}

@media (min-width: 768px) {
  [data-content-type='column-group'], [data-content-type='column-line'] {
    flex-wrap: nowrap;
  }
}

/* Columns */

[data-content-type='column'] {
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;
  background-attachment: scroll !important;
  flex-basis: 100%;
}

@media (min-width: 768px) {
  [data-content-type='column'] {
    flex-basis: auto
  }
}

/* Tabs/Tab Item */

[data-content-type='tabs'] .tabs-navigation {
  margin-bottom: -1px;
  display: block;
  padding: 0px;
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

[data-content-type$='block'] [data-content-type='tabs'] .tabs-navigation p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

[data-content-type='tabs'] .tabs-navigation li.tab-header {
  position: relative;
  margin-top: 0px;
  margin-bottom: 0px;
  margin-right: 0px;
  margin-left: -1px;
  display: inline-block;
  max-width: 100%;
  overflow-wrap: break-word;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
  border-width: 1px;
  border-bottom-width: 0px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

[data-content-type='tabs'] .tabs-navigation li.tab-header:first-child {
  margin-left: 0px;
}

[data-content-type='tabs'] .tabs-navigation li.tab-header.active {
  z-index: 20;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  border-bottom: 1px solid white;
}

[data-content-type='tabs'] .tabs-navigation li.tab-header a.tab-title {
  position: relative;
  display: block;
  cursor: pointer;
  white-space: normal;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  vertical-align: middle;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
}

[data-content-type$='block'] [data-content-type='tabs'] .tabs-navigation li.tab-header a.tab-title p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

[data-content-type='tabs'] .tabs-navigation li.tab-header a.tab-title span {
  font-weight: 600;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

[data-content-type='tabs'] .tabs-content {
  position: relative;
  z-index: 10;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 0.125rem;
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-container-lighter, #ffffff) calc(var(--tw-bg-opacity) * 100%), transparent);
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

[data-content-type='tabs'] .tabs-content [data-content-type='tab-item'] {
  box-sizing: border-box;
  padding: 2rem;
  min-height: inherit;
  background-attachment: scroll !important;
}

[data-content-type='tabs'].tab-align-left .tabs-content {
  border-top-left-radius: 0 !important;
}

[data-content-type='tabs'].tab-align-right .tabs-content {
  border-top-right-radius: 0 !important;
}

/**
 * Elements
 */

/* Text */

[data-content-type='text'] {
  overflow-wrap: break-word;
}

/* Heading */

[data-content-type='heading'] {
  overflow-wrap: break-word;
}

/* Buttons/Button Item */

[data-content-type='buttons'] {
  max-width: 100%;
}

[data-content-type='buttons'] [data-content-type='button-item'] {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  max-width: 100%;
}

[data-content-type='buttons'] [data-content-type='button-item'] [data-element='link'],
    [data-content-type='buttons'] [data-content-type='button-item'] [data-element='empty_link'] {
  max-width: 100%;
  overflow-wrap: break-word;
}

[data-content-type='buttons'] [data-content-type='button-item'] [data-element='empty_link'] {
  cursor: default;
}

[data-content-type='buttons'] [data-content-type='button-item'] a,
    [data-content-type='buttons'] [data-content-type='button-item'] button,
    [data-content-type='buttons'] [data-content-type='button-item'] div {
  display: inline-block;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

[data-content-type='buttons'] [data-content-type='button-item'] a.pagebuilder-button-link, [data-content-type='buttons'] [data-content-type='button-item'] button.pagebuilder-button-link, [data-content-type='buttons'] [data-content-type='button-item'] div.pagebuilder-button-link {
  box-sizing: border-box;
}

a.pagebuilder-button-primary, button.pagebuilder-button-primary, div.pagebuilder-button-primary {
  display: flex;
  align-items: center;
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-gray-darker, #2E332C) calc(var(--tw-bg-opacity) * 100%), transparent);
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  a.pagebuilder-button-primary, button.pagebuilder-button-primary, div.pagebuilder-button-primary {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1280px) {
  a.pagebuilder-button-primary, button.pagebuilder-button-primary, div.pagebuilder-button-primary {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

a.pagebuilder-button-primary svg, button.pagebuilder-button-primary svg, div.pagebuilder-button-primary svg {
  display: inline-flex;
}

a.pagebuilder-button-primary span, button.pagebuilder-button-primary span, div.pagebuilder-button-primary span {
  vertical-align: middle;
}

a.pagebuilder-button-primary:hover, button.pagebuilder-button-primary:hover, div.pagebuilder-button-primary:hover {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-bg-opacity) * 100%), transparent);
}

a.pagebuilder-button-primary:focus, button.pagebuilder-button-primary:focus, div.pagebuilder-button-primary:focus {
  border-color: transparent;
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-ring-opacity) * 100%), transparent);
  --tw-ring-opacity: 0.5;
}

a.pagebuilder-button-primary, button.pagebuilder-button-primary, div.pagebuilder-button-primary {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-bg-opacity) * 100%), transparent);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

a.pagebuilder-button-primary:hover, button.pagebuilder-button-primary:hover, div.pagebuilder-button-primary:hover {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-primary-darker, #0D1840) calc(var(--tw-bg-opacity) * 100%), transparent);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

a.pagebuilder-button-secondary, button.pagebuilder-button-secondary, div.pagebuilder-button-secondary {
  display: flex;
  align-items: center;
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-gray-darker, #2E332C) calc(var(--tw-bg-opacity) * 100%), transparent);
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  a.pagebuilder-button-secondary, button.pagebuilder-button-secondary, div.pagebuilder-button-secondary {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1280px) {
  a.pagebuilder-button-secondary, button.pagebuilder-button-secondary, div.pagebuilder-button-secondary {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

a.pagebuilder-button-secondary svg, button.pagebuilder-button-secondary svg, div.pagebuilder-button-secondary svg {
  display: inline-flex;
}

a.pagebuilder-button-secondary span, button.pagebuilder-button-secondary span, div.pagebuilder-button-secondary span {
  vertical-align: middle;
}

a.pagebuilder-button-secondary:hover, button.pagebuilder-button-secondary:hover, div.pagebuilder-button-secondary:hover {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-bg-opacity) * 100%), transparent);
}

a.pagebuilder-button-secondary:focus, button.pagebuilder-button-secondary:focus, div.pagebuilder-button-secondary:focus {
  border-color: transparent;
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-ring-opacity) * 100%), transparent);
  --tw-ring-opacity: 0.5;
}

a.pagebuilder-button-secondary, button.pagebuilder-button-secondary, div.pagebuilder-button-secondary {
  border-width: 2px;
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-gray-lighter, #41473D) calc(var(--tw-border-opacity) * 100%), transparent);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

a.pagebuilder-button-secondary:hover, button.pagebuilder-button-secondary:hover, div.pagebuilder-button-secondary:hover {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-gray-darker, #2E332C) calc(var(--tw-border-opacity) * 100%), transparent);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

a.pagebuilder-button-secondary:focus, button.pagebuilder-button-secondary:focus, div.pagebuilder-button-secondary:focus {
  border-width: 2px;
  border-color: transparent;
}

/* HTML Code */

[data-content-type='html'] {
  overflow-wrap: break-word;
}

/**
 * Media
 */

/* Image */

[data-content-type='image'] {
  box-sizing: border-box;
}

[data-content-type='image'] > [data-element='link'],
  [data-content-type='image'] > [data-element='link'] img {
  border-radius: inherit;
}

[data-content-type='image'] .pagebuilder-mobile-hidden {
  display: none;
}

@media (min-width: 768px) {
  [data-content-type='image'] .pagebuilder-mobile-hidden {
    display: inline-block;
  }
}

[data-content-type='image'] .pagebuilder-mobile-only {
  display: inline-block;
}

@media (min-width: 768px) {
  [data-content-type='image'] .pagebuilder-mobile-only {
    display: none;
  }
}

[data-content-type='image'] figcaption {
  overflow-wrap: break-word;
}

/* Video */

[data-content-type='video'] {
  font-size: 0;
}

[data-content-type='video'] .pagebuilder-video-inner {
  box-sizing: border-box;
  display: inline-block;
  width: 100%;
}

[data-content-type='video'] .pagebuilder-video-container {
  position: relative;
  overflow: hidden;
  border-radius: inherit;
  padding-top: 56.25%;
}

[data-content-type='video'] iframe,
  [data-content-type='video'] video {
  position: absolute;
  left: 0px;
  top: 0px;
  height: 100%;
  width: 100%;
}

/* Banner */

[data-content-type='banner'] > [data-element='link'], [data-content-type='banner'] > [data-element='empty_link'] {
  color: inherit;
  text-decoration: inherit;
}

[data-content-type='banner'] > [data-element='link']:hover, [data-content-type='banner'] > [data-element='empty_link']:hover {
  color: inherit;
  text-decoration: inherit;
}

[data-content-type='banner'] .pagebuilder-banner-wrapper {
  box-sizing: border-box;
  overflow-wrap: break-word;
  background-clip: padding-box;
  border-radius: inherit;
  background-attachment: scroll !important;
}

[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-overlay {
  position: relative;
  box-sizing: border-box;
  padding: 2rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-duration: 500ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 250ms ease, color 250ms ease;
}

[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-overlay.pagebuilder-poster-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
}

[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-overlay:not(.pagebuilder-poster-overlay) {
  max-width: none;
}

@media (min-width: 768px) {
  [data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-overlay:not(.pagebuilder-poster-overlay) {
    max-width: 36rem;
  }
}

[data-content-type='banner'] .pagebuilder-banner-wrapper.jarallax .video-overlay {
  z-index: 0;
}

[data-content-type='banner'] .pagebuilder-banner-wrapper [data-element='content'] {
  overflow: auto;
  min-height: 50px;
}

[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-banner-button {
  margin: 0px;
  margin-top: 1.25rem;
  display: inline-block;
  max-width: 100%;
  overflow-wrap: break-word;
  transition-property: opacity;
  transition-duration: 500ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  text-align: inherit;
}

[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-poster-content {
  width: 100%;
}

[data-content-type='banner'][data-appearance='collage-centered'] .pagebuilder-banner-wrapper .pagebuilder-overlay {
  margin-left: auto;
  margin-right: auto;
}

[data-content-type='banner'][data-appearance='collage-left'] .pagebuilder-banner-wrapper .pagebuilder-overlay {
  margin-right: auto;
}

[data-content-type='banner'][data-appearance='collage-right'] .pagebuilder-banner-wrapper .pagebuilder-overlay {
  margin-left: auto;
}

/* Slider */

[data-content-type='slider'] {
  visibility: hidden;
  position: relative;
}

[data-content-type='slider'].glider-initialized {
  visibility: visible;
}

[data-content-type='slider'] [data-role='glider-content'] {
  overflow-y: hidden;
}

[data-content-type='slider'] a.button {
  color: initial;
  padding: 10px;
  -webkit-text-decoration: none;
  text-decoration: none;
}

[data-content-type='slider'] .carousel-nav {
  position: absolute;
  bottom: 0px;
  margin-bottom: 0.5rem;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.75;
  left: 50%;
  transform: translateX(-50%);
}

/* Slide */

[data-content-type='slide'] {
  box-sizing: border-box;
  overflow: hidden;
  line-height: 1.25rem;
  min-height: inherit;
}

[data-content-type='slide'] > [data-element='link'],
  [data-content-type='slide'] > [data-element='empty_link'] {
  color: inherit;
  min-height: inherit;
  text-decoration: inherit;
}

[data-content-type='slide'] > [data-element='link']:hover, [data-content-type='slide'] > [data-element='empty_link']:hover {
  color: inherit;
  text-decoration: inherit;
}

[data-content-type='slide'] + [data-content-type='slide'] {
  height: 0px;
  min-height: 0px;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper {
  box-sizing: border-box;
  overflow-wrap: break-word;
  border-radius: inherit;
  min-height: inherit;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper .jarallax-viewport-element {
  position: absolute;
  top: 0px;
  z-index: 50;
  height: 100%;
  width: 0.125rem;
  left: -15000vw;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax .video-overlay {
  -webkit-transform: unset;
  z-index: 1;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax .pagebuilder-overlay {
  position: relative;
  z-index: 20;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax [id*='jarallax-container'] > div,
        [data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax [id*='jarallax-container'] > img,
        [data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax [id*='jarallax-container'] > video,
        [data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax [id*='jarallax-container'] > iframe {
  margin: auto !important;
  transform: none !important;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-overlay {
  box-sizing: border-box;
  padding: 2rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  border-radius: inherit;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-overlay.pagebuilder-poster-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: inherit;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-overlay:not(.pagebuilder-poster-overlay) {
  max-width: none;
}

@media (min-width: 768px) {
  [data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-overlay:not(.pagebuilder-poster-overlay) {
    max-width: 32rem;
  }
}

[data-content-type='slide'] .pagebuilder-slide-wrapper [data-element='content'] {
  overflow: auto;
  min-height: 50px;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-slide-button {
  margin: 0px;
  margin-top: 1.25rem;
  max-width: 100%;
  overflow-wrap: break-word;
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  text-align: inherit;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-poster-content {
  width: 100%;
}

[data-content-type='slide'][data-appearance='collage-centered'] .pagebuilder-slide-wrapper .pagebuilder-overlay {
  margin-left: auto;
  margin-right: auto;
}

[data-content-type='slide'][data-appearance='collage-left'] .pagebuilder-slide-wrapper .pagebuilder-overlay {
  margin-right: auto;
}

[data-content-type='slide'][data-appearance='collage-right'] .pagebuilder-slide-wrapper .pagebuilder-overlay {
  margin-left: auto;
}

/* Map */

[data-content-type='map'] {
  box-sizing: border-box;
  height: 18rem;
}

/**
 * Add Content
 */

/* Block */

[data-content-type$='block'] .block p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

/* Dynamic Block */

[data-content-type='dynamic_block'] [data-content-type='image'] img {
  display: inline;
}

[data-content-type='dynamic_block'] .block-banners .banner-item-content,
  [data-content-type='dynamic_block'] .block-banners-inline .banner-item-content {
  margin-bottom: auto;
}

/**
 * Glider
 */

.glider-contain {
  position: relative;
  margin-top: 0px;
  margin-bottom: 0px;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

.glider {
  overflow-y: hidden;
}

.glider.draggable {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  cursor: grab;
}

.glider.draggable .glider-slide img {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.glider.drag {
  cursor: grabbing;
}

.glider::-webkit-scrollbar {
  height: 0px;
  opacity: 0;
}

.glider .glider-track {
  z-index: 10;
  margin: 0px;
  display: flex;
  width: 100%;
  padding: 0px;
}

.glider-slide {
  margin: 0.5rem;
  width: 100%;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  align-content: center;
  justify-content: center;
  min-width: 150px;
}

.glider-slide img {
  max-width: 100%;
}

.glider-hide {
  opacity: 0;
}

.glider-prev.disabled, .glider-next.disabled {
  cursor: default;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
  opacity: 0.25;
}

.glider-dot {
  margin: 0.25rem;
  display: block;
  height: 0.75rem;
  width: 0.75rem;
  cursor: pointer;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
  opacity: 0.25;
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

[data-content-type$='block'] .glider-dot p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.glider-dot.active {
  opacity: 1;
}

@media (max-width: 36em) {
  .glider::-webkit-scrollbar {
    height: 0.25rem;
    width: 0.5rem;
    -webkit-appearance: none;
            appearance: none;
    opacity: 1;
  }

  .glider::-webkit-scrollbar-thumb {
    opacity: 1;
    border-radius: 99px;
    background-color: rgba(156, 156, 156, .25);
    box-shadow: 0 0 1px rgba(255, 255, 255, .25);
  }
}

[data-show-arrows='false'] .glider-prev,
  [data-show-arrows='false'] .glider-next {
  display: none;
}

[data-show-dots='false'] .glider-dots {
  display: none;
}

.reviews-star-filled {
  stop-color: color-mix(in srgb, var(--color-stars, #FFC600) calc(1 * 100%), transparent);
}

.reviews-star-empty {
  stop-color: color-mix(in srgb, var(--color-stars-empty, #C3C5C9) calc(1 * 100%), transparent);
}

[data-content-type="accordion-item"] {
  border-top-width: 1px;
}

[data-content-type="accordion"] [data-collapsible="true"]:after{
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="%23404542" class="w-6 h-6"> <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" /> </svg>');
}

[data-content-type="accordion"] [data-collapsible="true"]:hover:after{
  transform: translateX(-0.25rem);
}

[data-content-type="accordion"] [aria-expanded="true"] [data-collapsible="true"]:after {
  transform: rotate(-180deg);
}

/* purgecss end ignore */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.inset-x-0 {
  left: 0px;
  right: 0px;
}

.inset-x-4 {
  left: 1rem;
  right: 1rem;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.-bottom-px {
  bottom: -1px;
}

.-right-1\.5 {
  right: -0.375rem;
}

.-right-4 {
  right: -1rem;
}

.-top-1\.5 {
  top: -0.375rem;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-4 {
  bottom: 1rem;
}

.bottom-6 {
  bottom: 1.5rem;
}

.left-0 {
  left: 0px;
}

.left-1\/2 {
  left: 50%;
}

.left-4 {
  left: 1rem;
}

.right-0 {
  right: 0px;
}

.right-12 {
  right: 3rem;
}

.right-2 {
  right: 0.5rem;
}

.right-4 {
  right: 1rem;
}

.top-0 {
  top: 0px;
}

.top-12 {
  top: 3rem;
}

.top-2 {
  top: 0.5rem;
}

.top-6 {
  top: 1.5rem;
}

.top-full {
  top: 100%;
}

.isolate {
  isolation: isolate;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-50 {
  z-index: 50;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.order-4 {
  order: 4;
}

.order-first {
  order: -9999;
}

.order-last {
  order: 9999;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.col-span-full {
  grid-column: 1 / -1;
}

.col-start-2 {
  grid-column-start: 2;
}

.col-end-4 {
  grid-column-end: 4;
}

.row-15 {
  grid-row: 15/201;
}

.float-left {
  float: left;
}

.clear-left {
  clear: left;
}

.-m-2 {
  margin: -0.5rem;
}

.-m-4 {
  margin: -1rem;
}

.m-0 {
  margin: 0px;
}

.m-1 {
  margin: 0.25rem;
}

.m-2 {
  margin: 0.5rem;
}

.m-4 {
  margin: 1rem;
}

.m-auto {
  margin: auto;
}

.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.-mx-2 {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}

.-mx-3 {
  margin-left: -0.75rem;
  margin-right: -0.75rem;
}

.-mx-4 {
  margin-left: -1rem;
  margin-right: -1rem;
}

.-mx-6 {
  margin-left: -1.5rem;
  margin-right: -1.5rem;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-6 {
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.\!ml-0 {
  margin-left: 0px !important;
}

.-mb-2\.5 {
  margin-bottom: -0.625rem;
}

.-ml-6 {
  margin-left: -1.5rem;
}

.-ml-px {
  margin-left: -1px;
}

.-mr-1 {
  margin-right: -0.25rem;
}

.-mr-2 {
  margin-right: -0.5rem;
}

.-mr-4 {
  margin-right: -1rem;
}

.-mt-2 {
  margin-top: -0.5rem;
}

.-mt-6 {
  margin-top: -1.5rem;
}

.-mt-8 {
  margin-top: -2rem;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-10 {
  margin-left: 2.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

.ml-7 {
  margin-left: 1.75rem;
}

.ml-auto {
  margin-left: auto;
}

.mr-0 {
  margin-right: 0px;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-10 {
  margin-right: 2.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mr-8 {
  margin-right: 2rem;
}

.mr-auto {
  margin-right: auto;
}

.mt-0 {
  margin-top: 0px;
}

.mt-0\.5 {
  margin-top: 0.125rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-16 {
  margin-top: 4rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-auto {
  margin-top: auto;
}

.box-content {
  box-sizing: content-box;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.table-caption {
  display: table-caption;
}

.table-cell {
  display: table-cell;
}

.table-row {
  display: table-row;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.hidden {
  display: none;
}

.aspect-\[var\(--gallery-ratio\)\] {
  aspect-ratio: var(--gallery-ratio);
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.h-10 {
  height: 2.5rem;
}

.h-11 {
  height: 2.75rem;
}

.h-112 {
  height: 450px;
}

.h-12 {
  height: 3rem;
}

.h-16 {
  height: 4rem;
}

.h-2 {
  height: 0.5rem;
}

.h-3 {
  height: 0.75rem;
}

.h-4 {
  height: 1rem;
}

.h-44 {
  height: 11rem;
}

.h-48 {
  height: 12rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-8 {
  height: 2rem;
}

.h-9 {
  height: 2.25rem;
}

.h-auto {
  height: auto;
}

.h-full {
  height: 100%;
}

.max-h-0 {
  max-height: 0;
}

.max-h-\[min\(1024px\2c \(100\%_-_2rem\)\)\] {
  max-height: min(1024px,(100% - 2rem));
}

.max-h-full {
  max-height: 100%;
}

.max-h-screen {
  max-height: 100vh;
}

.max-h-screen-75 {
  max-height: 75vh;
}

.min-h-14 {
  min-height: 3.5rem;
}

.min-h-64 {
  min-height: 16rem;
}

.min-h-\[20px\] {
  min-height: 20px;
}

.min-h-a11y {
  min-height: 44px;
}

.min-h-screen {
  min-height: 100vh;
}

.w-0 {
  width: 0px;
}

.w-1\/2 {
  width: 50%;
}

.w-1\/4 {
  width: 25%;
}

.w-10 {
  width: 2.5rem;
}

.w-11 {
  width: 2.75rem;
}

.w-12 {
  width: 3rem;
}

.w-16 {
  width: 4rem;
}

.w-2 {
  width: 0.5rem;
}

.w-20 {
  width: 5rem;
}

.w-24 {
  width: 6rem;
}

.w-3 {
  width: 0.75rem;
}

.w-3\/4 {
  width: 75%;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: 1rem;
}

.w-4\/12 {
  width: 33.333333%;
}

.w-40 {
  width: 10rem;
}

.w-44 {
  width: 11rem;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-5\/12 {
  width: 41.666667%;
}

.w-56 {
  width: 14rem;
}

.w-6 {
  width: 1.5rem;
}

.w-60 {
  width: 15rem;
}

.w-64 {
  width: 16rem;
}

.w-7\/12 {
  width: 58.333333%;
}

.w-8 {
  width: 2rem;
}

.w-8\/12 {
  width: 66.666667%;
}

.w-9 {
  width: 2.25rem;
}

.w-\[480px\] {
  width: 480px;
}

.w-\[var\(--gallery-width\)\] {
  width: var(--gallery-width);
}

.w-auto {
  width: auto;
}

.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}

.w-full {
  width: 100%;
}

.w-max {
  width: -moz-max-content;
  width: max-content;
}

.w-screen {
  width: 100vw;
}

.min-w-12 {
  min-width: 3rem;
}

.min-w-20 {
  min-width: 5rem;
}

.min-w-40 {
  min-width: 10rem;
}

.min-w-48 {
  min-width: 12rem;
}

.min-w-\[theme\(spacing\.16\)\] {
  min-width: 4rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-\[25\%\] {
  max-width: 25%;
}

.max-w-\[50\%\] {
  max-width: 50%;
}

.max-w-\[min\(1280px\2c \(100\%_-_2rem\)\)\] {
  max-width: min(1280px,(100% - 2rem));
}

.max-w-full {
  max-width: 100%;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-prose {
  max-width: 65ch;
}

.max-w-screen-2xl {
  max-width: 1770px;
}

.max-w-screen-lg {
  max-width: 1024px;
}

.max-w-xl {
  max-width: 36rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-none {
  flex: none;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink {
  flex-shrink: 1;
}

.shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.flex-grow-0 {
  flex-grow: 0;
}

.grow {
  flex-grow: 1;
}

.grow-0 {
  flex-grow: 0;
}

.table-auto {
  table-layout: auto;
}

.table-fixed {
  table-layout: fixed;
}

.origin-top-left {
  transform-origin: top left;
}

.origin-top-right {
  transform-origin: top right;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-5 {
  --tw-translate-x: -1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-8 {
  --tw-translate-y: -2rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-full {
  --tw-translate-y: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-5 {
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-full {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.cursor-default {
  cursor: default;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.resize {
  resize: both;
}

.scroll-mt-8 {
  scroll-margin-top: 2rem;
}

.list-disc {
  list-style-type: disc;
}

.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.columns-xs {
  -moz-columns: 20rem;
       columns: 20rem;
}

.break-inside-avoid {
  page-break-inside: avoid;
  -moz-column-break-inside: avoid;
       break-inside: avoid;
}

.grid-flow-row {
  grid-auto-flow: row;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.grid-cols-\[auto_minmax\(0px\2c 1fr\)_auto\] {
  grid-template-columns: auto minmax(0px,1fr) auto;
}

.grid-rows-200 {
  grid-template-rows: repeat(200, 1fr) 90px;
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.place-items-center {
  place-items: center;
}

.content-center {
  align-content: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-evenly {
  justify-content: space-evenly;
}

.gap-0\.5 {
  gap: 0.125rem;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-1\.5 {
  gap: 0.375rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-11 {
  gap: 2.75rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-x-0\.5 {
  -moz-column-gap: 0.125rem;
       column-gap: 0.125rem;
}

.gap-x-1 {
  -moz-column-gap: 0.25rem;
       column-gap: 0.25rem;
}

.gap-x-2 {
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}

.gap-x-4 {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}

.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}

.gap-x-7 {
  -moz-column-gap: 1.75rem;
       column-gap: 1.75rem;
}

.gap-x-8 {
  -moz-column-gap: 2rem;
       column-gap: 2rem;
}

.gap-y-0 {
  row-gap: 0px;
}

.gap-y-1 {
  row-gap: 0.25rem;
}

.gap-y-16 {
  row-gap: 4rem;
}

.gap-y-2 {
  row-gap: 0.5rem;
}

.gap-y-3\.5 {
  row-gap: 0.875rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.gap-y-8 {
  row-gap: 2rem;
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-2\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.self-end {
  align-self: flex-end;
}

.self-center {
  align-self: center;
}

.self-stretch {
  align-self: stretch;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-hidden {
  overflow-y: hidden;
}

.overflow-x-scroll {
  overflow-x: scroll;
}

.overscroll-contain {
  overscroll-behavior: contain;
}

.overscroll-y-contain {
  overscroll-behavior-y: contain;
}

.overscroll-x-contain {
  overscroll-behavior-x: contain;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hyphens-auto {
  -webkit-hyphens: auto;
          hyphens: auto;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.break-words {
  overflow-wrap: break-word;
}

.break-all {
  word-break: break-all;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-none {
  border-radius: 0px;
}

.rounded-sm {
  border-radius: 0.125rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-b-md {
  border-bottom-right-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.rounded-l {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.rounded-l-none {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}

.rounded-r {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.rounded-r-none {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-2 {
  border-width: 2px;
}

.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-b-\[3px\] {
  border-bottom-width: 3px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-0 {
  border-left-width: 0px;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-r-0 {
  border-right-width: 0px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-2 {
  border-top-width: 2px;
}

.border-solid {
  border-style: solid;
}

.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity));
}

.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-blue-500, #0C40A5) calc(var(--tw-border-opacity) * 100%), transparent);
}

.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-blue-600, #0C40A5) calc(var(--tw-border-opacity) * 100%), transparent);
}

.border-container {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-containerborder, #e7e7e7) calc(var(--tw-border-opacity) * 100%), transparent);
}

.border-container-darker {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-containerborder-darker, #b6b6b6) calc(var(--tw-border-opacity) * 100%), transparent);
}

.border-container-lighter {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-containerborder-lighter, #f5f5f5) calc(var(--tw-border-opacity) * 100%), transparent);
}

.border-current {
  border-color: currentColor;
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
}

.border-green-400 {
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity));
}

.border-primary {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-border-opacity) * 100%), transparent);
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
}

.border-slate-100 {
  --tw-border-opacity: 1;
  border-color: rgb(241 245 249 / var(--tw-border-opacity));
}

.border-slate-200 {
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
}

.border-slate-300 {
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity));
}

.border-slate-400 {
  --tw-border-opacity: 1;
  border-color: rgb(148 163 184 / var(--tw-border-opacity));
}

.border-transparent {
  border-color: transparent;
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity));
}

.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-blue-600, #0C40A5) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.bg-container {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-container, #fafafa) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.bg-container-darker {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-container-darker, #f5f5f5) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.bg-container-lighter {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-container-lighter, #ffffff) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.bg-container-lighter\/95 {
  background-color: color-mix(in srgb, var(--color-container-lighter, #ffffff) calc(0.95 * 100%), transparent);
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}

.bg-green-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}

.bg-primary {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.bg-primary-lighter {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-primary-lighter, #2D55E3) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.bg-primary\/70 {
  background-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(0.7 * 100%), transparent);
}

.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.bg-secondary {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-secondary, #DC2626) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.bg-shades {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.bg-sky-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 242 254 / var(--tw-bg-opacity));
}

.bg-slate-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.bg-slate-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity));
}

.bg-slate-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity));
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-white\/25 {
  background-color: rgb(255 255 255 / 0.25);
}

.bg-white\/30 {
  background-color: rgb(255 255 255 / 0.3);
}

.bg-white\/60 {
  background-color: rgb(255 255 255 / 0.6);
}

.bg-white\/70 {
  background-color: rgb(255 255 255 / 0.7);
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}

.bg-opacity-100 {
  --tw-bg-opacity: 1;
}

.bg-opacity-25 {
  --tw-bg-opacity: 0.25;
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.bg-gradient-to-l {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-black\/10 {
  --tw-gradient-from: rgb(0 0 0 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white {
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.fill-black\/20 {
  fill: rgb(0 0 0 / 0.2);
}

.fill-current {
  fill: currentColor;
}

.stroke-current {
  stroke: currentColor;
}

.stroke-white {
  stroke: #fff;
}

.stroke-white\/75 {
  stroke: rgb(255 255 255 / 0.75);
}

.stroke-1 {
  stroke-width: 1;
}

.stroke-2 {
  stroke-width: 2;
}

.object-contain {
  object-fit: contain;
}

.object-cover {
  object-fit: cover;
}

.object-center {
  object-position: center;
}

.p-0 {
  padding: 0px;
}

.p-1 {
  padding: 0.25rem;
}

.p-10 {
  padding: 2.5rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-2\.5 {
  padding: 0.625rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-0\.5 {
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-12 {
  padding-left: 3rem;
  padding-right: 3rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-7 {
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pb-12 {
  padding-bottom: 3rem;
}

.pb-16 {
  padding-bottom: 4rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pb-\[calc\(theme\(padding\.4\)_-_3px\)\] {
  padding-bottom: calc(1rem - 3px);
}

.pb-\[var\(--thumb-size\)\] {
  padding-bottom: var(--thumb-size);
}

.pl-1 {
  padding-left: 0.25rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-3\.5 {
  padding-left: 0.875rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-5 {
  padding-left: 1.25rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pl-8 {
  padding-left: 2rem;
}

.pr-1 {
  padding-right: 0.25rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

.pr-7 {
  padding-right: 1.75rem;
}

.pt-1 {
  padding-top: 0.25rem;
}

.pt-10 {
  padding-top: 2.5rem;
}

.pt-16 {
  padding-top: 4rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-2\.5 {
  padding-top: 0.625rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-3\.5 {
  padding-top: 0.875rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-7 {
  padding-top: 1.75rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-start {
  text-align: start;
}

.text-end {
  text-align: end;
}

.align-top {
  vertical-align: top;
}

.align-middle {
  vertical-align: middle;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.italic {
  font-style: italic;
}

.tabular-nums {
  --tw-numeric-spacing: tabular-nums;
  font-feature-settings: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-7 {
  line-height: 1.75rem;
}

.leading-none {
  line-height: 1;
}

.leading-normal {
  line-height: 1.5;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

.text-amber-400 {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-blue-500, #0C40A5) calc(var(--tw-text-opacity) * 100%), transparent);
}

.text-blue-700 {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-blue-700, #0C40A5) calc(var(--tw-text-opacity) * 100%), transparent);
}

.text-blue-900 {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-blue-900, #E2E8F0) calc(var(--tw-text-opacity) * 100%), transparent);
}

.text-current {
  color: currentColor;
}

.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity));
}

.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity));
}

.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity));
}

.text-orange-400 {
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity));
}

.text-primary {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-text-opacity) * 100%), transparent);
}

.text-primary-lighter {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-gray-lighter, #41473D) calc(var(--tw-text-opacity) * 100%), transparent);
}

.text-primary-lightest {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-gray-lightest, #7B8774) calc(var(--tw-text-opacity) * 100%), transparent);
}

.text-primary\/50 {
  color: color-mix(in srgb, var(--color-gray, #363b33) calc(0.5 * 100%), transparent);
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}

.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity));
}

.text-secondary {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-text-opacity) * 100%), transparent);
}

.text-secondary-darker {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-primary-darker, #0D1840) calc(var(--tw-text-opacity) * 100%), transparent);
}

.text-slate-200 {
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}

.text-slate-400 {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}

.text-slate-600 {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.text-slate-700 {
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity));
}

.text-slate-800 {
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity));
}

.text-slate-900 {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity));
}

.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.underline {
  text-decoration-line: underline;
}

.line-through {
  text-decoration-line: line-through;
}

.no-underline {
  text-decoration-line: none;
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-25 {
  opacity: 0.25;
}

.opacity-30 {
  opacity: 0.3;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-60 {
  opacity: 0.6;
}

.opacity-75 {
  opacity: 0.75;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline-dashed {
  outline-style: dashed;
}

.outline-0 {
  outline-width: 0px;
}

.outline-2 {
  outline-width: 2px;
}

.-outline-offset-2 {
  outline-offset: -2px;
}

.outline-offset-2 {
  outline-offset: 2px;
}

.outline-offset-4 {
  outline-offset: 4px;
}

.outline-offset-8 {
  outline-offset: 8px;
}

.outline-blue-300 {
  outline-color: #93c5fd;
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-blue-500\/50 {
  --tw-ring-color: color-mix(in srgb, var(--color-blue-500, #0C40A5) calc(0.5 * 100%), transparent);
}

.ring-primary {
  --tw-ring-opacity: 1;
  --tw-ring-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-ring-opacity) * 100%), transparent);
}

.ring-primary\/50 {
  --tw-ring-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(0.5 * 100%), transparent);
}

.ring-primary\/75 {
  --tw-ring-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(0.75 * 100%), transparent);
}

.ring-red-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity));
}

.ring-opacity-50 {
  --tw-ring-opacity: 0.5;
}

.ring-offset-2 {
  --tw-ring-offset-width: 2px;
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur {
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-none {
  transition-property: none;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-1000 {
  transition-duration: 1000ms;
}

.duration-150 {
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.duration-700 {
  transition-duration: 700ms;
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.mask-overflow {
  --tw-mask-dir: to right;
  --tw-mask-size: 2rem;
  --tw-mask-color: 0 0 0;
  /* rgb color keys */
  --tw-mask-start: rgb(var(--tw-mask-color) / 0%);
  --tw-mask-end: rgb(var(--tw-mask-color) / 0%);
  --tw-mask: linear-gradient(
            var(--tw-mask-dir),
            var(--tw-mask-start),
            rgb(var(--tw-mask-color)) var(--tw-mask-size),
            rgb(var(--tw-mask-color)) calc(100% - var(--tw-mask-size)),
            var(--tw-mask-end)
        );
  -webkit-mask-image: var(--tw-mask);
  mask-image: var(--tw-mask);
}

.mask-overflow-start {
  --tw-mask-end: rgb(var(--tw-mask-color));
}

.mask-overflow-end {
  --tw-mask-start: rgb(var(--tw-mask-color));
}

.mask-dir-y {
  --tw-mask-dir: to bottom;
}

.\[-moz-appearance\:textfield\] {
  -moz-appearance: textfield;
}

.\[border-image\:conic-gradient\(theme\(colors\.slate\.200\)_0_0\)_fill_0\/\/0_100vw\] {
  border-image: conic-gradient(#e2e8f0 0 0) fill 0//0 100vw;
}

.page-footer ul li {
  padding-bottom: 0.75rem;
}

.footer-bottom-block p a {
  display: flex;
}

.page-header {
}

.search_result.category {
  width: 100%;
}

@media (min-width: 768px) {
  .search_result.category {
    width: 25%;
    border-right-width: 1px;
  }
}

.search_result.product {
  flex-grow: 1;
}

.page-main {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-text-opacity) * 100%), transparent);
}

.table-row-items > div.table-row-item {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-container-darker, #f5f5f5) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.table-row-items > div.table-row-item:nth-child(2n + 1) {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-container-lighter, #ffffff) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.cms-page-view .column.main a:not([class$="button-primary"]), [data-content-type='row'] a:not([class$="button-primary"]) {
  text-decoration-line: underline;
}

[data-content-type="banner"] > a[data-element='link'] {
  text-decoration-line: none;
}

form .field.choice.gdpr-js-content, fieldset .field.choice.gdpr-js-content {
  align-items: start;
}

.catalog-category-view .sidebar .filter-option h3 {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.account-nav ul li a, .account-nav ul li strong {
  display: flex;
  justify-content: space-between;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-text-opacity) * 100%), transparent);
}

.account-nav ul li a:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.account-nav ul li strong {
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
  text-decoration-line: underline;
}

.actions-toolbar {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: space-between;
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-containerborder-darker, #b6b6b6) calc(var(--tw-border-opacity) * 100%), transparent);
  padding-top: 1rem;
}

.actions-toolbar a.back {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-primary-darker, #0D1840) calc(var(--tw-text-opacity) * 100%), transparent);
  text-decoration-line: underline;
}

.customer-address-form .page-main .page-title {
  margin-bottom: 1rem;
  width: 100%;
  text-align: center;
}

.customer-address-form .page-main .message {
  margin-top: 1.5rem;
}

.customer-address-form .page-main .message ~ .message {
  margin-top: 0px;
}

.customer-address-form .page-main {
  margin-left: auto;
  margin-right: auto;
  max-width: 1024px;
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-top: 1rem;
}

body > div > div.grecaptcha-badge {
  display: none;
  height: 0px !important;
  width: 0px !important;
}

.order-items > div:nth-child(even) {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-container-darker, #f5f5f5) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.order-links {
  display: block;
}

[data-content-type$='block'] .order-links p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.order-links {
  align-items: center;
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-container, #fafafa) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.order-links li {
  display: inline-block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  cursor: pointer;
  white-space: nowrap;
}

.order-links li.current {
  flex-grow: 1;
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-text-opacity) * 100%), transparent);
}

.order-links li a {
  text-decoration-line: underline;
}

.order-date {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

form .field, fieldset .field {
  margin-top: 0.25rem;
}

/* Reserve space for single line form validation messages */

form .field.field-reserved, fieldset .field.field-reserved {
  margin-bottom: 1.75rem;
}

form .field.field-reserved ul:last-of-type, fieldset .field.field-reserved ul:last-of-type {
  margin-bottom: -1.5rem;
  padding-bottom: 0.25rem;
}

form .field.field-reserved ul, fieldset .field.field-reserved ul {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

form label, fieldset label {
  margin-bottom: 0.25rem;
  display: block;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-gray-lighter, #41473D) calc(var(--tw-text-opacity) * 100%), transparent);
}

[data-content-type$='block'] form label p:last-child,[data-content-type$='block']  fieldset label p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

form .field.choice, fieldset .field.choice {
  display: flex;
  align-items: center;
}

form .field.choice input, fieldset .field.choice input {
  margin-right: 1rem;
}

form .field.choice label, fieldset .field.choice label {
  margin-bottom: 0px;
}

form .field.field-error .messages, fieldset .field.field-error .messages {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
  max-width: -moz-fit-content;
  max-width: fit-content;
}

form legend, fieldset legend {
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-text-opacity) * 100%), transparent);
}

form legend + br, fieldset legend + br {
  display: none;
}

fieldset ~ fieldset {
  margin-top: 1rem;
}

/* For backwards compatibility */

.flex-columns-wrapper {
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .flex-columns-wrapper {
    flex-direction: row;
  }
}

.multirange-slider {
  display: grid;
  grid-template-areas: "stacked";
}

.multirange-slider [type="range"] {
  pointer-events: none;
  grid-area: stacked;
}

.multirange-slider [type="range"]::-webkit-slider-thumb {
  pointer-events: auto;
  isolation: isolate;
}

.multirange-slider [type="range"]::-moz-range-thumb {
  pointer-events: auto;
  isolation: isolate;
}

.menu_mobile {
}

.menu_desktop.decorate_links ul li.level0:hover .__link > span.title:before {
  opacity: 1;
  visibility: visible;
  width: 100%;
}

.menu_desktop.decorate_links ul li.level0 .__link > span.title {
  position: relative;
  display: inline-block;
}

.menu_desktop.decorate_links ul li.level0 .__link > span.title:before {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-gray-lighter, #41473D) calc(var(--tw-bg-opacity) * 100%), transparent);
  bottom: 0;
  left: 0;
  content: "";
  position: absolute;
  width: 0;
  height: 1px;
  transition: all .3s ease;
  opacity: 0;
  visibility: hidden;
}

.menu_desktop.mainmenu .__item .__link > span.title:hover {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-primary-darker, #0D1840) calc(var(--tw-text-opacity) * 100%), transparent);
}

.menu_desktop.mainmenu .__item .__link > span.title:before {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.menu_desktop.mainmenu .__inner-item--level1 {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-bottom-width: 1px;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.menu_desktop.mainmenu .__inner-item--level1:last-child {
  border-width: 0px;
}

.menu_desktop.mainmenu .__inner-item--level1:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-text-opacity) * 100%), transparent);
}

.menu_desktop.mainmenu .__inner-item--level1:hover a {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.checkout-onepage-success .checkout-success-container {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

div[data-content-type="row"] ol {
  list-style: decimal;
}

div[data-content-type="row"] ul {
  list-style: disc;
}

div[data-content-type="row"] li {
  margin: 0.25rem;
  margin-left: 1.5rem;
  padding: 0.25rem;
}

.description-attr-row>tr:nth-child(4n+1),
.description-attr-row>tr:nth-child(4n+2) {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.shadow-series {
  transition: transform .3s ease 0s;
  box-shadow: rgb(0 0 0/2%) 0 0 0 4px, rgb(0 0 0/5%) 0 1px 0 0, rgb(0 0 0/3%) 0 0 8px 0, rgb(0 0 0/10%) 0 20px 30px 0;
  transform: perspective(800px) rotateY(-10deg);
}

.shadow-series:hover {
  transform: perspective(800px) rotateY(0);
}

.reduction-fixed, .reduction-percent {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-availability-available, #009d00) calc(var(--tw-text-opacity) * 100%), transparent);
}

.final-price .price {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-text-opacity) * 100%), transparent);
}

#search_autocomplete .product_attribute .final-price .price {
  width: 100%;
}

.final-price .price:after {
  content: "*";
  margin-left: 0.25rem;
  vertical-align: top;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-gray-lightest, #7B8774) calc(var(--tw-text-opacity) * 100%), transparent);
}

.old-price .price-label {
  display:none;
}

.old-price .price-wrapper {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-text-opacity) * 100%), transparent);
}

#search_autocomplete .product_attribute .old-price .price-wrapper {
  width: 100%;
}

.product-item .price-box .old-price .price-container {
  display: inline;
}

.product-item .price-box .old-price .price-container .price {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-text-opacity) * 100%), transparent);
  text-decoration-line: line-through;
}

#search_autocomplete .product_attribute .product-item .price-box .old-price .price-container .price {
  width: 100%;
}

.product-item .price-box [data-price-type="finalPrice"] {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-text-opacity) * 100%), transparent);
}

#search_autocomplete .product_attribute .product-item .price-box [data-price-type="finalPrice"] {
  width: 100%;
}

.product-item .price-box [data-price-type="finalPrice"] :after {
  content: "*";
  margin-left: 0.25rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-gray-lightest, #7B8774) calc(var(--tw-text-opacity) * 100%), transparent);
}

.product-item .price-box .omnibus-price {
  display: flex;
  gap: 0.25rem;
  font-size: 0.75rem;
  line-height: 1rem;
}

#search_autocomplete .price-box {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

#search_autocomplete .price-box .old-price .price-final_price .price {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-gray-lightest, #7B8774) calc(var(--tw-text-opacity) * 100%), transparent);
  text-decoration-line: line-through;
}

#search_autocomplete .price-box .old-price .price-final_price + span {
  font-weight: 400;
}

#search_autocomplete .price-box .price-final_price .price {
  font-size: 1.125rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-text-opacity) * 100%), transparent);
}

#search_autocomplete .price-box .special-price .price-label, #search_autocomplete .price-box .omnibus-price, #search_autocomplete .reduction-percent, #search_autocomplete .reduction-fixed {
  display: none;
}

#search_autocomplete .product_attribute {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-evenly;
  padding-bottom: 0.5rem;
}

#search_autocomplete .product_attribute .font-bold {
  width: 100%;
}

#maincontent figure {
  margin-left: auto;
  margin-right: auto;
}

#maincontent button.pagebuilder-banner-button {
  display: inline-block !important;
}

.cms-index-index .breadcrumbs {
  display: none;
}

@media (min-width: 1280px) {
  .xl\:btn-size-lg {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

.backdrop\:bg-black\/25::backdrop {
  background-color: rgb(0 0 0 / 0.25);
}

.backdrop\:backdrop-blur-sm::backdrop {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.before\:h-3::before {
  content: var(--tw-content);
  height: 0.75rem;
}

.before\:w-3::before {
  content: var(--tw-content);
  width: 0.75rem;
}

.before\:shrink-0::before {
  content: var(--tw-content);
  flex-shrink: 0;
}

.before\:rounded-full::before {
  content: var(--tw-content);
  border-radius: 9999px;
}

.before\:bg-green-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}

.before\:bg-red-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}

.after\:inset-1::after {
  content: var(--tw-content);
  inset: 0.25rem;
}

.after\:inset-x-0::after {
  content: var(--tw-content);
  left: 0px;
  right: 0px;
}

.after\:bottom-0::after {
  content: var(--tw-content);
  bottom: 0px;
}

.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}

.after\:border-b-2::after {
  content: var(--tw-content);
  border-bottom-width: 2px;
}

.after\:border-transparent::after {
  content: var(--tw-content);
  border-color: transparent;
}

.after\:bg-white::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.after\:opacity-0::after {
  content: var(--tw-content);
  opacity: 0;
}

.after\:transition::after {
  content: var(--tw-content);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.first\:mt-0:first-child {
  margin-top: 0px;
}

.last\:mb-0:last-child {
  margin-bottom: 0px;
}

.last\:mb-6:last-child {
  margin-bottom: 1.5rem;
}

.last\:mr-0:last-child {
  margin-right: 0px;
}

.last\:border-0:last-child {
  border-width: 0px;
}

.last\:border-b:last-child {
  border-bottom-width: 1px;
}

.last\:border-b-0:last-child {
  border-bottom-width: 0px;
}

.last\:pb-0:last-child {
  padding-bottom: 0px;
}

.odd\:bg-slate-100:nth-child(odd) {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.even\:bg-container:nth-child(even) {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-container, #fafafa) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.even\:bg-container-darker:nth-child(even) {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-container-darker, #f5f5f5) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.invalid\:ring-2:invalid {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.invalid\:ring-red-500:invalid {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity));
}

.focus-within\:ring-1:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-within\:ring-2:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-within\:ring-4:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-blue-500:hover {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-blue-500, #0C40A5) calc(var(--tw-border-opacity) * 100%), transparent);
}

.hover\:border-primary:hover {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-border-opacity) * 100%), transparent);
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-blue-700, #0C40A5) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.hover\:bg-container-darker:hover {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-container-darker, #f5f5f5) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.hover\:bg-primary:hover {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.hover\:bg-primary\/10:hover {
  background-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(0.1 * 100%), transparent);
}

.hover\:bg-red-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(252 165 165 / var(--tw-bg-opacity));
}

.hover\:bg-slate-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.hover\:bg-slate-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity));
}

.hover\:bg-slate-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity));
}

.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.hover\:text-black:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-blue-600, #0C40A5) calc(var(--tw-text-opacity) * 100%), transparent);
}

.hover\:text-gray-400:hover {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.hover\:text-gray-500:hover {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.hover\:text-primary:hover {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-text-opacity) * 100%), transparent);
}

.hover\:text-primary-darker:hover {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-gray-darker, #2E332C) calc(var(--tw-text-opacity) * 100%), transparent);
}

.hover\:text-red-600:hover {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}

.hover\:text-red-800:hover {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity));
}

.hover\:text-secondary-darker:hover {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-primary-darker, #0D1840) calc(var(--tw-text-opacity) * 100%), transparent);
}

.hover\:text-yellow-500:hover {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:no-underline:hover {
  text-decoration-line: none;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-none:hover {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-sm:hover {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-primary\/30:hover {
  --tw-shadow-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(0.3 * 100%), transparent);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:\[-moz-appearance\:auto\]:hover {
  -moz-appearance: auto;
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.focus\:absolute:focus {
  position: absolute;
}

.focus\:z-10:focus {
  z-index: 10;
}

.focus\:z-30:focus {
  z-index: 30;
}

.focus\:z-40:focus {
  z-index: 40;
}

.focus\:border-0:focus {
  border-width: 0px;
}

.focus\:border-blue-300:focus {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity));
}

.focus\:border-primary:focus {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-border-opacity) * 100%), transparent);
}

.focus\:border-primary-lighter:focus {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-gray-lighter, #41473D) calc(var(--tw-border-opacity) * 100%), transparent);
}

.focus\:border-red-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
}

.focus\:border-transparent:focus {
  border-color: transparent;
}

.focus\:bg-slate-200:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity));
}

.focus\:bg-white:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.focus\:bg-none:focus {
  background-image: none;
}

.focus\:text-gray-600:focus {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.focus\:text-primary:focus {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-text-opacity) * 100%), transparent);
}

.focus\:text-red-800:focus {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity));
}

.focus\:shadow-primary\/30:focus {
  --tw-shadow-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(0.3 * 100%), transparent);
  --tw-shadow: var(--tw-shadow-colored);
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:outline-red-300:focus {
  outline-color: #fca5a5;
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-blue-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(191 219 254 / var(--tw-ring-opacity));
}

.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity));
}

.active\:bg-blue-700:active {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-blue-700, #0C40A5) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.active\:bg-gray-100:active {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.active\:bg-red-400:active {
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity));
}

.active\:bg-slate-100:active {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.active\:text-gray-500:active {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
}

.active\:text-gray-700:active {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.active\:text-red-900:active {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity));
}

.active\:shadow:active {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.active\:ring-0:active {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:invisible:disabled {
  visibility: hidden;
}

.disabled\:bg-gray-100:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.disabled\:bg-slate-600:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity));
}

.disabled\:text-slate-50:disabled {
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity));
}

.disabled\:opacity-30:disabled {
  opacity: 0.3;
}

.disabled\:opacity-70:disabled {
  opacity: 0.7;
}

.disabled\:opacity-75:disabled {
  opacity: 0.75;
}

.disabled\:shadow-none:disabled {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[open] .group-open\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[open] .group-open\:text-blue-700 {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-blue-700, #0C40A5) calc(var(--tw-text-opacity) * 100%), transparent);
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/filter:hover .group-hover\/filter\:border-primary {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-border-opacity) * 100%), transparent);
}

.group\/filter:hover .group-hover\/filter\:bg-primary {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.group\/filter:hover .group-hover\/filter\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.group\/filter:hover .group-hover\/filter\:after\:opacity-100::after {
  content: var(--tw-content);
  opacity: 1;
}

.group\/filter:focus .group-focus\/filter\:border-primary {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-border-opacity) * 100%), transparent);
}

.group:focus .group-focus\:border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
}

.group\/filter:focus .group-focus\/filter\:bg-primary {
  --tw-bg-opacity: 1;
  background-color: color-mix(in srgb, var(--color-primary, #0C40A5) calc(var(--tw-bg-opacity) * 100%), transparent);
}

.group\/filter:focus .group-focus\/filter\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.group\/filter:focus .group-focus\/filter\:after\:opacity-100::after {
  content: var(--tw-content);
  opacity: 1;
}

.aria-expanded\:border-blue-500[aria-expanded="true"] {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-blue-500, #0C40A5) calc(var(--tw-border-opacity) * 100%), transparent);
}

.aria-expanded\:border-current[aria-expanded="true"] {
  border-color: currentColor;
}

.aria-expanded\:text-blue-500[aria-expanded="true"] {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-blue-500, #0C40A5) calc(var(--tw-text-opacity) * 100%), transparent);
}

.aria-expanded\:text-blue-700[aria-expanded="true"] {
  --tw-text-opacity: 1;
  color: color-mix(in srgb, var(--color-blue-700, #0C40A5) calc(var(--tw-text-opacity) * 100%), transparent);
}

.aria-expanded\:after\:border-inherit[aria-expanded="true"]::after {
  content: var(--tw-content);
  border-color: inherit;
}

.aria-\[current\=page\]\:border-slate-400[aria-current="page"] {
  --tw-border-opacity: 1;
  border-color: rgb(148 163 184 / var(--tw-border-opacity));
}

.aria-\[current\=page\]\:underline[aria-current="page"] {
  text-decoration-line: underline;
}

.aria-\[current\=page\]\:hover\:border-blue-500:hover[aria-current="page"] {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-blue-500, #0C40A5) calc(var(--tw-border-opacity) * 100%), transparent);
}

.group[aria-expanded="true"] .group-aria-expanded\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[aria-expanded="true"] .group-aria-expanded\:text-inherit {
  color: inherit;
}

.data-\[sticky\=false\]\:translate-y-0[data-sticky="false"] {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[active\]\:border-primary[data-active] {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-gray, #363b33) calc(var(--tw-border-opacity) * 100%), transparent);
}

.data-\[has-current\]\:border-slate-400[data-has-current] {
  --tw-border-opacity: 1;
  border-color: rgb(148 163 184 / var(--tw-border-opacity));
}

.data-\[sticky\=true\]\:border-transparent[data-sticky="true"] {
  border-color: transparent;
}

.data-\[sticky\=false\]\:opacity-100[data-sticky="false"] {
  opacity: 1;
}

.data-\[sticky\=true\]\:shadow-2xl[data-sticky="true"] {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[has-current\]\:hover\:border-blue-500:hover[data-has-current] {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-blue-500, #0C40A5) calc(var(--tw-border-opacity) * 100%), transparent);
}

.data-\[has-current\]\:aria-expanded\:border-blue-500[aria-expanded="true"][data-has-current] {
  --tw-border-opacity: 1;
  border-color: color-mix(in srgb, var(--color-blue-500, #0C40A5) calc(var(--tw-border-opacity) * 100%), transparent);
}

.prose-headings\:first\:mt-0:first-child :is(:where(h1, h2, h3, h4, h5, h6, th):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  margin-top: 0px;
}

.prose-h2\:text-lg :is(:where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.prose-h3\:text-base :is(:where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  font-size: 1rem;
  line-height: 1.5rem;
}

.prose-h4\:text-sm :is(:where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.prose-lead\:text-lg :is(:where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.prose-lead\:font-bold :is(:where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  font-weight: 700;
}

@media (min-width: 640px) {
  .sm\:not-sr-only {
    position: static;
    width: auto;
    height: auto;
    padding: 0;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }

  .sm\:order-1 {
    order: 1;
  }

  .sm\:order-2 {
    order: 2;
  }

  .sm\:order-3 {
    order: 3;
  }

  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .sm\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:ml-2 {
    margin-left: 0.5rem;
  }

  .sm\:ml-3 {
    margin-left: 0.75rem;
  }

  .sm\:ml-6 {
    margin-left: 1.5rem;
  }

  .sm\:ml-auto {
    margin-left: auto;
  }

  .sm\:mr-8 {
    margin-right: 2rem;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:block {
    display: block;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:w-1\/2 {
    width: 50%;
  }

  .sm\:w-1\/3 {
    width: 33.333333%;
  }

  .sm\:w-14 {
    width: 3.5rem;
  }

  .sm\:w-20 {
    width: 5rem;
  }

  .sm\:w-48 {
    width: 12rem;
  }

  .sm\:w-5\/6 {
    width: 83.333333%;
  }

  .sm\:w-56 {
    width: 14rem;
  }

  .sm\:w-96 {
    width: 24rem;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:shrink-0 {
    flex-shrink: 0;
  }

  .sm\:grow-0 {
    flex-grow: 0;
  }

  .sm\:table-fixed {
    table-layout: fixed;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:items-end {
    align-items: flex-end;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-8 {
    gap: 2rem;
  }

  .sm\:overflow-hidden {
    overflow: hidden;
  }

  .sm\:p-8 {
    padding: 2rem;
  }

  .sm\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .sm\:py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
  }

  .sm\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .sm\:pb-0 {
    padding-bottom: 0px;
  }

  .sm\:pr-12 {
    padding-right: 3rem;
  }

  .sm\:pt-1 {
    padding-top: 0.25rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-right {
    text-align: right;
  }

  .sm\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .sm\:duration-700 {
    transition-duration: 700ms;
  }
}

@media (min-width: 768px) {
  .md\:visible {
    visibility: visible;
  }

  .md\:bottom-2 {
    bottom: 0.5rem;
  }

  .md\:left-2 {
    left: 0.5rem;
  }

  .md\:right-2 {
    right: 0.5rem;
  }

  .md\:order-3 {
    order: 3;
  }

  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:col-start-1 {
    grid-column-start: 1;
  }

  .md\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .md\:row-start-1 {
    grid-row-start: 1;
  }

  .md\:-mx-4 {
    margin-left: -1rem;
    margin-right: -1rem;
  }

  .md\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .md\:mx-4 {
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .md\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .md\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .md\:my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .md\:-mr-1 {
    margin-right: -0.25rem;
  }

  .md\:-mt-1 {
    margin-top: -0.25rem;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-3 {
    margin-bottom: 0.75rem;
  }

  .md\:ml-0 {
    margin-left: 0px;
  }

  .md\:ml-auto {
    margin-left: auto;
  }

  .md\:mr-0 {
    margin-right: 0px;
  }

  .md\:mr-5 {
    margin-right: 1.25rem;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-10 {
    margin-top: 2.5rem;
  }

  .md\:mt-4 {
    margin-top: 1rem;
  }

  .md\:mt-5 {
    margin-top: 1.25rem;
  }

  .md\:block {
    display: block;
  }

  .md\:inline-block {
    display: inline-block;
  }

  .md\:inline {
    display: inline;
  }

  .md\:flex {
    display: flex;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-24 {
    height: 6rem;
  }

  .md\:h-6 {
    height: 1.5rem;
  }

  .md\:h-auto {
    height: auto;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-1\/3 {
    width: 33.333333%;
  }

  .md\:w-2\/3 {
    width: 66.666667%;
  }

  .md\:w-2\/6 {
    width: 33.333333%;
  }

  .md\:w-20 {
    width: 5rem;
  }

  .md\:w-24 {
    width: 6rem;
  }

  .md\:w-4\/6 {
    width: 66.666667%;
  }

  .md\:w-6 {
    width: 1.5rem;
  }

  .md\:w-\[640px\] {
    width: 640px;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:shrink-0 {
    flex-shrink: 0;
  }

  .md\:-translate-x-1\/3 {
    --tw-translate-x: -33.333333%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-\[42\%_minmax\(0\2c _1fr\)\] {
    grid-template-columns: 42% minmax(0, 1fr);
  }

  .md\:grid-rows-\[min-content_minmax\(0\2c _1fr\)\] {
    grid-template-rows: min-content minmax(0, 1fr);
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-wrap {
    flex-wrap: wrap;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:gap-1 {
    gap: 0.25rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:gap-x-5 {
    -moz-column-gap: 1.25rem;
         column-gap: 1.25rem;
  }

  .md\:gap-x-6 {
    -moz-column-gap: 1.5rem;
         column-gap: 1.5rem;
  }

  .md\:border-0 {
    border-width: 0px;
  }

  .md\:bg-transparent {
    background-color: transparent;
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .md\:px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .md\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .md\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .md\:pl-16 {
    padding-left: 4rem;
  }

  .md\:pl-5 {
    padding-left: 1.25rem;
  }

  .md\:pt-0 {
    padding-top: 0px;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .lg\:sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }

  .lg\:absolute {
    position: absolute;
  }

  .lg\:sticky {
    position: sticky;
  }

  .lg\:inset-x-0 {
    left: 0px;
    right: 0px;
  }

  .lg\:inset-y-0 {
    top: 0px;
    bottom: 0px;
  }

  .lg\:inset-y-auto {
    top: auto;
    bottom: auto;
  }

  .lg\:bottom-0 {
    bottom: 0px;
  }

  .lg\:bottom-4 {
    bottom: 1rem;
  }

  .lg\:bottom-auto {
    bottom: auto;
  }

  .lg\:left-0 {
    left: 0px;
  }

  .lg\:left-4 {
    left: 1rem;
  }

  .lg\:left-\[var\(--msrp-inline-offset\)\] {
    left: var(--msrp-inline-offset);
  }

  .lg\:right-4 {
    right: 1rem;
  }

  .lg\:right-6 {
    right: 1.5rem;
  }

  .lg\:right-auto {
    right: auto;
  }

  .lg\:top-0 {
    top: 0px;
  }

  .lg\:top-2 {
    top: 0.5rem;
  }

  .lg\:top-6 {
    top: 1.5rem;
  }

  .lg\:top-\[var\(--msrp-block-offset\)\] {
    top: var(--msrp-block-offset);
  }

  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:order-first {
    order: -9999;
  }

  .lg\:col-auto {
    grid-column: auto;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .lg\:ml-2 {
    margin-left: 0.5rem;
  }

  .lg\:ml-5 {
    margin-left: 1.25rem;
  }

  .lg\:mr-4 {
    margin-right: 1rem;
  }

  .lg\:mt-3 {
    margin-top: 0.75rem;
  }

  .lg\:mt-8 {
    margin-top: 2rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:inline-block {
    display: inline-block;
  }

  .lg\:inline {
    display: inline;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:table-cell {
    display: table-cell;
  }

  .lg\:table-header-group {
    display: table-header-group;
  }

  .lg\:table-row {
    display: table-row;
  }

  .lg\:grid {
    display: grid;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-1\/3 {
    width: 33.333333%;
  }

  .lg\:w-1\/4 {
    width: 25%;
  }

  .lg\:w-2\/3 {
    width: 66.666667%;
  }

  .lg\:w-56 {
    width: 14rem;
  }

  .lg\:w-80 {
    width: 20rem;
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:max-w-xs {
    max-width: 20rem;
  }

  .lg\:flex-1 {
    flex: 1 1 0%;
  }

  .lg\:table-auto {
    table-layout: auto;
  }

  .lg\:-translate-y-0 {
    --tw-translate-y: -0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:scroll-mt-24 {
    scroll-margin-top: 6rem;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }

  .lg\:grid-cols-\[auto_minmax\(0px\2c 1fr\)_auto\] {
    grid-template-columns: auto minmax(0px,1fr) auto;
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:flex-row-reverse {
    flex-direction: row-reverse;
  }

  .lg\:flex-col {
    flex-direction: column;
  }

  .lg\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .lg\:items-start {
    align-items: flex-start;
  }

  .lg\:justify-start {
    justify-content: flex-start;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:gap-x-10 {
    -moz-column-gap: 2.5rem;
         column-gap: 2.5rem;
  }

  .lg\:gap-y-0 {
    row-gap: 0px;
  }

  .lg\:divide-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(0px * var(--tw-divide-y-reverse));
  }

  .lg\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .lg\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .lg\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .lg\:py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .lg\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .lg\:pb-0 {
    padding-bottom: 0px;
  }

  .lg\:pb-3\.5 {
    padding-bottom: 0.875rem;
  }

  .lg\:pl-24 {
    padding-left: 6rem;
  }

  .lg\:pl-\[var\(--thumb-size\)\] {
    padding-left: var(--thumb-size);
  }

  .lg\:pt-2 {
    padding-top: 0.5rem;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-right {
    text-align: right;
  }

  .lg\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .lg\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .lg\:shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .lg\:mask-dir-y {
    --tw-mask-dir: to bottom;
  }

  .lg\:\[border-image\:none\] {
    border-image: none;
  }
}

@media (min-width: 1280px) {
  .xl\:bottom-8 {
    bottom: 2rem;
  }

  .xl\:left-8 {
    left: 2rem;
  }

  .xl\:right-8 {
    right: 2rem;
  }

  .xl\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .xl\:-mt-12 {
    margin-top: -3rem;
  }

  .xl\:mt-0 {
    margin-top: 0px;
  }

  .xl\:w-1\/2 {
    width: 50%;
  }

  .xl\:w-1\/3 {
    width: 33.333333%;
  }

  .xl\:w-1\/4 {
    width: 25%;
  }

  .xl\:grow {
    flex-grow: 1;
  }

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:border-none {
    border-style: none;
  }

  .xl\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .xl\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

@media (min-width: 1770px) {
  .\32xl\:w-96 {
    width: 24rem;
  }
}

.\[\&\:\:-webkit-calendar-picker-indicator\]\:\!hidden::-webkit-calendar-picker-indicator {
  display: none !important;
}

.\[\&\:\:-webkit-details-marker\]\:hidden::-webkit-details-marker {
  display: none;
}

.\[\&\:\:-webkit-inner-spin-button\]\:invisible::-webkit-inner-spin-button {
  visibility: hidden;
}

.\[\&\:\:-webkit-inner-spin-button\]\:hidden::-webkit-inner-spin-button {
  display: none;
}

.hover\:\[\&\:\:-webkit-inner-spin-button\]\:visible::-webkit-inner-spin-button:hover {
  visibility: visible;
}

.\[\&\:\:-webkit-list-button\]\:w-0::-webkit-list-button {
  width: 0px;
}

.\[\&_\.price-label\]\:sr-only .price-label {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.\[\&_\.price-wrapper\]\:text-inherit .price-wrapper {
  color: inherit;
}

.\[\&_\.price-wrapper\]\:\[font-size\:inherit\] .price-wrapper {
  font-size: inherit;
}

.\[\&_\.price\]\:font-normal .price {
  font-weight: 400;
}

.\[\&_\.price\]\:line-through .price {
  text-decoration-line: line-through;
}

.\[\&_\:is\(input\2c select\2c textarea\)\]\:scroll-my-36 :is(input,select,textarea) {
  scroll-margin-top: 9rem;
  scroll-margin-bottom: 9rem;
}

.\[\&_\>\*\]\:px-2 >* {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
