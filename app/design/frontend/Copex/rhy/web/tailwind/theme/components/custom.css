.checkout-onepage-success {
    .checkout-success-container {
        @apply max-w-screen-xl mx-auto;
    }
}

div[data-content-type="row"] {
    ol {
        list-style: decimal;
    }
    ul {
        list-style: disc;
    }
    li {
        @apply p-1 m-1 ml-6;
    }
}

.description-attr-row>tr:nth-child(4n+1),
.description-attr-row>tr:nth-child(4n+2) {
    @apply bg-white;
}

.shadow-series {
    transition: transform .3s ease 0s;
    box-shadow: rgb(0 0 0/2%) 0 0 0 4px, rgb(0 0 0/5%) 0 1px 0 0, rgb(0 0 0/3%) 0 0 8px 0, rgb(0 0 0/10%) 0 20px 30px 0;
    transform: perspective(800px) rotateY(-10deg);
}

.shadow-series:hover {
    transform: perspective(800px) rotateY(0);
}

.reduction-fixed, .reduction-percent {
    @apply text-availability-available font-semibold text-sm;
}

.final-price {
    .price {
        @apply text-secondary font-bold text-3xl;
    }

    .price:after {
        content: "*";
        @apply text-primary-lightest font-normal ml-1 text-lg align-top;
    }
}

.old-price {
    .price-label { display:none;}
    .price-wrapper {
        @apply text-secondary font-bold text-base;
    }
}

.product-item .price-box {
    .old-price .price-container {
        @apply inline;
        .price {
            @apply text-secondary font-bold text-base line-through;
        }
    }
    [data-price-type="finalPrice"] {
        @apply text-secondary font-bold text-2xl;
        :after {
            content: "*";
            @apply text-primary-lightest font-normal ml-1 text-lg;
        }
    }
    .omnibus-price {
        @apply text-xs flex gap-1;
    }
}
#search_autocomplete {
    .price-box{
        @apply flex justify-end gap-2;
        .old-price .price-final_price .price{
            @apply line-through text-primary-lightest text-sm font-normal;
        }
        .old-price .price-final_price + span {
            @apply font-normal;
        }
        .price-final_price .price{
            @apply text-secondary text-lg;
        }
    }
    .price-box .special-price .price-label, .price-box .omnibus-price, .reduction-percent, .reduction-fixed {
        @apply hidden;
    }
    .product_attribute {
        @apply flex flex-row flex-wrap justify-evenly pb-2;
        .font-bold {
            @apply w-full;
        }
    }
}