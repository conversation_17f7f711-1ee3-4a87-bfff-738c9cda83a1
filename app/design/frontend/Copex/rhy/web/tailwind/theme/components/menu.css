.menu_mobile {
}

.menu_desktop {
    &.decorate_links ul {
        li.level0 {
            &:hover .__link > span.title:before {
                 opacity: 1;
                 visibility: visible;
                 width: 100%;
             }
             .__link > span.title {
                position: relative;
                display: inline-block;
                &:before {
                    @apply bg-shades-lighter;
                     bottom: 0;
                     left: 0;
                     content: "";
                     position: absolute;
                     width: 0;
                     height: 1px;
                     -webkit-transition: all .3s ease;
                     transition: all .3s ease;
                     opacity: 0;
                     visibility: hidden;
                 }
            }
        }
    }
  &.mainmenu {
    .__item .__link > span.title{
        @apply hover:text-secondary-darker;
        &:before {
             @apply bg-primary;
         }
    }
    .__inner-item--level1 {
        @apply hover:bg-gray-100 hover:text-secondary transform transition-all border-b last:border-0;

        &:hover a {
            @apply translate-x-1;
        }
    }
 }
}