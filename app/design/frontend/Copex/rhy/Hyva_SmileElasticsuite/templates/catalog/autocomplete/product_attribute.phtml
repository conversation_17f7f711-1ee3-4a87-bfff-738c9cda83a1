<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);
?>

<template x-if="searchResult.type == 'product_attribute'">
    <a class="btn btn-primary rounded-full inline-flex items-center px-4 py-2 min-w-12 justify-center" :href="searchResult.url" :title="searchResult.title">
        <span class="text-sm" x-text="searchResult.title"></span>
    </a>
</template>

<script>
    function renderEsAutocompleteTitleAttribute(data)
    {
        data = data.filter(function(item) {
            return item.type === 'product_attribute';
        }).map(function(item) {
            return item['attribute_label']
        }).reduce(function(prev, item) {
            if (item in prev) {
                prev[item]++;
            } else {
                prev[item] = 1;
            }
            return prev;
        }, {});

        data = Object.entries(data).sort(function(item1, item2) {
            return item2[1] - item1[1]
        }).map(function(item) {return item[0]});

        if (data.length > 2) {
            data = data.slice(0, 2);
            data.push('...');
        }

        return data.join(', ');
    }
</script>
