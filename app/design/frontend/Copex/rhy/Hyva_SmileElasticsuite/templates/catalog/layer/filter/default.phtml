<?php

use Magento\Catalog\Helper\Data;
use Magento\Framework\Escaper;
use Smile\ElasticsuiteCatalog\Block\Navigation\FilterRenderer;
use Hyva\Theme\Model\LocaleFormatter;

/** @var FilterRenderer $block */
/** @var Escaper $escaper */
/** @var array $filterItems */
/** @var Data $catalogHelper */
/** @var LocaleFormatter $localeFormatter */

$catalogHelper = $this->helper('\Magento\Catalog\Helper\Data');
?>

<ol
    class="items space-y-2.5 my-2"
    aria-label="<?= $escaper->escapeHtmlAttr(__('%1 filter options', $this->getFilter()->getName())) ?>"
>
    <?php foreach ($filterItems as $filterItem): ?>
        <li class="item text-sm">
            <?php if ($filterItem->getCount() > 0): ?>
                <a
                    href="<?= $escaper->escapeUrl($filterItem->getUrl()) ?>"
                    class="group/filter flex gap-2 justify-between items-center hover:text-black outline-offset-4"
                >
                    <?php $selected = $filterItem->getIsSelected(); ?>
                    <span class="flex gap-2 items-center">
                        <?php if ($this->isMultipleSelectEnabled()) : ?>
                            <span class="rounded-sm border mr-2 <?= $selected ? 'bg-primary border-primary' : 'border-slate-300'; ?>">
                                <svg
                                    fill="none"
                                    class="stroke-2 stroke-white <?= $selected ? 'opacity-100' : 'opacity-0'; ?>"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg"
                                    aria-hidden="true"
                                >
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5"></path>
                                </svg>
                            </span>
                            <svg
                                fill="none"
                                class="rounded-sm border <?= $selected ? 'bg-primary border-primary' : 'border-slate-300 bg-white'; ?>  text-white transition
                                    group-hover/filter:bg-primary group-hover/filter:border-primary group-focus/filter:bg-primary group-focus/filter:border-primary"
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                                aria-hidden="true"
                                stroke-width="2"
                                stroke="currentColor"
                            >
                                <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5"></path>
                            </svg>
                        <?php else: ?>
                            <span
                                class="relative inline-flex shrink-0 justify-center items-center border border-slate-300 w-5 h-5 rounded-full bg-white
                                    after:opacity-0 after:absolute after:inset-1 after:bg-white after:rounded-full after:transition
                                    group-hover/filter:bg-primary group-hover/filter:border-primary group-hover/filter:text-white group-hover/filter:after:opacity-100
                                    group-focus/filter:bg-primary group-focus/filter:border-primary group-focus/filter:text-white group-focus/filter:after:opacity-100"
                                aria-hidden="true"
                            ></span>
                        <?php endif; ?>
                        <?= $filterItem->getLabel() ?>
                    </span>
                    <?php if ($catalogHelper->shouldDisplayProductCountOnLayer()): ?>
                        <span class="count text-slate-500 tabular-nums"><?= $filterItem->getCount()?></span>
                        <span class="sr-only"><?= $escaper->escapeHtml(__('products available')) ?></span>
                    <?php endif; ?>
                    <?php if ($selected): ?>
                        <span class="sr-only">
                            <?= $escaper->escapeHtml(__('filter selected')) ?>
                        </span>
                    <?php endif; ?>
                </a>
            <?php else: ?>
                <span class="flex justify-between py-1 hover:text-black">
                    <?= $filterItem->getLabel() ?>
                    <?php if ($catalogHelper->shouldDisplayProductCountOnLayer()): ?>
                        (<span class="count text-slate-500 tabular-nums"><?= $filterItem->getCount()?></span>
                        <span class="sr-only">
                            <?= $escaper->escapeHtml(__('products available')) ?>
                        </span>)
                    <?php endif; ?>
                </span>
            <?php endif; ?>
        </li>
    <?php endforeach ?>
</ol>
