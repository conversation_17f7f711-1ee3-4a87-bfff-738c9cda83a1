<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Search\Helper\Data as SearchHelper;

/**
 * Template for quick search mini form.
 * Overridden to manage template injection for the rendering of autocomplete results.
 *
 * @var \Smile\ElasticsuiteCore\Block\Search\Form\Autocomplete $block
 * @var SearchHelper $helper
 * @var Escaper $escaper
 * @var \Hyva\Theme\Model\ViewModelRegistry $viewModels
 * @var \Hyva\Theme\ViewModel\HeroiconsOutline $heroicons
 */

$helper        = $this->helper(SearchHelper::class);
$suggestionUrl = $helper->getResultUrl() . '?' . $helper->getQueryParamName() . '=';
$heroicons     = $viewModels->require(\Hyva\Theme\ViewModel\HeroiconsOutline::class);
$templates     = json_decode($block->getJsonSuggestRenderers(), true);

?>

<script>
    function initMiniSearchComponent() {
        return {
            show:false,
            formSelector: "#search_mini_form",
            url: "<?= /* @escapeNotVerified */ $block->getUrl('search/ajax/suggest') ?>",
            destinationSelector: "#search_autocomplete",
            templates: <?= /* @noEscape */ $block->getJsonSuggestRenderers() ?>,
            priceFormat: <?= /* @noEscape */ $block->getJsonPriceFormat() ?>,
            minSearchLength: <?= /* @escapeNotVerified */ $helper->getMinQueryLength() ?>,
            searchResultsByType: {},
            currentRequest: null,

            /**
             * Get search results.
             */
            getSearchResults: function () {
                let value = document.querySelector('#search').value.trim();

                if (value.length < parseInt(this.minSearchLength, 10)) {
                    this.searchResultsByType = [];

                    return false;
                }

                let url = this.url + '?' + new URLSearchParams({
                    q: document.querySelector('#search').value,
                    _: Date.now()
                }).toString();

                if (this.currentRequest !== null) {
                    this.currentRequest.abort();
                }
                this.currentRequest = new AbortController();

                fetch(url, {
                    method: 'GET',
                    signal: this.currentRequest.signal,
                }).then((response) => {
                    if (response.ok) {
                        return response.json();
                    }
                }).then((data)  => {
                    this.show = data.length > 0;

                    this.searchResultsByType = data.reduce((acc, result) => {
                        if (! acc[result.type]) acc[result.type] = [];
                        acc[result.type].push(result);
                        return acc;
                    }, {});
                }).catch((error) => {
                    ;
                });
            },
        }
    }
</script>
<search
    id="elasticsuite-search-container"
    x-data="initMiniSearchComponent()"
    @click.away="show = false"
>
    <form class="form minisearch container px-0 max-w-2xl" id="search_mini_form" action="<?= $escaper->escapeUrl($helper->getResultUrl()) ?>" method="get">
        <div class="flex w-full">
            <label class="sr-only" for="search" data-role="minisearch-label">
                <span><?= $escaper->escapeHtml(__('Search')) ?></span>
            </label>
            <input
                id="search"
                x-ref="searchInput"
                type="search"
                autocapitalize="off"
                autocomplete="off"
                autocorrect="off"
                name="<?= $escaper->escapeHtmlAttr($helper->getQueryParamName()) ?>"
                value="<?= $escaper->escapeHtmlAttr($helper->getEscapedQueryText()) ?>"
                placeholder="<?= $escaper->escapeHtmlAttr(__('Search entire store here...')) ?>"
                maxlength="<?= $escaper->escapeHtmlAttr($helper->getMaxQueryLength()) ?>"
                class="w-full border-slate-300 rounded-l-md rounded-r-none"
                @input.debounce.300="getSearchResults()"
                @search-open.window.debounce.10="$el.focus(); $el.select()"
            >
            <button
                type="submit"
                title="<?= $escaper->escapeHtml(__('Search')) ?>"
                class="action search btn justify-center rounded-md text-base hover:shadow-lg active:shadow disabled:shadow-none transition rounded-l-none rounded-r-md shadow-none px-3
                    bg-blue-600 text-white border border-transparent hover:bg-blue-700 focus:ring-blue-200 active:bg-blue-700 disabled:bg-slate-600 disabled:text-slate-50 disabled:opacity-70"
                aria-label="<?= $escaper->escapeHtml(__('Search')) ?>"
            >
                <?= $heroicons->searchHtml('', 24, 24, ['aria-hidden' => 'true']); ?>
            </button>
        </div>
        <div id="search_autocomplete" class="search-autocomplete relative w-full" x-show="show" x-cloak>
            <div class="z-10 absolute bg-white border border-solid border-black w-full flex flex-col">
                <template x-for="(searchResultByType,type) in searchResultsByType">
                    <div class="" :class="type">
                        <template x-if="searchResultByType.hasOwnProperty(0) && templates[searchResultByType[0].type].title && templates[searchResultByType[0].type].titleRenderer === undefined">
                            <div class="font-bold pt-2 pl-2 text-center" x-text="templates[searchResultByType[0].type].title"></div>
                        </template>
                        <template x-if="searchResultByType.hasOwnProperty(0) && templates[searchResultByType[0].type].titleRenderer !== undefined">
                            <div class="font-bold pt-2 pl-2 text-center" x-text="window[templates[searchResultByType[0].type].titleRenderer](searchResultByType)"></div>
                        </template>

                        <template x-for="searchResult in searchResultByType">
                            <div class="hover:bg-slate-100">
                                <?php foreach(json_decode($block->getJsonSuggestRenderers(), true) as $renderer): ?>
                                    <?= $block->getLayout()
                                        ->createBlock('Magento\Framework\View\Element\Template')
                                        ->setTemplate($renderer['template'])
                                        ->setData('suggestion_url', $suggestionUrl)
                                        ->toHtml()
                                    ?>
                                <?php endforeach; ?>
                            </div>
                        </template>
                    </div>
                </template>
            </div>
        </div>
        <?= $block->getChildHtml() ?>
    </form>
</search>
