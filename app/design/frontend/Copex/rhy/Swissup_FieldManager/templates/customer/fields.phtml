<?php
/**
 * Hyvä Themes - Customer Fields Template
 * Alpine.js implementation for Swissup Field Manager module
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */

$fields = $block->getFields();
$formCode = $block->getFormCode();
$destination = $block->getDestination();

if (!$fields || empty($fields)) {
    return;
}
?>

<div x-data="initCustomerFields()" 
     x-init="init()" 
     class="customer-fields-container"
     data-form-code="<?= $escaper->escapeHtmlAttr($formCode) ?>"
     data-destination="<?= $escaper->escapeHtmlAttr($destination) ?>">
     
    <!-- Fields will be rendered by Alpine.js -->
    <template x-for="field in fields" :key="field.attribute_code">
        <div class="field customer-field" 
             :class="getFieldClasses(field)"
             x-show="shouldShowField(field)">
            
            <!-- Field Label -->
            <label :for="getFieldId(field)" 
                   class="label"
                   :class="{ 'required': field.is_required }">
                <span x-text="field.frontend_label"></span>
                <span x-show="field.is_required" class="required-indicator">*</span>
            </label>
            
            <!-- Field Input -->
            <div class="control">
                <!-- Text Input -->
                <template x-if="field.frontend_input === 'text'">
                    <input type="text" 
                           :id="getFieldId(field)"
                           :name="getFieldName(field)"
                           :value="getFieldValue(field)"
                           :required="field.is_required"
                           :maxlength="field.validate_rules?.max_text_length"
                           class="input-text"
                           :class="getInputClasses(field)"
                           :data-validate="getValidationRules(field)">
                </template>
                
                <!-- Textarea -->
                <template x-if="field.frontend_input === 'textarea'">
                    <textarea :id="getFieldId(field)"
                              :name="getFieldName(field)"
                              :required="field.is_required"
                              :rows="field.multiline_count || 3"
                              class="input-text"
                              :class="getInputClasses(field)"
                              :data-validate="getValidationRules(field)"
                              x-text="getFieldValue(field)"></textarea>
                </template>
                
                <!-- Select Dropdown -->
                <template x-if="field.frontend_input === 'select'">
                    <select :id="getFieldId(field)"
                            :name="getFieldName(field)"
                            :required="field.is_required"
                            class="select"
                            :class="getInputClasses(field)"
                            :data-validate="getValidationRules(field)">
                        <option value="" x-show="!field.is_required">
                            <?= $escaper->escapeHtml(__('Please select...')) ?>
                        </option>
                        <template x-for="option in field.options" :key="option.value">
                            <option :value="option.value" 
                                    :selected="option.value == getFieldValue(field)"
                                    x-text="option.label"></option>
                        </template>
                    </select>
                </template>
                
                <!-- Multiselect -->
                <template x-if="field.frontend_input === 'multiselect'">
                    <select :id="getFieldId(field)"
                            :name="getFieldName(field) + '[]'"
                            :required="field.is_required"
                            multiple
                            class="select multiselect"
                            :class="getInputClasses(field)"
                            :data-validate="getValidationRules(field)">
                        <template x-for="option in field.options" :key="option.value">
                            <option :value="option.value" 
                                    :selected="isOptionSelected(field, option.value)"
                                    x-text="option.label"></option>
                        </template>
                    </select>
                </template>
                
                <!-- Boolean/Yes-No -->
                <template x-if="field.frontend_input === 'boolean'">
                    <select :id="getFieldId(field)"
                            :name="getFieldName(field)"
                            :required="field.is_required"
                            class="select"
                            :class="getInputClasses(field)"
                            :data-validate="getValidationRules(field)">
                        <option value="" x-show="!field.is_required">
                            <?= $escaper->escapeHtml(__('Please select...')) ?>
                        </option>
                        <option value="1" :selected="getFieldValue(field) == '1'">
                            <?= $escaper->escapeHtml(__('Yes')) ?>
                        </option>
                        <option value="0" :selected="getFieldValue(field) == '0'">
                            <?= $escaper->escapeHtml(__('No')) ?>
                        </option>
                    </select>
                </template>
                
                <!-- Date -->
                <template x-if="field.frontend_input === 'date'">
                    <input type="date" 
                           :id="getFieldId(field)"
                           :name="getFieldName(field)"
                           :value="getFieldValue(field)"
                           :required="field.is_required"
                           class="input-text"
                           :class="getInputClasses(field)"
                           :data-validate="getValidationRules(field)">
                </template>
                
                <!-- File Upload -->
                <template x-if="field.frontend_input === 'file'">
                    <input type="file" 
                           :id="getFieldId(field)"
                           :name="getFieldName(field)"
                           :required="field.is_required"
                           class="input-file"
                           :class="getInputClasses(field)"
                           :accept="getFileAcceptTypes(field)"
                           :data-validate="getValidationRules(field)">
                </template>
            </div>
            
            <!-- Field Note/Description -->
            <div x-show="field.note" 
                 class="field-note text-sm text-gray-600 mt-1"
                 x-html="field.note"></div>
        </div>
    </template>
</div>

<script>
function initCustomerFields() {
    return {
        fields: <?= $this->getFieldsJson() ?>,
        formCode: '<?= $escaper->escapeJs($formCode) ?>',
        destination: '<?= $escaper->escapeJs($destination) ?>',
        customerData: <?= $this->getCustomerDataJson() ?>,
        
        init() {
            this.injectFieldsIntoForm();
            this.setupFormValidation();
        },
        
        injectFieldsIntoForm() {
            if (!this.destination) {
                return;
            }
            
            // Wait for the target form to be available
            this.waitForElement(this.destination, (targetElement) => {
                this.moveFieldsToDestination(targetElement);
            });
        },
        
        waitForElement(selector, callback) {
            const element = document.querySelector(selector);
            if (element) {
                callback(element);
                return;
            }
            
            const observer = new MutationObserver(() => {
                const element = document.querySelector(selector);
                if (element) {
                    observer.disconnect();
                    callback(element);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        },
        
        moveFieldsToDestination(targetElement) {
            const fieldsContainer = this.$el;
            const fieldsToMove = fieldsContainer.querySelectorAll('.customer-field');
            
            fieldsToMove.forEach(field => {
                targetElement.appendChild(field.cloneNode(true));
            });
            
            // Hide original container
            fieldsContainer.style.display = 'none';
        },
        
        getFieldId(field) {
            return `customer_field_${field.attribute_code}`;
        },
        
        getFieldName(field) {
            return `customer[${field.attribute_code}]`;
        },
        
        getFieldValue(field) {
            if (this.customerData && this.customerData[field.attribute_code] !== undefined) {
                return this.customerData[field.attribute_code];
            }
            return field.default_value || '';
        },
        
        getFieldClasses(field) {
            const classes = ['field', 'customer-field'];
            
            if (field.is_required) {
                classes.push('required');
            }
            
            if (field.frontend_class) {
                classes.push(field.frontend_class);
            }
            
            return classes.join(' ');
        },
        
        getInputClasses(field) {
            const classes = [];
            
            if (field.frontend_input === 'text' || field.frontend_input === 'textarea') {
                classes.push('input-text');
            } else if (field.frontend_input === 'select' || field.frontend_input === 'multiselect' || field.frontend_input === 'boolean') {
                classes.push('select');
            }
            
            if (field.frontend_class) {
                classes.push(field.frontend_class);
            }
            
            return classes.join(' ');
        },
        
        getValidationRules(field) {
            const rules = {};
            
            if (field.is_required) {
                rules.required = true;
            }
            
            if (field.validate_rules) {
                Object.assign(rules, field.validate_rules);
            }
            
            // Add specific validation rules based on field type
            if (field.frontend_input === 'email') {
                rules.email = true;
            }
            
            if (field.validate_rules?.min_text_length) {
                rules['min_text_length'] = field.validate_rules.min_text_length;
            }
            
            if (field.validate_rules?.max_text_length) {
                rules['max_text_length'] = field.validate_rules.max_text_length;
            }
            
            return Object.keys(rules).length > 0 ? JSON.stringify(rules) : '';
        },
        
        getFileAcceptTypes(field) {
            if (field.validate_rules?.file_extensions) {
                return field.validate_rules.file_extensions.split(',').map(ext => `.${ext.trim()}`).join(',');
            }
            return '';
        },
        
        shouldShowField(field) {
            // Add any conditional logic here
            return true;
        },
        
        isOptionSelected(field, optionValue) {
            const fieldValue = this.getFieldValue(field);
            
            if (Array.isArray(fieldValue)) {
                return fieldValue.includes(optionValue);
            }
            
            return fieldValue == optionValue;
        },
        
        setupFormValidation() {
            // Setup form validation if needed
            this.$nextTick(() => {
                const form = document.querySelector(`form[data-form-code="${this.formCode}"]`) || 
                           document.querySelector('form');
                
                if (form && window.mage) {
                    // Initialize Magento validation if available
                    try {
                        require(['mage/validation'], function() {
                            if (form.validation) {
                                form.validation();
                            }
                        });
                    } catch (e) {
                        console.log('Magento validation not available');
                    }
                }
            });
        }
    };
}
</script>

<style>
/* Customer Fields Styling */
.customer-field {
    @apply mb-4;
}

.customer-field .label {
    @apply block text-sm font-medium text-gray-700 mb-1;
}

.customer-field .label.required .required-indicator {
    @apply text-red-500 ml-1;
}

.customer-field .control {
    @apply relative;
}

.customer-field .input-text,
.customer-field .select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary;
}

.customer-field .input-text:focus,
.customer-field .select:focus {
    @apply ring-2 ring-primary ring-opacity-50;
}

.customer-field .multiselect {
    @apply min-h-20;
}

.customer-field .input-file {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary file:mr-4 file:py-1 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-white hover:file:bg-primary-dark;
}

.customer-field .field-note {
    @apply text-sm text-gray-600 mt-1;
}

/* Error states */
.customer-field.mage-error .input-text,
.customer-field.mage-error .select {
    @apply border-red-500 focus:border-red-500 focus:ring-red-500;
}

.customer-field .mage-error {
    @apply text-red-500 text-sm mt-1;
}

/* Required field indicator */
.customer-field.required .label::after {
    content: ' *';
    @apply text-red-500;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .customer-field .input-text,
    .customer-field .select {
        @apply text-base; /* Prevent zoom on iOS */
    }
}
</style>
