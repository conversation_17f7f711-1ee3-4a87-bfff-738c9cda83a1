<?php
/**
 * Hyvä Themes - Address Field Manager Template
 * Alpine.js implementation for Swissup Address Field Manager module
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */

$config = $this->getJsonConfig();
if (!$config || $config === '[]') {
    return;
}
?>

<div x-data="initAddressFieldManager(<?= $escaper->escapeHtmlAttr($config) ?>)" 
     x-init="init()" 
     class="address-field-manager-container">
</div>

<script>
function initAddressFieldManager(config) {
    return {
        config: config || {},
        observer: null,
        processedElements: new Set(),
        
        init() {
            this.processExistingElements();
            this.setupMutationObserver();
        },
        
        processExistingElements() {
            if (!this.config.selectors || !Array.isArray(this.config.selectors)) {
                return;
            }
            
            const selector = this.config.selectors.join(',');
            const elements = document.querySelectorAll(selector);
            
            elements.forEach(element => {
                this.processElement(element);
            });
        },
        
        setupMutationObserver() {
            if (!this.config.mutable) {
                return;
            }
            
            this.observer = new MutationObserver((mutations) => {
                this.debounce(() => {
                    mutations.forEach(mutation => {
                        this.handleMutation(mutation);
                    });
                }, 200)();
            });
            
            this.observer.observe(document.body, {
                childList: true,
                characterData: true,
                subtree: true
            });
        },
        
        handleMutation(mutation) {
            let target;
            
            switch (mutation.type) {
                case 'childList':
                    target = mutation.target;
                    break;
                case 'characterData':
                    target = mutation.target.parentNode;
                    break;
                default:
                    return;
            }
            
            if (target && this.shouldProcessElement(target)) {
                this.cleanupAddressText(target);
            }
        },
        
        processElement(element) {
            if (!element || this.processedElements.has(element)) {
                return;
            }
            
            this.processedElements.add(element);
            
            if (element.tagName === 'SELECT') {
                this.cleanupAddressSelect(element);
            } else {
                this.cleanupAddressText(element);
                
                if (this.config.mutable) {
                    this.observeElement(element);
                }
            }
        },
        
        shouldProcessElement(element) {
            if (!this.config.selectors || !Array.isArray(this.config.selectors)) {
                return false;
            }
            
            return this.config.selectors.some(selector => {
                try {
                    return element.matches && element.matches(selector);
                } catch (e) {
                    return false;
                }
            });
        },
        
        observeElement(element) {
            if (this.observer) {
                this.observer.observe(element, {
                    childList: true,
                    characterData: true,
                    subtree: true
                });
            }
        },
        
        cleanupAddressSelect(element) {
            const options = element.querySelectorAll('option');
            
            options.forEach(option => {
                const address = option.textContent || '';
                const cleanAddress = address.replace(/(,\s*){2,}/g, ', ');
                option.textContent = cleanAddress;
            });
        },
        
        cleanupAddressText(element) {
            if (!element || !element.childNodes) {
                return;
            }
            
            // Unwrap existing cleanup spans
            element.querySelectorAll('span.afm-br br').forEach(br => {
                const span = br.parentNode;
                if (span && span.classList.contains('afm-br')) {
                    span.replaceWith(br);
                }
            });
            
            element.querySelectorAll('span.afm-comma').forEach(span => {
                span.replaceWith(document.createTextNode(', '));
            });
            
            const children = Array.from(element.childNodes);
            
            children.forEach((current, index) => {
                if (current.nodeType === Node.COMMENT_NODE) {
                    return;
                }
                
                // Hide Yes/No field values
                if (this.isYesNoField(current)) {
                    this.wrapInHiddenSpan(current, 'afm-hidden');
                    return;
                }
                
                const prev = this.getPreviousVisibleSibling(current);
                const next = this.getNextVisibleSibling(current);
                
                if (this.isComma(current)) {
                    if (this.isComma(prev) || this.isBr(prev) || this.isBr(next)) {
                        this.wrapInHiddenSpan(current, 'afm-comma afm-hidden');
                    }
                } else if (this.isBr(current)) {
                    if (this.isBr(prev)) {
                        this.wrapInHiddenSpan(current, 'afm-br afm-hidden');
                    }
                }
            });
        },
        
        wrapInHiddenSpan(node, className) {
            if (node.parentNode && !node.parentNode.classList?.contains('afm-hidden')) {
                const span = document.createElement('span');
                span.className = className;
                span.style.display = 'none';
                
                node.parentNode.insertBefore(span, node);
                span.appendChild(node);
            }
        },
        
        isBr(element) {
            if (!element) {
                return true;
            }
            return element.nodeType === Node.ELEMENT_NODE && element.tagName === 'BR';
        },
        
        isComma(element) {
            if (!element) {
                return false;
            }
            return element.nodeType === Node.TEXT_NODE && element.nodeValue === ', ';
        },
        
        isYesNoField(element) {
            if (!element) {
                return false;
            }
            
            if (element.nodeType === Node.TEXT_NODE) {
                const value = element.nodeValue?.trim();
                return value === 'Yes' || value === 'No' || value === 'Disabled';
            }
            
            return false;
        },
        
        isVisible(element) {
            if (!element) {
                return false;
            }
            
            switch (element.nodeType) {
                case Node.ELEMENT_NODE:
                    if (this.isBr(element)) {
                        return true;
                    }
                    const text = element.textContent?.trim();
                    return text && this.isElementVisible(element);
                    
                case Node.TEXT_NODE:
                    return element.nodeValue?.trim();
                    
                case Node.COMMENT_NODE:
                    return false;
                    
                default:
                    return false;
            }
        },
        
        isElementVisible(element) {
            try {
                const style = window.getComputedStyle(element);
                return style.display !== 'none' && 
                       style.visibility !== 'hidden' && 
                       style.opacity !== '0';
            } catch (e) {
                return true;
            }
        },
        
        getPreviousVisibleSibling(element) {
            let sibling = element.previousSibling;
            
            while (sibling) {
                if (this.isVisible(sibling)) {
                    return sibling;
                }
                sibling = sibling.previousSibling;
            }
            
            return null;
        },
        
        getNextVisibleSibling(element) {
            let sibling = element.nextSibling;
            
            while (sibling) {
                if (this.isVisible(sibling)) {
                    return sibling;
                }
                sibling = sibling.nextSibling;
            }
            
            return null;
        },
        
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        destroy() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
            this.processedElements.clear();
        }
    };
}

// Global cleanup function for compatibility
window.addressFieldManagerCleanup = function(selectors, mutable = false) {
    const manager = initAddressFieldManager({
        selectors: Array.isArray(selectors) ? selectors : [selectors],
        mutable: mutable
    });
    manager.init();
    return manager;
};
</script>

<style>
/* Address Field Manager Styling */
.afm-hidden {
    display: none !important;
}

.afm-br,
.afm-comma {
    display: none;
}

/* Ensure proper spacing in address blocks */
address {
    line-height: 1.5;
}

address br + br {
    display: none;
}

/* Clean up multiple commas */
address .afm-comma + .afm-comma {
    display: none;
}

/* Responsive address display */
@media (max-width: 640px) {
    address {
        font-size: 0.875rem;
        line-height: 1.4;
    }
}
</style>
