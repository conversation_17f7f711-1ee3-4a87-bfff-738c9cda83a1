.firecheckout {
    .label[id^="tooltip"] {
        display: none;
    }
    .payment-method-title{
        border-color: #f4f4f4;
    }
    .table-checkout-shipping-method tbody {
        td:first-child{
            flex: none;
        }
        td:last-child {
            display: none;
        }

        td.col-price {
            width: 100px;
            flex: none;
        }
    }
    #maincontent .authentication-wrapper .action-auth-toggle{
        padding: 0 12px;
    }
    #mp-extra-fee-shipping {
        dl.fieldset {
            margin: 5px 0 0 !important;
        }
        .item-title {
            padding-top: 5px;
            border-top: 1px solid #f4f4f4;

            .label {
                display: none;
            }
        }
        .label {
            .price {
                width: 100px;
                display: inline-block;
            }
            > span {
                font-size: 13px;
                padding: 10px;
                font-weight:bold;
            }
        }
    }
    &.checkout-index-index.fc-form-hide-labels #checkout .checkout-billing-address .fieldset div.field-select-billing > .label {
        opacity: 1;
        max-width: 100%;
        left: 0;
        position: relative !important;
        top: 0;
        overflow: visible;
        margin: 10px 0 0;
    }
    .table-totals {
        .total-old-price{
            * {
                border-top: 0;
                padding: 4px 0;
            }
            td {
                text-align: right;
                display: block;
            }
        }
        .total-percentage td{
            border-top:0; padding: 4px 0;
        }
    }
}

.checkout-onepage-success {
    .col.image {display: none;}
    .col.name .product-image-container {
        display: inline;
        min-width: unset;
        .product-image-wrapper {
            padding:0 !important;
            display: inline-block;
            position: relative;
            float: left;
            height: inherit;
        }
        .product-image-photo {
            float: left;
            position: relative;
            top: unset;
            left: unset;
        }
    }
    .payment-method {
        word-break: break-word;
    }
    .checkout-success-container .order-details-items.ordered .actions-toolbar .action.back{
        display: none;
    }
}
