@desktop: ~"only screen and (min-width: 992px) and (max-width: 1199px)";
@tablet: ~"only screen and (min-width: 768px) and (max-width: 991px)";
@phone: ~"only screen and (max-width: 767px)";

@border_color_4: #dae2e6;

.fotorama__caption{
  visibility: hidden !important;
}

.fotorama--fullscreen .fotorama__fullscreen-icon:focus:after{
  box-shadow: none;
}
.fotorama--fullscreen .fotorama__fullscreen-icon::before {
  color: #333;
  content: '\f81a';
}

.fotorama--fullscreen .fotorama__nav.fotorama__nav--thumbs {
  text-align: center;
}

.page-layout-1column .product-info-top {
  display: grid;
  grid-template-columns: 500px auto 400px;
  column-gap: 20px;

  > div, .product.media {
    width: 100%;
  }

  .product.media {
    grid-area: 1  e("/") 1  e("/") span 2  e("/") 1;
    padding-right: 20px;
    .loading-mask {
      position: absolute;
    }
  }

  .product-info-head.product-info-main {
    grid-area: 1  e("/") 2  e("/") 1  e("/") 2;
    margin-bottom:0;
  }

  .product-info-main {
    grid-area: 1  e("/") 2  e("/") 2  e("/") 2;
    .product.attribute {
      .type {
        &::after {
          content: ':';
        }
      }
      &.workdays{
        > div {
          display: inline-block;
        }
        .type {
          margin-right: 2px;
        }
      }
    }
    .product.attribute .type {
      font-weight: bold;
    }
    .product-info-delivery {
      .stock.available .label {
        display: inline-block;
          font-weight: bold;
      }
      span {
          display: inline-block !important;
          font-weight: 400;
      }
    }
    .seo-link a{
      color: #000;
      font-weight: 600;
    }
  }

  .product-info-additional-column {
    grid-area: 1  e("/") 3  e("/") span 2  e("/") 3;
    .product-info-price {
      margin-top: 10px;
    }
  }
}

body.catalog-product-view {
  .old-price {
    display: block;
  }

    .product-shipping {
      .headline {
        display: none;
        em {
          font-size: 20px;
          vertical-align: middle;
          margin: 0 20px 0 10px;
        }
      }
      @media @phone {
        .headline {
          display: block;
          width: 100%;
        }
      }

    }

  .clearer {
    border-bottom: 1px solid @border_color_4;
  }
  .product.info.detailed{
    .block.related{
      .title {
        margin-top: 0;
      }
      border-bottom: none;
      .block-actions{
        button {
          height: 30px;
        }
      }
    }
  }
  .block.related{
    border-bottom: 1px solid @border_color_4;
    .title {
      margin: 10px 0;
    }
    .block-actions{
      button {
        font-size: 1rem;
        font-weight: 600;
        height: auto;
      }
    }
    .products-grid.products-related .product-item-info {
      width: auto;
      vertical-align: top;
      > a{
        margin-right: 10px;
        float: left;
        img {
          max-width: 100px;
        }
      }
      .product-item-name {
        height: inherit;
        white-space: normal;
      }
      .product-item-details {
        text-align: left;
        padding: 0;
        float: left;
        .related-price {
          display: inline-block;
          position: relative;
          .price-final_price{
            display: inline-block;
            margin-right: 20px;
          }
          .choice.related {
            position: absolute;
            right: 0;
            top: 5px;
            left: auto;
          }
        }
      }
    }
    .product-item .checkbox.related.crosssell {
      display: none;
    }
  }

  .product.attribute.description {
    padding: 20px;
    margin: 20px 0;
  }

  .product.media .image-360 {
    margin: 1px;
  }

  @media @desktop, @tablet {
    &.page-layout-1column .product-info-top {
      grid-template-columns: 500px auto;
      .product.media {
        width: 100%;
        grid-area: 1  e("/") 1  e("/") 4  e("/") 1;
      }
      .product-info-head.product-info-main {
        grid-area: 1  e("/") 2  e("/") 1  e("/") 2;
      }
      .product-info-main {
        grid-area: 3  e("/") 2  e("/") 3  e("/") 2;
      }
      .product-info-additional-column {
        grid-area: 2  e("/") 2  e("/") 2  e("/") 2;
      }
    }
  }

  @media @tablet {
    .moved-add-to-links {display: inline-block;margin-top: 0;}
  }

  @media @phone {
    &.page-layout-1column .product-info-top {
      grid-template-columns: 1fr;
      .product.media {
        width: 100%;
        grid-area: 1  e("/") 1  e("/") 1  e("/") 1;
      }
      .product-info-head.product-info-main {
        grid-area: 2  e("/") 1  e("/") 2  e("/") 1;
      }
      .product-info-main {
        grid-area: 4  e("/") 1  e("/") 4  e("/") 1;
      }
      .product-info-additional-column {
        grid-area: 3  e("/") 1  e("/") 3  e("/") 1;
      }
    }
  }

  .products-grid.products-upsell .product-item-name {
    height: inherit;
  }
}

.catalog-product-view {
  .stock.unavailable,.stock.unavailable-backorder{
    &+ .product.attribute.delivery_time {
      display: none;
    }
  }
}

.catalog-product-view {
    .product-info-delivery {
        & .stock.available {
            margin-left: 0 !important;
            padding: 0 !important;
        }
    }
}

.catalog-category-view .product-image-photo.default_image {width: unset;}

.catalog-product-view {
    .product-info-delivery {
        & .stock.available {
            margin-left: 0 !important;
            padding: 0 !important;
        }
    }
}
