// /**
//  * Copyright © 2015 Magento. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//    Common
//--------------------------------------

& when (@media-common = true) {

//
//    Using buttons mixins
//--------------------------------------
button,
a.action.primary {
    .lib-css(border-radius, @button__border-radius);
    min-height: 38px;
    line-height: 22px;
    font-weight: 400;
}

button.action.switch {
    height: auto;
}

button {
    &:not(.primary) {
        border: 1px solid #ccc;
        color: #777;
        line-height: 30px;
        padding: 0 12px;
        height: 32px;
        background-color: #fff;
    }
    &:hover {
        color: #fff;
        background-color: @theme-color;
        border-color: @theme-color;
    }
}

body {
    &:not(._keyfocus) {
        button {
            &:focus {
                box-shadow: none;
            }
        }
    }
}

a.action.primary {
    .lib-link-as-button();
}

.action.primary {
    .lib-button-primary();
    font-weight: 400;
    line-height: 22px;
}

#discount-coupon-form {
    #coupon_code {
        border-radius: 0 3px 3px 0;
    }
}

}
