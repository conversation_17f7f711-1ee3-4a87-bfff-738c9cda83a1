<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/** @var \Magento\Review\Block\Product\ReviewRenderer $block */

$url = $block->getReviewsUrl() . '#reviews';
$urlForm = $block->getReviewsUrl() . '#review-form';
?>
<?php if ($block->isReviewEnabled() && $block->getReviewsCount()) : ?>
    <?php $rating = $block->getRatingSummary(); ?>
    <?php $rating = $rating instanceof \Magento\Framework\DataObject ? $rating->getRatingSummary() : $rating; ?>
    <div class="product-reviews-summary<?= !$rating ? ' no-rating' : '' ?>" itemprop="aggregateRating" itemscope itemtype="http://schema.org/AggregateRating">
        <?php if ($rating) :?>
        <div class="rating-summary">
             <span class="label"><span><?= $block->escapeHtml(__('Rating')) ?>:</span></span>
             <div class="rating-result" title="<?= $block->escapeHtmlAttr($rating); ?>%">
                 <span style="width:<?= $block->escapeHtmlAttr($rating); ?>%">
                     <span>
                         <span itemprop="ratingValue"><?= $block->escapeHtml($rating); ?></span>% of <span itemprop="bestRating">100</span>
                     </span>
                 </span>
             </div>
         </div>
        <?php endif;?>
        <div class="reviews-actions">
            <a class="action view"
               href="<?= $block->escapeUrl($url) ?>">
                <span itemprop="reviewCount"><?= $block->escapeHtml($block->getReviewsCount()) ?></span>&nbsp;
                <span><?= ($block->getReviewsCount() == 1) ? $block->escapeHtml(__('Review')) : $block->escapeHtml(__('Reviews')) ?></span>
            </a>
            <a class="action add" href="<?= $block->escapeUrl($urlForm) ?>"><?= $block->escapeHtml(__('Add Your Review')) ?></a>
        </div>
    </div>
<?php elseif ($block->isReviewEnabled() && $block->getDisplayIfEmpty()) : ?>
    <div class="product-reviews-summary empty">
        <div class="reviews-actions">
            <a class="action add" href="<?= $block->escapeUrl($urlForm) ?>">
                <?= $block->escapeHtml(__('Be the first to review this product')) ?>
            </a>
        </div>
    </div>
<?php endif; ?>
