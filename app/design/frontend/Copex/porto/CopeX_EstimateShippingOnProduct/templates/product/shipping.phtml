<?php
/** @var $block \Magento\Backend\Block\Template */
/** @var $viewModel CopeX\EstimateShippingOnProduct\ViewModel\View */
/** @var \Magento\Framework\Escaper $escaper */

$viewModel = $block->getViewModel();
if ($viewModel->isVisible()) :
$rates = $viewModel->getRates(); ?>
<?php if ($rates) : ?>
<script>
    lazyLoadJs('<?= $escaper->escapeUrl($block->getViewFileUrl("CopeX_AlpineJs::js/alpine.js")); ?>');
</script>
<div class="product-series__selection">
    <div class="product-series__section--shipping"
    >
        <div class="label"><?= __('Shipping Methods') ?></div>
        <form id="product-shipping-method" class="options" x-data="{ data:
            <?= $block->escapeHtml($viewModel->convertToJson($rates)); ?>, open: false, selected: '<?= current(current($rates))['code']?>' }" @change-current-rate.window="selected=$event.detail.selected">
                <div class="open" @click="open = ! open"><span
                            x-html="'<div>' + data[selected].title + data[selected].price_formated + '</div>'"
                            class="selected"
                    ></span>
                    <div class="triangle">
                        <div class="over-triangle"></div>
                    </div>
                </div>
                <div style="display: none;" x-show="open" @click.outside="open = false">
                    <template x-for="rate in data">
                        <div class="dropdown-item" @click="open=false">
                            <input name="shipping-method" class="form-check-input" type="radio"
                                   :id="rate.code"
                                   :value="rate.code"
                                   x-model="selected"
                                   :data-extended-shipping-time="rate.extended_shipping_time"
                                   :data-shipping-carrier-code="rate.carrier"
                                   :data-shipping-method-code="rate.method"
                                   :data-price="rate.price">
                            <label class="form-check-label" :for="rate.code" x-html="'<span class=\'title\'>' + rate.title + '</span>' +  rate.price_formated"></label>
                        </div>
                    </template>
                </div>
        </form>
        <?php if ($viewModel->allowOtherCountries()) : ?>
            <div class="product-shipping__other-countries" >
                <button name="btn-estimate-shipping" id="btn-estimate-shipping-popup" type="button" title="<?= __('Estimate shipping for other country') ?>" class="action tocart"><?= __('Estimate shipping for other country') ?></button>
                <div style="display: none" id="product-shipping__shipping-modal">
                    <div class="message"><?= __('Please choose your country'); ?></div>
                    <div class="country-select">
                        <?= $block->getLayout()->getBlockSingleton(\Magento\Directory\Block\Data::class)->getCountryHtmlSelect(); ?>
                    </div>
                    <input type="hidden" name="product_id" id="product_id" value="<?=$viewModel->getProduct()->getId(); ?>" />
                    <button name="btn-estimate-shipping" id="btn-estimate-shipping" type="button" title="<?= __('Estimate shipping for other country') ?>" class="action tocart"><?= __('Estimate shipping for other country') ?></button>
                    <ul id="btn-estimate-shipping-results"></ul>
                </div>
            </div>
        <?php endif; ?>
        <script type="text/x-magento-init">
            {
                "*": {
                     "shippingEstimate": {
                        "productId": <?= $viewModel->getProduct()->getId() ?>,
                        "useExtraPriceBox": <?= $viewModel->useExtraPriceBox() ? "true" : "false" ?>,
                        "updateQuote": <?= $viewModel->updateQuote() ? "true" : "false" ?>
                     }
                }
            }
        </script>
    </div>
</div>
<?php endif; ?>
<?php endif; ?>
