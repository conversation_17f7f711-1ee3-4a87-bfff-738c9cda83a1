<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis

/**
 * Product additional attributes template
 *
 * @var $block \Magento\Catalog\Block\Product\View\Attributes
 * @var $viewModel Groupes
 */

use CopeX\ProductAttributeGrouping\ViewModel\Groupes;

?>
<?php
    $_helper = $this->helper(Magento\Catalog\Helper\Output::class);
    $_product = $block->getProduct();
    $viewModel = $this->getViewModel();
    $i = 0;
    $groupes = $viewModel->getGroupes($block->getProduct());
?>
<?php foreach($groupes as $group) : ?>
<?php if ($_additional = $block->getAdditionalData($group[Groupes::EXCLUDES])) :?>
    <div class="additional-attributes-wrapper table-wrapper">
        <div class="container data table additional-attributes col-12" id="product-attribute-specs-table<?= ++$i ?>">
            <div class="heading"><?= $block->escapeHtml(__($group['label'])) ?></div>
            <div class="row">
            <?php foreach ($_additional as $_data) :?>
                <div class="col-md-6 col-sm-12">
                    <div class="row">
                        <div class="col label col-4"><?= $block->escapeHtml($_data['label']) ?></div>
                        <div class="col data col-8"><?= /* @noEscape */ $_helper->productAttribute($_product, $_data['value'], $_data['code']) ?></div>
                    </div>
                </div>
            <?php endforeach; ?>
            </div>
        </div>
    </div>
<?php endif;?>
<?php endforeach; ?>