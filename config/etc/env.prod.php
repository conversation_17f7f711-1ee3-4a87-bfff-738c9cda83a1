<?php
return [
    'modules'          =>
        [
            'ADM_QuickDevBar'                    => 0,
            'Yireo_Whoops'                       => 0,
            'MagePal_PreviewCheckoutSuccessPage' => 0,
            'Smile_DebugToolbar'                 => 0,
            'MSP_Common'                         => 0,
            'MSP_DevTools'                       => 0,
            'Ess_M2ePro'                         => 1,
            'M2E_Core'                           => 1,
            'M2E_Otto'                           => 1,
            'Smile_ElasticsuiteCms'              => 1,
            'Salecto_MediaStorageSync'           => 0,
        ],
    'backend'          =>
        [
            'frontName' => 'backoffice',
        ],
    'install'          => [
        'date' => 'Mon, 16 Mar 2020 08:43:05 +0000',
    ],
    'crypt'            => [
        'key' => '84e9d81a82c98652d3e795c6db22f980',
    ],
    'session'          =>
        [
            'save'  => 'redis',
            'redis' =>
                [
                    'host'                  => '127.0.0.1',
                    'port'                  => '6379',
                    'password'              => '',
                    'timeout'               => '2.5',
                    'persistent_identifier' => '',
                    'database'              => '2',
                    'compression_threshold' => '2048',
                    'compression_library'   => 'gzip',
                    'log_level'             => '1',
                    'max_concurrency'       => '20',
                    'break_after_frontend'  => '15',
                    'break_after_adminhtml' => '30',
                    'first_lifetime'        => '600',
                    'bot_first_lifetime'    => '60',
                    'bot_lifetime'          => '7200',
                    'disable_locking'       => '0',
                    'min_lifetime'          => '60',
                    'max_lifetime'          => '2592000',
                ],
        ],
    'cache'            =>
        [
            'frontend' =>
                [
                    'default'    =>
                        [
                            'backend'         => 'Cm_Cache_Backend_Redis',
                            'backend_options' =>
                                [
                                    'server' => '127.0.0.1',
                                    'port'   => '6379',
                                ],
                        ],
                    'page_cache' =>
                        [
                            'backend'         => 'Cm_Cache_Backend_Redis',
                            'backend_options' =>
                                [
                                    'server'        => '127.0.0.1',
                                    'port'          => '6379',
                                    'database'      => '1',
                                    'compress_data' => '0',
                                ],
                        ],
                ],
        ],
    'db'               =>
        [
            'table_prefix' => '',
            'connection'   =>
                [
                    'default' =>
                        [
                            'host'     => '**************',
                            'dbname'   => 'usrdb_m2xxxrbb_m2',
                            'username' => 'm2xxxrbb',
                            'password' => 'EiUk8Re468bTVdLDxP#',
                            'active'   => '1',
                        ],
                ],
        ],
    'resource'         =>
        [
            'default_setup' =>
                [
                    'connection' => 'default',
                ],
        ],
    'x-frame-options'  => 'SAMEORIGIN',
    'MAGE_MODE'        => 'production',
    'cache_types'      =>
        [
            'config'                 => 1,
            'layout'                 => 1,
            'block_html'             => 1,
            'collections'            => 1,
            'reflection'             => 1,
            'db_ddl'                 => 1,
            'eav'                    => 1,
            'config_integration'     => 1,
            'config_integration_api' => 1,
            'full_page'              => 1,
            'translate'              => 1,
            'config_webservice'      => 1,
            'compiled_config'        => 1,
            'customer_notification'  => 1,
            'elasticsuite'           => 1,
        ],
    'directories'      =>
        [
            'document_root_is_pub' => true,
        ],
    'system'           => [
        'default' => [
//            'web' =>[
//                'secure' => [
//                    'base_url' => 'http://m2.rottner.services/',
//                    'base_link_url' => 'http://m2.rottner.services/'
//                ],
//                'unsecure' => [
//                    'base_url' => 'http://m2.rottner.services/',
//                    'base_link_url' => 'http://m2.rottner.services/'
//                ]
//            ],
//            'smile_elasticsuite_core_base_settings' => [
//                'es_client' => [
//                    'servers' => 'search:9200',
//                    'enable_https_mode' => '0',
//                    'enable_http_auth' => '0',
//                    'http_auth_user' => '',
//                    'http_auth_pwd' => '',
//                ],
//            ],
//            'catalog'                               => [
//                'search' => [
//                    'engine' => 'elasticsuite',
//                ],
//            ],

'xtcore'       => [
    'adminnotification' => [
        'enabled' => 0,
    ],
],
'pimcore'      => [
    'default' => [
        'base_url' => 'https://pim.rottner.services/',
        'api_key'  => '0:3:66dp/WAUqTNV1I5f73y5PScBMyzGbtRNE2MB+KqgGlzOlg6JpbRAq4cq8K9MZNliCYrD/Ir0CCbkU9LV',
    ],
],
'dev'          => [
    'js'       => [
        'enable_js_bundling'          => '0',
        'merge_files'                 => '0',
        'minify_files'                => '1',
        'enable_magepack_js_bundling' => 1,
        'move_script_to_bottom'       => 0,
    ],
    'css'      => [
        'minify_files'    => '1',
        'merge_css_files' => '0',
    ],
    'template' => [
        'minify_html'   => '0',
        'allow_symlink' => '1',
    ],
    'image'    => [
        'default_adapter' => 'GD2',
    ],
    'static'   => [
        'sign' => 1,
    ],
],
'system'       => [
    'backup' => [
        'functionality_enabled' => 1,
    ],
],
'swissup_core' => [
    'notification' => [
        'enabled' => 0,
    ],
],
'emailcatcher' => [
    'general' => [
        'enabled' => 1,
    ],
],
        ],
        'website' => [
            'mailboxshop24_za' => [
                'carriers' => [
                    'collivery' => [
                        'active'    => 1,
                        'name'      => 'MDS Collivery',
                        'title'     => 'MDS Collivery',
                        'user_name' => '<EMAIL>',
                        'password'  => 'Takealot9876',
                        'markup'    => 0,
                    ],
                ],
                'payment'  => [
                    'payfast'                       => [
                        'enabled'      => 1,
                        'title'        => 'PayFast: Credit Card/ Debit Card/ instant EFT',
                        'server'       => 'live',
                        'merchant_id'  => '11852336',
                        'merchant_key' => '794c7mruw8oyn',
                        'passphrase'   => '2fYK9x9MPPRq4BU5Uk23Pb3BfM7vCYVC',
                    ],
                    'squareup_subscription_payment' => [
                        'active' => 0,
                    ],
                ],
            ],
            'rottner_za'       => [
                'carriers' => [
                    'collivery' => [
                        'active'    => 1,
                        'name'      => 'MDS Collivery',
                        'title'     => 'MDS Collivery',
                        'user_name' => '<EMAIL>',
                        'password'  => 'Takealot9876',
                        'markup'    => 0,
                    ],
                ],
                'dev'      => [
                    'js' => [
                        'enable_magepack_js_bundling' => 0,
                    ],
                ],
                'payment'  => [
                    'payfast' => [
                        'enabled'      => 1,
                        'title'        => 'PayFast: Credit Card/ Debit Card/ instant EFT',
                        'server'       => 'live',
                        'merchant_id'  => '11852336',
                        'merchant_key' => '794c7mruw8oyn',
                        'passphrase'   => '2fYK9x9MPPRq4BU5Uk23Pb3BfM7vCYVC',
                    ],
                ],
            ],
        ],
    ],
    'http_cache_hosts' => [
        [
            'host' => 'localhost',
            'port' => '8080',
        ],
    ],
    'queue'            => [
        'consumers_wait_for_messages' => 0,
    ],
];
