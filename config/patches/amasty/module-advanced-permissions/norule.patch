Index: Observer/Admin/Category/RestrictEditObserver.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Observer/Admin/Category/RestrictEditObserver.php b/Observer/Admin/Category/RestrictEditObserver.php
--- a/Observer/Admin/Category/RestrictEditObserver.php	
+++ b/Observer/Admin/Category/RestrictEditObserver.php	(date 1666840397427)
@@ -39,7 +39,7 @@
     {
         if ($id = $observer->getRequest()->getParam('id')) {
             $rule = $this->helper->currentRule();
-
+            if(!$rule) return;
             if ($rule->getCategories() && !in_array($id, $rule->getCategories())) {
                 $this->helper->redirectHome();
             }
Index: Observer/Admin/Category/EditPredispatchObserver.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Observer/Admin/Category/EditPredispatchObserver.php b/Observer/Admin/Category/EditPredispatchObserver.php
--- a/Observer/Admin/Category/EditPredispatchObserver.php	
+++ b/Observer/Admin/Category/EditPredispatchObserver.php	(date 1666840322459)
@@ -23,6 +23,7 @@
     public function execute(\Magento\Framework\Event\Observer $observer)
     {
         $rule = $this->helper->currentRule();
+        if(!$rule) return;
         $catRestrictions = $rule->getCategories();
 
         if (!$catRestrictions) {
