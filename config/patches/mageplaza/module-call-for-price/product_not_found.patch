Index: Helper/Rule.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/Helper/Rule.php b/Helper/Rule.php
--- a/Helper/Rule.php	
+++ b/Helper/Rule.php	(date 1682568696000)
@@ -260,6 +260,7 @@
         $this->mpCallForPriceRule = false;

         $product            = $this->_productFactory->create()->load($productById);
+        if(!$product || !$product->getId()) return false;
         $attributeCfpValues = $product->getCustomAttribute('mp_callforprice');
         $attributeCfpValues = $attributeCfpValues ? $attributeCfpValues->getValue() :
             AttributeOptions::ATTRIBUTE_PARENT_CATEGORY;
