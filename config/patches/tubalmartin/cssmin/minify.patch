Index: src/Minifier.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/Minifier.php b/src/Minifier.php
--- a/src/Minifier.php
+++ b/src/Minifier.php	(date 1747646865230)
@@ -298,44 +298,44 @@
         $css = $this->processDataUrls($css);
 
         // Process comments
-        $css = preg_replace_callback(
+        $newCss = preg_replace_callback(
             '/(?<!\\\\)\/\*(.*?)\*(?<!\\\\)\//Ss',
             array($this, 'processCommentsCallback'),
-            $css
+            (string)$css
         );
-
+        if(!is_null($newCss)) $css = $newCss;
         // IE7: Process Microsoft matrix filters (whitespaces between Matrix parameters). Can contain strings inside.
-        $css = preg_replace_callback(
+        $newCss = preg_replace_callback(
             '/filter:\s*progid:DXImageTransform\.Microsoft\.Matrix\(([^)]+)\)/Ss',
             array($this, 'processOldIeSpecificMatrixDefinitionCallback'),
-            $css
+            (string)$css
         );
-
+        if(!is_null($newCss)) $css = $newCss;
         // Process quoted unquotable attribute selectors to unquote them. Covers most common cases.
         // Likelyhood of a quoted attribute selector being a substring in a string: Very very low.
-        $css = preg_replace(
+        $newCss = preg_replace(
             '/\[\s*([a-z][a-z-]+)\s*([\*\|\^\$~]?=)\s*[\'"](-?[a-z_][a-z0-9-_]+)[\'"]\s*\]/Ssi',
             '[$1$2$3]',
-            $css
+            (string)$css
         );
-
+        if(!is_null($newCss)) $css = $newCss;
         // Process strings so their content doesn't get accidentally minified
-        $css = preg_replace_callback(
+        $newCss = preg_replace_callback(
             '/(?:"(?:[^\\\\"]|\\\\.|\\\\)*")|'."(?:'(?:[^\\\\']|\\\\.|\\\\)*')/S",
             array($this, 'processStringsCallback'),
-            $css
+            (string)$css
         );
-
+        if(!is_null($newCss)) $css = $newCss;
         // Normalize all whitespace strings to single spaces. Easier to work with that way.
-        $css = preg_replace('/\s+/S', ' ', $css);
-
+        $newCss = preg_replace('/\s+/S', ' ', (string)$css);
+        if(!is_null($newCss)) $css = $newCss;
         // Process import At-rules with unquoted URLs so URI reserved characters such as a semicolon may be used safely.
-        $css = preg_replace_callback(
+        $newCss = preg_replace_callback(
             '/@import url\(([^\'"]+?)\)( |;)/Si',
             array($this, 'processImportUnquotedUrlAtRulesCallback'),
-            $css
+            (string)$css
         );
-        
+        if(!is_null($newCss)) $css = $newCss;
         // Process comments
         $css = $this->processComments($css);
         
